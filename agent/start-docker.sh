#!/bin/bash

# Start Agent Memory Service with Docker

echo "🚀 Starting Agent Memory Service with Docker..."

# Check if .env.docker exists
if [ ! -f ".env.docker" ]; then
    echo "❌ .env.docker file not found!"
    echo "Please make sure .env.docker exists with proper configuration."
    exit 1
fi

# Check if Supabase is running on host
echo "🔍 Checking if Supabase is running on host..."
if ! curl -s http://localhost:54321/health > /dev/null; then
    echo "❌ Supabase is not running on localhost:54321"
    echo "Please start Supabase first using: supabase start"
    exit 1
fi

echo "✅ Supabase is running"

# Build and start services
echo "🏗️  Building and starting Docker services..."
docker-compose up --build

echo "🎉 Agent Memory Service is starting!"
echo "📝 Service will be available at: http://localhost:8010"
echo "📊 Ollama will be available at: http://localhost:11434"
echo ""
echo "To stop the services, press Ctrl+C or run: docker-compose down"
