#!/usr/bin/env python3
"""
Test script for memory analysis and inspiration generation.
"""

import asyncio
import os
from mem0 import Memory
from src.analyzer import <PERSON><PERSON><PERSON>yzer
from src.inspiration import InspirationAgent

async def test_memory_analysis():
    """Test the memory analysis functionality."""
    print("🧪 Testing Memory Analysis and Inspiration Generation")
    print("=" * 60)
    
    # Configuration
    embedding_model = "nomic-embed-text:latest"
    embedding_dims = 768
    
    config = {
        "vector_store": {
            "provider": "supabase",
            "config": {
                "collection_name": "memories",
                "connection_string": "postgresql://postgres:postgres@localhost:54322/postgres",
                "index_method": "hnsw",
                "index_measure": "cosine_distance",
                "embedding_model_dims": embedding_dims,
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "temperature": 0,
                "max_tokens": 2000,
                "ollama_base_url": "http://localhost:11434",
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": embedding_model,
                "ollama_base_url": "http://localhost:11434",
            }
        }
    }
    
    # Initialize memory client
    print("🔧 Initializing Memory client...")
    memory = Memory.from_config(config)
    print("✅ Memory client initialized")
    
    # Test user
    user_id = "test_analysis_user"
    
    # Clean up first
    try:
        memory.delete_all(user_id=user_id)
        print("🧹 Cleaned up existing memories")
    except:
        pass
    
    # Add some test memories
    print("\n📝 Adding test memories...")
    test_memories = [
        {
            "messages": [{"role": "user", "content": "我喜欢在北京的故宫博物院参观中国古代文物"}],
            "metadata": {"category": "interests", "location": "beijing"}
        },
        {
            "messages": [{"role": "user", "content": "我对机器学习和人工智能很感兴趣，特别是自然语言处理"}],
            "metadata": {"category": "interests", "topic": "ai"}
        },
        {
            "messages": [{"role": "user", "content": "我喜欢阅读科幻小说，特别是刘慈欣的作品"}],
            "metadata": {"category": "preferences", "topic": "reading"}
        },
        {
            "messages": [{"role": "user", "content": "我在学习Python编程，希望能够开发一些有趣的项目"}],
            "metadata": {"category": "interests", "topic": "programming"}
        },
        {
            "messages": [{"role": "user", "content": "我喜欢旅游，想去上海看看外滩和东方明珠"}],
            "metadata": {"category": "preferences", "location": "shanghai"}
        }
    ]
    
    for i, memory_data in enumerate(test_memories):
        try:
            result = memory.add(
                memory_data["messages"],
                user_id=user_id,
                metadata=memory_data["metadata"]
            )
            print(f"✅ Added memory {i+1}: {memory_data['messages'][0]['content'][:50]}...")
        except Exception as e:
            print(f"❌ Failed to add memory {i+1}: {str(e)}")
    
    print(f"\n📊 Added {len(test_memories)} test memories")
    
    # Test Memory Analyzer
    print("\n🔍 Testing Memory Analyzer...")
    analyzer = MemoryAnalyzer(memory)
    
    try:
        insights = await analyzer.analyze_user_memories(user_id)
        print(f"✅ Generated {len(insights)} insights")
        
        for i, insight in enumerate(insights):
            print(f"\n📋 Insight {i+1}:")
            print(f"   Type: {insight['type']}")
            print(f"   Content: {insight['content']}")
            print(f"   Confidence: {insight['confidence']}")
            print(f"   Related memories: {len(insight.get('related_memories', []))}")
            
    except Exception as e:
        print(f"❌ Memory analysis failed: {str(e)}")
    
    # Test Inspiration Agent
    print("\n💡 Testing Inspiration Agent...")
    inspiration_agent = InspirationAgent(memory)
    
    # Test different contexts
    test_contexts = [
        None,  # No context
        {"topic": "人工智能", "activity": "学习"},
        {"topic": "旅游", "activity": "计划"},
        {"activity": "编程"},
        {"topic": "文化"}
    ]
    
    for i, context in enumerate(test_contexts):
        try:
            print(f"\n🎯 Test {i+1}: Context = {context}")
            inspiration = await inspiration_agent.generate_inspiration(user_id, context)
            
            print(f"   Type: {inspiration['type']}")
            print(f"   Content: {inspiration['content']}")
            print(f"   Confidence: {inspiration['confidence']}")
            print(f"   Animation: {inspiration['animation_state']}")
            print(f"   Related memories: {len(inspiration.get('related_memories', []))}")
            
        except Exception as e:
            print(f"❌ Inspiration generation failed for context {context}: {str(e)}")
    
    # Clean up
    try:
        memory.delete_all(user_id=user_id)
        print("\n🧹 Final cleanup completed")
    except:
        pass
    
    print("\n🎉 Memory analysis and inspiration testing completed!")

if __name__ == "__main__":
    asyncio.run(test_memory_analysis())
