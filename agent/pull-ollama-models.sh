#!/bin/bash

# Script to pull required Ollama models

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Pulling required Ollama models...${NC}"

# Wait for Ollama service to be ready
echo -e "${YELLOW}Waiting for Ollama service to be ready...${NC}"
until $(curl --output /dev/null --silent --fail http://localhost:11434/api/tags); do
    printf '.'
    sleep 5
done

echo -e "\n${GREEN}Ollama service is ready!${NC}"

# Pull LLM model
echo -e "${YELLOW}Pulling LLM model (qwen3:4b)...${NC}"
curl -X POST http://localhost:11434/api/pull -d '{"name": "qwen3:4b"}'

# Wait for the model to be pulled
echo -e "${YELLOW}Waiting for qwen3:4b model to be pulled...${NC}"
until $(curl --output /dev/null --silent --fail http://localhost:11434/api/tags | grep -q "qwen3:4b"); do
    printf '.'
    sleep 5
done

echo -e "\n${GREEN}qwen3:4b model pulled successfully!${NC}"

# Pull embedding model
echo -e "${YELLOW}Pulling embedding model (snowflake-arctic-embed2)...${NC}"
curl -X POST http://localhost:11434/api/pull -d '{"name": "snowflake-arctic-embed2"}'

# Wait for the model to be pulled
echo -e "${YELLOW}Waiting for snowflake-arctic-embed2 model to be pulled...${NC}"
until $(curl --output /dev/null --silent --fail http://localhost:11434/api/tags | grep -q "snowflake-arctic-embed2"); do
    printf '.'
    sleep 5
done

echo -e "\n${GREEN}snowflake-arctic-embed2 model pulled successfully!${NC}"

echo -e "${GREEN}All required Ollama models have been pulled successfully!${NC}"
