#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running Agent Memory test in Docker environment...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if the Docker Compose services are running
if ! docker-compose ps | grep -q "agent-memory"; then
    echo -e "${YELLOW}Agent Memory service is not running. Starting Docker Compose services...${NC}"
    docker-compose up -d
    
    # Wait for services to be ready
    echo -e "${YELLOW}Waiting for services to be ready...${NC}"
    sleep 10
fi

# Copy the test script to the agent-memory container
echo -e "${YELLOW}Copying test script to the agent-memory container...${NC}"
docker cp test_memory_docker.py agent-memory:/app/test_memory.py

# Run the test script in the agent-memory container
echo -e "${YELLOW}Running test script in the agent-memory container...${NC}"
docker exec agent-memory python /app/test_memory.py

echo -e "${GREEN}Test completed!${NC}"
