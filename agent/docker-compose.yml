version: '3.8'

services:
  agent-memory:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8010:8010"
    environment:
      - PORT=8010
      - HOST=0.0.0.0
      - LOG_LEVEL=INFO
      - MEM0_USE_LOCAL=true
      # Supabase configuration
      - SUPABASE_URL=http://host.docker.internal:54321
      - SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      - SUPABASE_PGVECTOR_TABLE=memories
      - SUPABASE_PGVECTOR_SCHEMA=public
      - SUPABASE_DB_STRING=postgresql://postgres:<EMAIL>:54322/postgres
      # Ollama configuration for LLM
      - LLM_PROVIDER=ollama
      - LLM_MODEL=qwen3:4b
      - OLLAMA_BASE_URL=http://ollama:11434
      # Analysis settings
      - ANALYSIS_INTERVAL_MINUTES=60
      - ANALYSIS_ENABLED=true
      # Embedder configuration
      - EMBEDDER_PROVIDER=ollama
      - EMBEDDER_MODEL=snowflake-arctic-embed2
    volumes:
      - ./src:/app/src
    depends_on:
      ollama-setup:
        condition: service_completed_successfully
    networks:
      - agent-network
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ${HOME}/.ollama:/root/.ollama
    networks:
      - agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: serve

  ollama-setup:
    image: curlimages/curl:latest
    depends_on:
      ollama:
        condition: service_healthy
    networks:
      - agent-network
    restart: "no"
    command: >
      sh -c "
        echo 'Pulling qwen3:4b model...' &&
        curl -X POST http://ollama:11434/api/pull -d '{\"name\": \"qwen3:4b\"}' &&
        echo 'Pulling snowflake-arctic-embed2 model...' &&
        curl -X POST http://ollama:11434/api/pull -d '{\"name\": \"snowflake-arctic-embed2\"}' &&
        echo 'All models pulled successfully!'
      "

networks:
  agent-network:
    driver: bridge
