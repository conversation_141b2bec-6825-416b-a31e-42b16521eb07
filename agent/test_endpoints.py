#!/usr/bin/env python3
"""
Test script for Agent Memory API endpoints.

This script tests all the API endpoints to ensure they work correctly.
"""

import requests
import json
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8011"
TEST_USER_ID = "test_user_endpoints"

def print_separator(title: str):
    """Print a separator with a title."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def print_response(response: requests.Response, description: str):
    """Print response details."""
    print(f"📡 {description}")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    print()

def test_health_check():
    """Test the health check endpoint."""
    print_separator("Test 1: Health Check")

    try:
        response = requests.get(f"{BASE_URL}/")
        print_response(response, "GET /")

        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print("❌ Health check failed")

    except Exception as e:
        print(f"❌ Health check error: {str(e)}")

def test_add_memory():
    """Test adding a memory."""
    print_separator("Test 2: Add Memory")

    payload = {
        "messages": [
            {"role": "user", "content": "我喜欢在北京的故宫博物院参观中国古代文物。"},
            {"role": "assistant", "content": "故宫博物院确实是一个了解中国古代文化的绝佳地方。您对哪个朝代的文物最感兴趣？"}
        ],
        "user_id": TEST_USER_ID,
        "metadata": {"category": "travel", "location": "beijing"}
    }

    try:
        response = requests.post(
            f"{BASE_URL}/memories",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print_response(response, "POST /memories")

        if response.status_code == 200:
            print("✅ Add memory passed")
            return response.json().get("id")
        else:
            print("❌ Add memory failed")
            return None

    except Exception as e:
        print(f"❌ Add memory error: {str(e)}")
        return None

def test_get_all_memories():
    """Test getting all memories for a user."""
    print_separator("Test 3: Get All Memories")

    try:
        response = requests.get(f"{BASE_URL}/memories/{TEST_USER_ID}")
        print_response(response, f"GET /memories/{TEST_USER_ID}")

        if response.status_code == 200:
            print("✅ Get all memories passed")
            return response.json().get("memories", [])
        else:
            print("❌ Get all memories failed")
            return []

    except Exception as e:
        print(f"❌ Get all memories error: {str(e)}")
        return []

def test_search_memories():
    """Test searching memories."""
    print_separator("Test 4: Search Memories")

    payload = {
        "query": "北京有什么著名的博物馆？",
        "user_id": TEST_USER_ID,
        "limit": 5
    }

    try:
        response = requests.post(
            f"{BASE_URL}/memories/search",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print_response(response, "POST /memories/search")

        if response.status_code == 200:
            print("✅ Search memories passed")
            return response.json().get("memories", [])
        else:
            print("❌ Search memories failed")
            return []

    except Exception as e:
        print(f"❌ Search memories error: {str(e)}")
        return []

def test_update_memory(memory_id: str):
    """Test updating a memory."""
    print_separator("Test 5: Update Memory")

    if not memory_id:
        print("⚠️ No memory ID provided, skipping update test")
        return

    payload = {
        "memory_id": memory_id,
        "data": "我特别喜欢在北京的故宫博物院和国家博物馆参观中国古代文物和艺术品。"
    }

    try:
        response = requests.put(
            f"{BASE_URL}/memories",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print_response(response, "PUT /memories")

        if response.status_code == 200:
            print("✅ Update memory passed")
        else:
            print("❌ Update memory failed")

    except Exception as e:
        print(f"❌ Update memory error: {str(e)}")

def test_delete_memory(memory_id: str):
    """Test deleting a specific memory."""
    print_separator("Test 6: Delete Memory")

    if not memory_id:
        print("⚠️ No memory ID provided, skipping delete test")
        return

    try:
        response = requests.delete(f"{BASE_URL}/memories/{memory_id}")
        print_response(response, f"DELETE /memories/{memory_id}")

        if response.status_code == 200:
            print("✅ Delete memory passed")
        else:
            print("❌ Delete memory failed")

    except Exception as e:
        print(f"❌ Delete memory error: {str(e)}")

def test_delete_all_user_memories():
    """Test deleting all memories for a user."""
    print_separator("Test 7: Delete All User Memories")

    try:
        response = requests.delete(f"{BASE_URL}/memories/user/{TEST_USER_ID}")
        print_response(response, f"DELETE /memories/user/{TEST_USER_ID}")

        if response.status_code == 200:
            print("✅ Delete all user memories passed")
        else:
            print("❌ Delete all user memories failed")

    except Exception as e:
        print(f"❌ Delete all user memories error: {str(e)}")

def main():
    """Run all endpoint tests."""
    print_separator("Agent Memory API Endpoint Tests")

    # Test 1: Health check
    test_health_check()

    # Test 2: Add memory
    memory_id = test_add_memory()

    # Wait a bit for memory to be processed
    time.sleep(2)

    # Test 3: Get all memories
    memories = test_get_all_memories()

    # Test 4: Search memories
    search_results = test_search_memories()

    # Test 5: Update memory (if we have a memory ID)
    if memory_id:
        test_update_memory(memory_id)
    elif memories:
        # Use the first memory from get_all if add_memory didn't return an ID
        first_memory = memories[0]
        if isinstance(first_memory, dict) and 'id' in first_memory:
            test_update_memory(first_memory['id'])

    # Test 6: Delete specific memory (if we have a memory ID)
    if memory_id:
        test_delete_memory(memory_id)
    elif memories:
        # Use the first memory from get_all if add_memory didn't return an ID
        first_memory = memories[0]
        if isinstance(first_memory, dict) and 'id' in first_memory:
            test_delete_memory(first_memory['id'])

    # Test 7: Delete all user memories
    test_delete_all_user_memories()

    print_separator("All tests completed")

if __name__ == "__main__":
    main()
