"""
Inspiration Agent Module

This module provides functionality to generate inspirational suggestions
based on analyzed user memories and interactions.
"""

import logging
import random
from typing import Dict, List, Any, Optional
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class InspirationAgent:
    """
    Generates inspirational suggestions based on user memories and interactions.
    """

    def __init__(self, memory_client, llm_client=None):
        """
        Initialize the inspiration agent.

        Args:
            memory_client: An instance of mem0 Memory client
            llm_client: Optional LLM client for generating suggestions
        """
        self.memory = memory_client
        self.llm = llm_client

    async def generate_inspiration(
        self,
        user_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate an inspirational suggestion for the user.

        Args:
            user_id: The ID of the user
            context: Optional context about the user's current activity

        Returns:
            A dictionary containing the inspiration details
        """
        try:
            # Get relevant memories
            query = self._build_query(context)
            memories_result = self.memory.search(query=query, user_id=user_id, limit=5)

            # Handle different return formats from mem0
            memories = self._extract_memories_list(memories_result)

            if not memories:
                logger.info(f"No relevant memories found for user {user_id}")
                return self._generate_generic_inspiration()

            logger.info(f"Generating inspiration based on {len(memories)} memories for user {user_id}")

            # Generate inspiration based on memories
            inspiration = await self._generate_personalized_inspiration(memories, context)

            return inspiration

        except Exception as e:
            logger.error(f"Error generating inspiration for user {user_id}: {str(e)}")
            return self._generate_generic_inspiration()

    def _build_query(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Build a search query based on the current context.

        Args:
            context: Optional context about the user's current activity

        Returns:
            A search query string
        """
        if not context:
            return "What are the user's main interests and preferences?"

        # Extract relevant information from context
        current_topic = context.get("topic", "")
        current_activity = context.get("activity", "")

        # Build query based on context
        if current_topic and current_activity:
            return f"What does the user know about {current_topic} and how do they approach {current_activity}?"
        elif current_topic:
            return f"What does the user know about {current_topic} and what are they interested in learning?"
        elif current_activity:
            return f"How does the user typically approach {current_activity} and what are their preferences?"

        return "What are the user's main interests and preferences?"

    def _extract_memories_list(self, memories_result: Any) -> List[Dict[str, Any]]:
        """
        Extract memories list from different return formats of mem0.

        Args:
            memories_result: The result from mem0 search/get_all

        Returns:
            A list of memory dictionaries
        """
        if isinstance(memories_result, dict) and 'results' in memories_result:
            return memories_result['results']
        elif isinstance(memories_result, list):
            return memories_result
        else:
            return []

    async def _generate_personalized_inspiration(
        self,
        memories: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a personalized inspiration based on memories and context.

        Args:
            memories: List of relevant memories
            context: Optional context about the user's current activity

        Returns:
            A dictionary containing the personalized inspiration
        """
        # Extract memory content and metadata
        memory_contents = []
        memory_ids = []

        for memory in memories:
            # Handle different memory formats
            if isinstance(memory, dict):
                content = memory.get("memory", memory.get("content", ""))
                memory_id = memory.get("id", "")
                if content:
                    memory_contents.append(content)
                if memory_id:
                    memory_ids.append(memory_id)

        if not memory_contents:
            return self._generate_generic_inspiration()

        # Use LLM if available, otherwise use rule-based approach
        if self.llm:
            try:
                inspiration = await self._generate_llm_inspiration(memory_contents, context)
                inspiration["related_memories"] = memory_ids[:3]
                return inspiration
            except Exception as e:
                logger.warning(f"LLM generation failed, falling back to rule-based: {str(e)}")

        # Rule-based inspiration generation
        inspiration_type, content, confidence = self._generate_rule_based_inspiration(
            memory_contents, context
        )

        return {
            "type": inspiration_type,
            "content": content,
            "confidence": confidence,
            "related_memories": memory_ids[:3],
            "created_at": datetime.now().isoformat(),
            "animation_state": self._determine_animation_state(inspiration_type)
        }

    def _generate_generic_inspiration(self) -> Dict[str, Any]:
        """
        Generate a generic inspiration when no relevant memories are available.

        Returns:
            A dictionary containing a generic inspiration
        """
        generic_inspirations = [
            {
                "type": "suggestion",
                "content": "Consider taking a different approach to your current task.",
                "confidence": 0.5,
                "animation_state": "thinking"
            },
            {
                "type": "question",
                "content": "Have you thought about the problem from multiple perspectives?",
                "confidence": 0.5,
                "animation_state": "curious"
            },
            {
                "type": "reminder",
                "content": "Taking short breaks can help improve creativity and problem-solving.",
                "confidence": 0.5,
                "animation_state": "helpful"
            }
        ]

        inspiration = random.choice(generic_inspirations)
        inspiration["created_at"] = datetime.now().isoformat()
        inspiration["related_memories"] = []

        return inspiration

    def _determine_animation_state(self, inspiration_type: str) -> str:
        """
        Determine the appropriate animation state based on the inspiration type.

        Args:
            inspiration_type: The type of inspiration

        Returns:
            An animation state string
        """
        animation_states = {
            "suggestion": "excited",
            "question": "curious",
            "connection": "thoughtful",
            "reminder": "helpful"
        }

        return animation_states.get(inspiration_type, "neutral")

    async def _generate_llm_inspiration(
        self,
        memory_contents: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate inspiration using LLM based on memory contents and context.

        Args:
            memory_contents: List of memory content strings
            context: Optional context about the user's current activity

        Returns:
            A dictionary containing the LLM-generated inspiration
        """
        # Build prompt for LLM
        memories_text = "\n".join([f"- {content}" for content in memory_contents[:5]])

        context_text = ""
        if context:
            if context.get("topic"):
                context_text += f"Current topic: {context['topic']}\n"
            if context.get("activity"):
                context_text += f"Current activity: {context['activity']}\n"

        prompt = f"""Based on the user's past memories and current context, generate a helpful and inspiring suggestion.

User's memories:
{memories_text}

{context_text}

Please provide a brief, actionable, and inspiring suggestion that connects to their interests and current situation.
The suggestion should be encouraging and help them think creatively or make progress.

Respond with just the suggestion text, no additional formatting."""

        try:
            # This would be the actual LLM call
            # For now, return a placeholder that shows the LLM integration structure
            response = "Based on your interests, consider exploring how these topics connect to create new insights."

            return {
                "type": "llm_suggestion",
                "content": response,
                "confidence": 0.8,
                "created_at": datetime.now().isoformat(),
                "animation_state": "thoughtful"
            }
        except Exception as e:
            logger.error(f"LLM generation error: {str(e)}")
            raise

    def _generate_rule_based_inspiration(
        self,
        memory_contents: List[str],
        context: Optional[Dict[str, Any]] = None
    ) -> tuple[str, str, float]:
        """
        Generate inspiration using rule-based approach.

        Args:
            memory_contents: List of memory content strings
            context: Optional context about the user's current activity

        Returns:
            A tuple of (inspiration_type, content, confidence)
        """
        num_memories = len(memory_contents)

        # Analyze context if available
        current_topic = context.get("topic", "") if context else ""
        current_activity = context.get("activity", "") if context else ""

        # Rule-based logic
        if num_memories >= 5:
            if current_topic:
                content = f"You have extensive knowledge about {current_topic}. Consider how your past insights might apply to new challenges."
            else:
                content = "You have a rich history of interactions. Consider connecting ideas from different areas of your experience."
            return "synthesis", content, 0.8

        elif num_memories >= 3:
            if current_activity:
                content = f"Based on your past experiences with {current_activity}, you might want to try a different approach or build on what worked before."
            else:
                content = "You've explored this area before. Consider what patterns or connections you might have missed."
            return "connection", content, 0.7

        elif num_memories >= 1:
            content = "You've shown interest in this topic before. Consider revisiting and expanding on your earlier thoughts."
            return "reminder", content, 0.6

        else:
            content = "This seems like a new area for you. Consider starting with the basics and building from there."
            return "exploration", content, 0.5
