"""
Memory Analyzer Module

This module provides functionality to analyze user memories and generate insights.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class MemoryAnalyzer:
    """
    Analyzes user memories to extract patterns and generate insights.
    """

    def __init__(self, memory_client):
        """
        Initialize the memory analyzer with a memory client.

        Args:
            memory_client: An instance of mem0 Memory client
        """
        self.memory = memory_client

    async def analyze_user_memories(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Analyze memories for a specific user and generate insights.

        Args:
            user_id: The ID of the user whose memories to analyze

        Returns:
            A list of insights generated from the user's memories
        """
        try:
            # Retrieve all memories for the user
            memories_result = self.memory.get_all(user_id=user_id)

            # Handle different return formats from mem0
            memories = self._extract_memories_list(memories_result)

            if not memories:
                logger.info(f"No memories found for user {user_id}")
                return []

            logger.info(f"Analyzing {len(memories)} memories for user {user_id}")

            # Group memories by type/category
            categorized_memories = self._categorize_memories(memories)

            # Generate insights based on categorized memories
            insights = []

            # Process preferences
            if "preferences" in categorized_memories:
                preference_insights = self._analyze_preferences(categorized_memories["preferences"])
                insights.extend(preference_insights)

            # Process interests
            if "interests" in categorized_memories:
                interest_insights = self._analyze_interests(categorized_memories["interests"])
                insights.extend(interest_insights)

            # Process temporal patterns
            temporal_insights = self._analyze_temporal_patterns(memories)
            insights.extend(temporal_insights)

            # Process connections between topics
            connection_insights = self._analyze_connections(memories)
            insights.extend(connection_insights)

            logger.info(f"Generated {len(insights)} insights for user {user_id}")
            return insights

        except Exception as e:
            logger.error(f"Error analyzing memories for user {user_id}: {str(e)}")
            return []

    def _extract_memories_list(self, memories_result: Any) -> List[Dict[str, Any]]:
        """
        Extract memories list from different return formats of mem0.

        Args:
            memories_result: The result from mem0 get_all

        Returns:
            A list of memory dictionaries
        """
        if isinstance(memories_result, dict) and 'results' in memories_result:
            return memories_result['results']
        elif isinstance(memories_result, list):
            return memories_result
        else:
            return []

    def _categorize_memories(self, memories: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Categorize memories based on their content and metadata.

        Args:
            memories: List of memory objects

        Returns:
            Dictionary mapping categories to lists of memories
        """
        categories = {}

        for memory in memories:
            # Handle different memory formats
            if isinstance(memory, dict):
                # Extract category from metadata if available
                metadata = memory.get("metadata", {})
                if isinstance(metadata, dict):
                    category = metadata.get("category", "uncategorized")
                else:
                    category = "uncategorized"
            else:
                category = "uncategorized"

            if category not in categories:
                categories[category] = []

            categories[category].append(memory)

        return categories

    def _analyze_preferences(self, preference_memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze preference-related memories to extract user preferences.

        Args:
            preference_memories: List of preference-related memories

        Returns:
            List of preference insights
        """
        insights = []

        if preference_memories:
            # Extract memory IDs safely
            memory_ids = []
            for memory in preference_memories[:3]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "preference",
                "content": "User has expressed preferences that could be relevant to their current task",
                "confidence": 0.8,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_interests(self, interest_memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze interest-related memories to identify user interests.

        Args:
            interest_memories: List of interest-related memories

        Returns:
            List of interest insights
        """
        insights = []

        if interest_memories:
            # Extract memory IDs safely
            memory_ids = []
            for memory in interest_memories[:3]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "interest",
                "content": "Based on past interactions, the user might be interested in exploring related topics",
                "confidence": 0.7,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_temporal_patterns(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze temporal patterns in user memories.

        Args:
            memories: List of all user memories

        Returns:
            List of temporal pattern insights
        """
        insights = []

        # Example: Check if user has been consistently active
        if len(memories) > 5:
            # Extract memory IDs safely
            memory_ids = []
            for memory in memories[-5:]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "activity",
                "content": "User has been consistently engaging with the system",
                "confidence": 0.6,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights

    def _analyze_connections(self, memories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyze connections between different topics in user memories.

        Args:
            memories: List of all user memories

        Returns:
            List of connection insights
        """
        insights = []

        if len(memories) > 3:
            # Extract memory IDs safely
            memory_ids = []
            for memory in memories[-3:]:
                if isinstance(memory, dict) and "id" in memory:
                    memory_ids.append(memory["id"])

            insights.append({
                "type": "connection",
                "content": "There might be connections between the user's recent topics of interest",
                "confidence": 0.5,
                "related_memories": memory_ids,
                "created_at": datetime.now().isoformat()
            })

        return insights
