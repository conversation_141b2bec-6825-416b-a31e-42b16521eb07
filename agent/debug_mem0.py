#!/usr/bin/env python3
"""
Debug script to understand mem0 API return formats.
"""

import os
import json
from mem0 import Memory

def print_json(data, title="Data"):
    """Print data as formatted JSON."""
    print(f"\n{title}:")
    print(f"Type: {type(data)}")
    print(f"Content: {json.dumps(data, indent=2, ensure_ascii=False, default=str)}")

def main():
    # Configuration
    embedding_model = "nomic-embed-text:latest"
    embedding_dims = 768
    
    config = {
        "vector_store": {
            "provider": "supabase",
            "config": {
                "collection_name": "memories",
                "connection_string": "postgresql://postgres:postgres@localhost:54322/postgres",
                "index_method": "hnsw",
                "index_measure": "cosine_distance",
                "embedding_model_dims": embedding_dims,
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:4b",
                "temperature": 0,
                "max_tokens": 2000,
                "ollama_base_url": "http://localhost:11434",
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": embedding_model,
                "ollama_base_url": "http://localhost:11434",
            }
        }
    }
    
    print("Initializing Memory...")
    m = Memory.from_config(config)
    print("✅ Memory initialized")
    
    # Test user
    user_id = "debug_user"
    
    # Clean up first
    try:
        m.delete_all(user_id=user_id)
        print("🧹 Cleaned up existing memories")
    except:
        pass
    
    # Test 1: Add memory
    print("\n" + "="*50)
    print("TEST 1: Adding memory")
    messages = [{"role": "user", "content": "I love debugging code"}]
    add_result = m.add(messages, user_id=user_id)
    print_json(add_result, "Add result")
    
    # Test 2: Get all memories
    print("\n" + "="*50)
    print("TEST 2: Getting all memories")
    get_all_result = m.get_all(user_id=user_id)
    print_json(get_all_result, "Get all result")
    
    # Test 3: Search memories
    print("\n" + "="*50)
    print("TEST 3: Searching memories")
    search_result = m.search("debugging", user_id=user_id)
    print_json(search_result, "Search result")
    
    # Test 4: Update memory (if we have a memory ID)
    memory_id = None
    if isinstance(get_all_result, list) and len(get_all_result) > 0:
        memory_id = get_all_result[0].get('id')
    elif isinstance(get_all_result, dict) and 'results' in get_all_result:
        if len(get_all_result['results']) > 0:
            memory_id = get_all_result['results'][0].get('id')
    
    if memory_id:
        print("\n" + "="*50)
        print("TEST 4: Updating memory")
        print(f"Using memory ID: {memory_id}")
        try:
            update_result = m.update(memory_id=memory_id, data="I love debugging and testing code")
            print_json(update_result, "Update result")
        except Exception as e:
            print(f"Update error: {e}")
        
        print("\n" + "="*50)
        print("TEST 5: Deleting memory")
        try:
            delete_result = m.delete(memory_id=memory_id)
            print_json(delete_result, "Delete result")
        except Exception as e:
            print(f"Delete error: {e}")
    else:
        print("\n⚠️ No memory ID found, skipping update/delete tests")
    
    # Clean up
    try:
        m.delete_all(user_id=user_id)
        print("\n🧹 Final cleanup completed")
    except:
        pass

if __name__ == "__main__":
    main()
