#!/bin/bash

# Setup script for Agent Memory service using uv

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Setting up Agent Memory service...${NC}"

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo -e "${RED}uv is not installed. Installing uv...${NC}"
    curl -LsSf https://astral.sh/uv/install.sh | sh

    # Add uv to PATH for this session
    export PATH="$HOME/.cargo/bin:$PATH"

    echo -e "${GREEN}uv installed successfully!${NC}"
else
    echo -e "${GREEN}uv is already installed.${NC}"
fi

# Create virtual environment
echo -e "${YELLOW}Creating virtual environment...${NC}"
uv venv

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source .venv/bin/activate

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
uv pip install -e .

# Check if Supabase configuration is needed
if [ "$1" == "--setup-supabase" ]; then
    echo -e "${YELLOW}Setting up Supabase configuration...${NC}"

    # Using Supabase configuration from backend/.env.docker
    supabase_url="http://host.docker.internal:54321"
    supabase_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
    supabase_db_string="postgresql://postgres:<EMAIL>:54322/postgres"
    table_name="memories"
    schema_name="public"

    echo -e "${GREEN}Using Supabase configuration from backend/.env.docker${NC}"

    # Update .env file with Supabase configuration
    if [ -f .env ]; then
        # Replace existing Supabase configuration
        sed -i.bak "s|SUPABASE_URL=.*|SUPABASE_URL=${supabase_url}|g" .env
        sed -i.bak "s|SUPABASE_KEY=.*|SUPABASE_KEY=${supabase_key}|g" .env
        sed -i.bak "s|SUPABASE_PGVECTOR_TABLE=.*|SUPABASE_PGVECTOR_TABLE=${table_name}|g" .env
        sed -i.bak "s|SUPABASE_PGVECTOR_SCHEMA=.*|SUPABASE_PGVECTOR_SCHEMA=${schema_name}|g" .env
        sed -i.bak "s|SUPABASE_DB_STRING=.*|SUPABASE_DB_STRING=${supabase_db_string}|g" .env
        rm -f .env.bak
    else
        # Create new .env file with Supabase configuration
        cp .env.example .env
        sed -i.bak "s|SUPABASE_URL=.*|SUPABASE_URL=${supabase_url}|g" .env
        sed -i.bak "s|SUPABASE_KEY=.*|SUPABASE_KEY=${supabase_key}|g" .env
        sed -i.bak "s|SUPABASE_PGVECTOR_TABLE=.*|SUPABASE_PGVECTOR_TABLE=${table_name}|g" .env
        sed -i.bak "s|SUPABASE_PGVECTOR_SCHEMA=.*|SUPABASE_PGVECTOR_SCHEMA=${schema_name}|g" .env
        sed -i.bak "s|SUPABASE_DB_STRING=.*|SUPABASE_DB_STRING=${supabase_db_string}|g" .env
        rm -f .env.bak
    fi

    echo -e "${GREEN}Supabase configuration updated successfully!${NC}"
    echo -e "${YELLOW}Note: Make sure pgvector extension is enabled in your Supabase project.${NC}"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}.env file created. Please update it with your configuration.${NC}"
else
    echo -e "${GREEN}.env file already exists.${NC}"
fi

# Check if Ollama is installed and pull required models
if [ "$1" == "--setup-ollama" ] || [ "$1" == "--setup-all" ]; then
    echo -e "${YELLOW}Setting up Ollama models...${NC}"

    # Check if Ollama is installed
    if ! command -v ollama &> /dev/null; then
        echo -e "${RED}Ollama is not installed. Please install Ollama first: https://ollama.ai/download${NC}"
        echo -e "${YELLOW}Or you can use Docker to run Ollama with the provided docker-compose.yml${NC}"
    else
        echo -e "${GREEN}Ollama is installed. Pulling required models...${NC}"

        # Pull LLM model
        echo -e "${YELLOW}Pulling LLM model (qwen3:4b)...${NC}"
        ollama pull qwen3:4b

        # Pull embedding model
        echo -e "${YELLOW}Pulling embedding model (snowflake-arctic-embed2)...${NC}"
        ollama pull snowflake-arctic-embed2

        echo -e "${GREEN}Ollama models pulled successfully!${NC}"
    fi
fi

echo -e "${GREEN}Setup completed successfully!${NC}"
echo -e "${YELLOW}To start the service, run:${NC}"
echo -e "${GREEN}source .venv/bin/activate && uvicorn src.main:app --reload --port 8010${NC}"
echo -e "${YELLOW}Or use Docker Compose:${NC}"
echo -e "${GREEN}docker-compose up -d${NC}"
