# Memory Service Configuration for Docker

# Service settings
PORT=8010
HOST=0.0.0.0
LOG_LEVEL=INFO

# mem0 configuration
MEM0_USE_LOCAL=true
MEM0_API_KEY=

# Supabase configuration (Docker environment - connecting to host)
SUPABASE_URL=http://host.docker.internal:54321
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_PGVECTOR_TABLE=memories
SUPABASE_PGVECTOR_SCHEMA=public
SUPABASE_DB_STRING=postgresql://postgres:<EMAIL>:54322/postgres

# LLM configuration (Docker environment - connecting to ollama container)
LLM_PROVIDER=ollama
LLM_MODEL=qwen3:4b
OLLAMA_BASE_URL=http://ollama:11434

# Embedder configuration
EMBEDDER_PROVIDER=ollama
EMBEDDER_MODEL=snowflake-arctic-embed2

# Analysis settings
ANALYSIS_INTERVAL_MINUTES=60
ANALYSIS_ENABLED=true
