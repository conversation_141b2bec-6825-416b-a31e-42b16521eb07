FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh

# Copy project files
COPY pyproject.toml README.md ./
COPY src ./src

# Install dependencies using uv
RUN /root/.cargo/bin/uv pip install -e .

# Copy the .env file (this will be overridden by Docker Compose)
COPY .env.example .env

# Copy entrypoint script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Expose the port
EXPOSE 8010

# Set entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
