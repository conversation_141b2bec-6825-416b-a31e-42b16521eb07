FROM python:3.10-slim

WORKDIR /app

# Set proxy environment variables for faster downloads
ENV https_proxy=http://host.docker.internal:7890
ENV http_proxy=http://host.docker.internal:7890
ENV all_proxy=socks5://host.docker.internal:7890

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
RUN curl -LsSf https://astral.sh/uv/install.sh | sh

# Unset proxy variables after installation (optional, for runtime)
ENV https_proxy=
ENV http_proxy=
ENV all_proxy=

# Copy project files
COPY pyproject.toml README.md ./
COPY src ./src

# Install dependencies using uv
RUN /root/.local/bin/uv pip install --system -e .

# Copy the .env file (this will be overridden by Docker Compose)
COPY .env.example .env

# Copy entrypoint script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Expose the port
EXPOSE 8010

# Set entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
