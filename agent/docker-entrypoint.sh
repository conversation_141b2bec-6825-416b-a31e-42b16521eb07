#!/bin/bash

# Docker entrypoint script for agent-memory service

# Wait for Ollama service to be ready
echo "Waiting for Ollama service to be ready..."
until $(curl --output /dev/null --silent --fail http://ollama:11434/api/tags); do
    printf '.'
    sleep 5
done
echo "Ollama service is ready!"

# Check if required models are available
echo "Checking if required models are available..."
MODELS_READY=false

until $MODELS_READY; do
    TAGS=$(curl -s http://ollama:11434/api/tags)

    if echo "$TAGS" | grep -q "qwen3:4b" && echo "$TAGS" | grep -q "snowflake-arctic-embed2"; then
        MODELS_READY=true
        echo "All required models are available!"
    else
        echo "Waiting for models to be pulled..."
        sleep 10
    fi
done

# Start the application
echo "Starting the application..."
exec uvicorn src.main:app --host 0.0.0.0 --port 8010
