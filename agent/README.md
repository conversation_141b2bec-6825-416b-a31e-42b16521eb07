# 灵感代理 (Inspiration Agent)

基于用户交互记忆的灵感提示系统，通过异步分析用户会话，提供个性化的灵感和建议。

## 快速开始

### 前置条件

1. **Supabase**
   - 已启用 pgvector 扩展
   - 已创建 memories 表和 match_vectors 函数

2. **Ollama**
   - 已安装 Ollama（[下载地址](https://ollama.ai/download)）
   - 需要的模型：qwen3:4b（LLM）和嵌入模型（见下方模型配置）

### Docker 安装（推荐）

```bash
# 克隆仓库（如果尚未克隆）
git clone <repository-url>
cd agent

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 测试服务
chmod +x run_test_docker.sh
./run_test_docker.sh
```

### 本地安装

```bash
# 克隆仓库（如果尚未克隆）
git clone <repository-url>
cd agent

# 安装依赖并配置环境
chmod +x setup.sh
./setup.sh --setup-all

# 启动服务
source .venv/bin/activate
uvicorn src.main:app --reload --port 8010

# 测试服务
chmod +x run_test_local.sh
./run_test_local.sh
```

### API 使用

```python
# 添加记忆
curl -X POST "http://localhost:8010/memories" \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"我喜欢在北京的故宫博物院参观中国古代文物。"}],"user_id":"test_user"}'

# 搜索记忆
curl -X POST "http://localhost:8010/memories/search" \
  -H "Content-Type: application/json" \
  -d '{"query":"北京有什么著名的博物馆？","user_id":"test_user"}'
```

## 功能特点

- **记忆存储与分析**：存储用户交互，区分长短期记忆，提供检索和关联能力
- **异步灵感生成**：后台分析用户历史，识别兴趣模式，提供个性化建议
- **本地模型支持**：使用 Ollama 提供的 qwen3:4b 和 snowflake-arctic-embed2 模型
- **模型同步**：宿主机和容器共享模型文件，避免重复下载
- **Supabase 向量存储**：利用 pgvector 进行高效的语义搜索

## 主要 API 端点

- `POST /memories` - 添加新的记忆
- `GET /memories/{user_id}` - 获取用户的所有记忆
- `POST /memories/search` - 搜索相关记忆
- `PUT /memories` - 更新现有记忆
- `DELETE /memories/{memory_id}` - 删除特定记忆

## 文档

- API 文档：http://localhost:8010/docs
- ReDoc：http://localhost:8010/redoc

## 技术细节

### 系统架构

本项目采用混合架构，将 Memory 功能作为独立服务实现，同时提供与主系统集成的接口。

- **Memory 服务**：使用 FastAPI 实现的独立服务
- **向量存储**：使用 Supabase 的 pgvector 扩展
- **LLM 和嵌入**：使用 Ollama 提供的本地模型

### 数据模型

Memory 数据结构：
```
id: TEXT PRIMARY KEY
embedding: VECTOR(768 或 1024)  # 根据嵌入模型而定
metadata: JSONB
created_at: TIMESTAMP
updated_at: TIMESTAMP
```

### 模型配置

- **LLM**：qwen3:4b
  - 温度：0
  - 最大 token：2000

- **嵌入模型**：支持两种选择
  - **nomic-embed-text:latest**（推荐）
    - 向量维度：768
    - 模型大小：较小，速度快
  - **snowflake-arctic-embed2**
    - 向量维度：1024
    - 模型大小：较大，精度更高

## ⚠️ 重要注意事项

### 嵌入模型维度一致性

**问题描述**：嵌入模型的维度必须与数据库表结构保持一致，否则会出现"Dimensions reported by adapter, dimension, and existing collection do not match"错误。

**解决方案**：
1. **选择嵌入模型**：在 `test_memory.py` 中修改 `embedding_model` 变量
   ```python
   # 选择其中一个
   embedding_model = "nomic-embed-text:latest"      # 768 维度
   embedding_model = "snowflake-arctic-embed2"      # 1024 维度
   ```

2. **清理旧数据**（如果切换模型）：
   ```sql
   -- 删除旧的 vecs schema（包含所有相关表）
   DROP SCHEMA IF EXISTS vecs CASCADE;

   -- 重新创建 schema
   CREATE SCHEMA IF NOT EXISTS vecs;
   ```

3. **验证模型维度**：
   ```bash
   # 检查模型信息
   ollama show nomic-embed-text:latest
   ollama show snowflake-arctic-embed2
   ```

### 数据库表管理

- **自动创建**：mem0 会自动在 `vecs` schema 中创建所需的表
- **表结构**：向量维度会根据配置中的 `embedding_model_dims` 自动设置
- **索引优化**：自动创建 HNSW 索引以提高搜索性能

### 模型下载

确保在使用前下载所需的模型：
```bash
# 下载 LLM 模型
ollama pull qwen3:4b

# 下载嵌入模型（选择其中一个）
ollama pull nomic-embed-text:latest
ollama pull snowflake-arctic-embed2
```

## 测试

### 测试脚本

提供了两个测试脚本：

- `run_test_local.sh`：在本地环境测试
- `run_test_docker.sh`：在 Docker 环境测试

### 手动测试

也可以直接运行测试文件：
```bash
# 本地测试
uv run python test_memory.py

# Docker 环境测试
docker-compose exec agent uv run python test_memory.py
```

### 测试内容

测试包括以下功能：
1. **Memory 初始化**：验证配置和连接
2. **添加简单记忆**：测试基本的记忆存储
3. **检索所有记忆**：获取用户的完整记忆列表
4. **语义搜索**：测试中文查询和语义匹配
5. **对话记忆**：测试复杂对话的记忆提取
6. **相关查询**：验证记忆关联和检索准确性

### 常见测试问题

1. **维度不匹配错误**：
   - 检查嵌入模型配置
   - 清理旧的数据库表
   - 确保模型已正确下载

2. **连接错误**：
   - 验证 Supabase 连接字符串
   - 确认 Ollama 服务运行状态
   - 检查端口配置

## 故障排除

### 常见问题及解决方案

1. **"Dimensions reported by adapter, dimension, and existing collection do not match"**
   ```bash
   # 解决步骤：
   # 1. 检查当前使用的嵌入模型维度
   ollama show nomic-embed-text:latest  # 768 维度
   ollama show snowflake-arctic-embed2  # 1024 维度

   # 2. 清理数据库中的旧表
   psql "postgresql://postgres:postgres@localhost:54322/postgres" -c "DROP SCHEMA IF EXISTS vecs CASCADE;"
   psql "postgresql://postgres:postgres@localhost:54322/postgres" -c "CREATE SCHEMA IF NOT EXISTS vecs;"

   # 3. 重新运行测试
   uv run python test_memory.py
   ```

2. **Ollama 连接失败**
   ```bash
   # 检查 Ollama 服务状态
   ollama list

   # 启动 Ollama（如果未运行）
   ollama serve
   ```

3. **Supabase 连接问题**
   ```bash
   # 测试数据库连接
   psql "postgresql://postgres:postgres@localhost:54322/postgres" -c "SELECT version();"
   ```

4. **模型未找到**
   ```bash
   # 下载缺失的模型
   ollama pull qwen3:4b
   ollama pull nomic-embed-text:latest
   ```

### 性能优化建议

1. **嵌入模型选择**：
   - 开发/测试环境：使用 `nomic-embed-text:latest`（更快）
   - 生产环境：使用 `snowflake-arctic-embed2`（更准确）

2. **数据库优化**：
   - 定期清理旧的记忆数据
   - 监控向量索引性能
   - 根据数据量调整索引参数

## 下一步计划

1. 完善记忆分析算法
2. 增强灵感生成能力
3. 开发前端动画界面组件
4. 实现与主系统的集成
5. 添加用户反馈机制
6. 进行性能优化
7. 添加记忆数据的备份和恢复功能
