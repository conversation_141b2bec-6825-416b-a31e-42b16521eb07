[project]
name = "agent-memory"
version = "0.1.0"
description = "Agent Memory service using mem0"
authors = [
    {name = "3stooges", email = "<EMAIL>"},
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.23.2",
    "mem0ai>=0.1.0",
    "pydantic>=2.4.2",
    "python-dotenv>=1.0.0",
    "supabase>=1.0.3",
    "pgvector>=0.2.0",
    "ollama>=0.4.8",
    "vecs>=0.4.5",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/agent_memory"]

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "B", "I"]
ignore = []

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

# 添加 uv 镜像设置
[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true
