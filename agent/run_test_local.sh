#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running Agent Memory test locally...${NC}"

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo -e "${YELLOW}Virtual environment not found. Setting up...${NC}"
    ./setup.sh
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source .venv/bin/activate

# Check if Ollama is running
if ! curl -s http://localhost:11434/api/tags > /dev/null; then
    echo -e "${RED}Ollama is not running. Please start Ollama and try again.${NC}"
    echo -e "${YELLOW}You can start Ollama with: ollama serve${NC}"
    exit 1
fi

# Check if required models are available
echo -e "${YELLOW}Checking if required models are available...${NC}"
MODELS=$(curl -s http://localhost:11434/api/tags)

if ! echo "$MODELS" | grep -q "qwen3:4b"; then
    echo -e "${YELLOW}qwen3:4b model not found in Ollama. Checking local files...${NC}"

    # Check if model exists in ~/.ollama directory
    if [ -d "$HOME/.ollama/models" ] && ls $HOME/.ollama/models/*qwen3:4b* 1> /dev/null 2>&1; then
        echo -e "${GREEN}qwen3:4b model found in local files. Ollama should load it automatically.${NC}"
    else
        echo -e "${YELLOW}qwen3:4b model not found locally. Pulling...${NC}"
        ollama pull qwen3:4b
    fi
fi

if ! echo "$MODELS" | grep -q "snowflake-arctic-embed2"; then
    echo -e "${YELLOW}snowflake-arctic-embed2 model not found in Ollama. Checking local files...${NC}"

    # Check if model exists in ~/.ollama directory
    if [ -d "$HOME/.ollama/models" ] && ls $HOME/.ollama/models/*snowflake-arctic-embed2* 1> /dev/null 2>&1; then
        echo -e "${GREEN}snowflake-arctic-embed2 model found in local files. Ollama should load it automatically.${NC}"
    else
        echo -e "${YELLOW}snowflake-arctic-embed2 model not found locally. Pulling...${NC}"
        ollama pull snowflake-arctic-embed2
    fi
fi

# Run the test script
echo -e "${YELLOW}Running test script...${NC}"
python test_memory.py

echo -e "${GREEN}Test completed!${NC}"
