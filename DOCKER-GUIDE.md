# 3Stooges Portal Docker 部署指南

本指南将帮助您使用 Docker Compose 部署和管理 3Stooges Portal 应用。

## 前提条件

确保您的系统已安装以下软件：

- Docker
- Docker Compose
- Supabase CLI (用于本地开发)

## 部署架构

应用采用以下架构部署：

- **前端**: 使用 Nginx 作为 Web 服务器，提供静态文件服务和反向代理
- **后端**: FastAPI 应用，提供 API 服务
- **PDF 服务**: 提供 PDF 处理功能
- **Supabase**: 在宿主机上运行，容器通过 `host.docker.internal` 访问

## 快速开始

### 1. 启动 Supabase 服务

首先，确保 Supabase 服务在宿主机上运行：

```bash
# 在项目根目录下
supabase start
```

### 2. 使用启动脚本

我们提供了一个便捷的启动脚本来管理 Docker 服务：

```bash
# 添加执行权限
chmod +x docker-start.sh

# 构建并启动服务
./docker-start.sh --build
```

### 3. 访问应用

应用将在以下地址可用：

- 前端应用: http://localhost:8080
- 后端 API: http://localhost:8080/api/
- PDF 服务: http://localhost:8080/pdf-service/

## 启动脚本使用说明

启动脚本提供了多种命令来管理 Docker 服务：

```bash
# 显示帮助信息
./docker-start.sh --help

# 构建并启动服务
./docker-start.sh --build

# 启动服务（不重新构建）
./docker-start.sh --start

# 停止服务
./docker-start.sh --stop

# 重启服务
./docker-start.sh --restart

# 查看日志
./docker-start.sh --logs

# 查看服务状态
./docker-start.sh --status
```

## 手动管理 Docker 服务

如果您不想使用启动脚本，也可以直接使用 Docker Compose 命令：

### 构建并启动服务

```bash
docker-compose up -d --build
```

### 启动服务（不重新构建）

```bash
docker-compose up -d
```

### 停止服务

```bash
docker-compose down
```

### 重启服务

```bash
docker-compose restart
```

### 查看日志

```bash
# 查看所有服务的日志
docker-compose logs -f

# 查看特定服务的日志
docker-compose logs -f stooges-backend
docker-compose logs -f stooges-pdf-service
docker-compose logs -f stooges-nginx
```

### 检查容器状态

```bash
docker-compose ps
```

### 进入容器内部

```bash
docker-compose exec stooges-backend bash
docker-compose exec stooges-pdf-service bash
docker-compose exec stooges-nginx sh
```

## 环境变量配置

各服务的环境变量配置文件：

- 后端: `backend/.env.docker`
- 前端: `frontend/.env.docker`

如需修改配置，请编辑这些文件后重新构建服务。

## 故障排除

### 1. Supabase 连接问题

如果容器无法连接到宿主机上的 Supabase，请检查：

- Supabase 服务是否正在运行 (`supabase status`)
- `host.docker.internal` 是否正确解析为宿主机 IP
- 防火墙设置是否允许容器访问宿主机端口

### 2. 构建失败

如果构建过程失败，可能的原因包括：

- 网络问题导致依赖下载失败
- 磁盘空间不足
- Docker 资源限制过低

尝试以下解决方案：

```bash
# 清理 Docker 缓存
docker system prune -a

# 增加 Docker 资源限制（内存、CPU）
# 在 Docker Desktop 设置中调整
```

### 3. 服务无法启动

如果服务启动失败，请检查日志：

```bash
docker-compose logs -f
```

常见问题包括：

- 端口冲突（8080 端口已被占用）
- 环境变量配置错误
- 数据库连接问题

### 4. 前端无法访问后端 API

如果前端无法访问后端 API，请检查：

- Nginx 配置是否正确
- 后端服务是否正常运行
- 网络配置是否正确

## 生产环境部署注意事项

对于生产环境部署，建议：

1. 使用 HTTPS 加密通信
2. 配置适当的日志轮转
3. 设置容器资源限制
4. 实施监控和告警
5. 配置自动备份
6. 使用 Docker Swarm 或 Kubernetes 进行容器编排

## 更多资源

- [Docker 文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Supabase 文档](https://supabase.io/docs)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Nginx 文档](https://nginx.org/en/docs/)
