#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
  echo -e "${2}${1}${NC}"
}

print_message "3Stooges Portal Docker 环境检查工具" "$BLUE"
echo ""

# 检查 Docker 是否安装
print_message "检查 Docker 安装..." "$YELLOW"
if command -v docker &> /dev/null; then
  docker_version=$(docker --version)
  print_message "✅ Docker 已安装: $docker_version" "$GREEN"
else
  print_message "❌ Docker 未安装" "$RED"
  print_message "请先安装 Docker: https://docs.docker.com/get-docker/" "$YELLOW"
  exit 1
fi

# 检查 Docker Compose 是否安装
print_message "检查 Docker Compose 安装..." "$YELLOW"
if command -v docker-compose &> /dev/null; then
  compose_version=$(docker-compose --version)
  print_message "✅ Docker Compose 已安装: $compose_version" "$GREEN"
else
  print_message "❌ Docker Compose 未安装" "$RED"
  print_message "请先安装 Docker Compose: https://docs.docker.com/compose/install/" "$YELLOW"
  exit 1
fi

# 检查 Docker 服务是否运行
print_message "检查 Docker 服务状态..." "$YELLOW"
if docker info &> /dev/null; then
  print_message "✅ Docker 服务正在运行" "$GREEN"
else
  print_message "❌ Docker 服务未运行" "$RED"
  print_message "请启动 Docker 服务" "$YELLOW"
  exit 1
fi

# 检查 Supabase 是否安装
print_message "检查 Supabase CLI 安装..." "$YELLOW"
if command -v supabase &> /dev/null; then
  supabase_version=$(supabase --version)
  print_message "✅ Supabase CLI 已安装: $supabase_version" "$GREEN"
else
  print_message "⚠️ Supabase CLI 未安装" "$YELLOW"
  print_message "建议安装 Supabase CLI: https://supabase.com/docs/guides/cli" "$YELLOW"
fi

# 检查 Supabase 服务是否运行
print_message "检查 Supabase 服务状态..." "$YELLOW"
if nc -z localhost 54321 &>/dev/null; then
  print_message "✅ Supabase 服务正在运行" "$GREEN"
else
  print_message "⚠️ Supabase 服务未运行" "$YELLOW"
  print_message "请使用 'supabase start' 启动 Supabase 服务" "$YELLOW"
fi

# 检查必要的文件是否存在
print_message "检查必要文件..." "$YELLOW"

files_to_check=(
  "docker-compose.yml"
  "backend/Dockerfile"
  "frontend/Dockerfile"
  "pdf_service/Dockerfile"
  "docker/nginx/Dockerfile"
  "docker/nginx/nginx.conf"
  "backend/.env.docker"
  "frontend/.env.docker"
)

all_files_exist=true

for file in "${files_to_check[@]}"; do
  if [ -f "$file" ]; then
    print_message "✅ 文件存在: $file" "$GREEN"
  else
    print_message "❌ 文件不存在: $file" "$RED"
    all_files_exist=false
  fi
done

if [ "$all_files_exist" = false ]; then
  print_message "❌ 缺少必要文件，请确保所有文件都存在" "$RED"
  exit 1
fi

# 检查端口是否被占用
print_message "检查端口占用情况..." "$YELLOW"

port_to_check=8080
if nc -z localhost $port_to_check &>/dev/null; then
  print_message "⚠️ 端口 $port_to_check 已被占用，可能会导致部署冲突" "$YELLOW"
  print_message "请确保端口 $port_to_check 未被其他应用占用" "$YELLOW"
else
  print_message "✅ 端口 $port_to_check 未被占用" "$GREEN"
fi

# 总结
echo ""
print_message "环境检查完成" "$BLUE"
print_message "您可以使用以下命令部署应用:" "$YELLOW"
print_message "  ./docker-start.sh --build" "$BLUE"
echo ""
