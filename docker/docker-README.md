# 使用 Docker Compose 运行项目

本项目使用 Docker Compose 进行容器化部署，包括前端、后端、PDF 服务和 Supabase 服务。为了更好地管理服务，我们将 Supabase 和应用服务分开配置。

## 前提条件

- 安装 [Docker](https://docs.docker.com/get-docker/)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)
- 安装 [Python 3](https://www.python.org/downloads/) (用于运行启动脚本)

## 快速开始

1. 克隆仓库：

```bash
git clone <repository-url>
cd <repository-directory>
```

2. 使用启动脚本启动所有服务：

```bash
# 给脚本添加执行权限
chmod +x start_services.py

# 启动所有服务
./start_services.py
```

3. 访问服务：

- 前端：http://localhost:3000
- 后端 API：http://localhost:8000
- PDF 服务：http://localhost:8002
- Supabase：http://localhost:54321

## 启动脚本选项

启动脚本提供了一些选项，可以根据需要使用：

```bash
# 跳过启动 Supabase 服务
./start_services.py --skip-supabase

# 跳过启动应用服务
./start_services.py --skip-app

# 设置等待 Supabase 初始化的时间（默认为 10 秒）
./start_services.py --wait 20
```

## 服务说明

- **postgres**：PostgreSQL 数据库服务，用于存储应用数据
- **supabase**：Supabase 服务，提供身份验证和数据库访问
- **backend**：后端 API 服务，基于 FastAPI
- **pdf_service**：PDF 处理服务，提供 PDF 文件的解析和转换功能
- **frontend**：前端服务，基于 React 和 Vite

## 环境变量

环境变量配置在 `.env` 文件中，启动脚本会自动创建该文件（如果不存在）。您可以根据需要修改该文件中的配置。

## 手动启动服务

如果您不想使用启动脚本，也可以手动启动服务：

1. 启动 Supabase 服务：

```bash
docker compose -f supabase-docker-compose.yml up -d
```

2. 等待 Supabase 初始化（约 10 秒）

3. 启动应用服务：

```bash
docker compose -f docker-compose.yml up -d
```

## 数据持久化

数据存储在 Docker 卷中，即使容器被删除，数据也不会丢失：

- `postgres_data`：PostgreSQL 数据
- `supabase_data`：Supabase 数据

## 故障排除

1. 如果服务无法启动，请检查日志：

```bash
docker-compose logs -f [service-name]
```

2. 如果需要重置数据库，可以删除卷并重新创建：

```bash
docker-compose down -v
docker-compose up -d
```

3. 如果需要进入容器内部进行调试：

```bash
docker-compose exec [service-name] bash
```

## 生产环境部署

对于生产环境，建议：

1. 修改所有默认密码和密钥
2. 配置 HTTPS
3. 限制端口访问
4. 设置适当的资源限制
5. 配置监控和日志收集
