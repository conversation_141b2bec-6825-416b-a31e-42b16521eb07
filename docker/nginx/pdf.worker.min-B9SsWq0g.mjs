// 这个文件是一个有效的 JavaScript 模块，用于替代 PDF.js worker
// 它实现了 PDF.js worker 的最小接口，以便前端代码可以正常工作

// 创建一个全局的 pdfjsWorker 对象
const pdfjsWorker = {
  WorkerMessageHandler: {
    setup(handler) {
      console.log('PDF.js worker setup called');
      // 返回一个空对象，表示设置成功
      return {};
    }
  }
};

// 导出 WorkerMessageHandler
export const WorkerMessageHandler = pdfjsWorker.WorkerMessageHandler;

// 默认导出
export default pdfjsWorker;
