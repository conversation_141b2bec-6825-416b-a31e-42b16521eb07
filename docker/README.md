# Docker 部署指南

本项目使用 Docker 进行容器化部署，包括前端、后端和 PDF 服务。Supabase 服务在宿主机上单独运行。

## 部署架构

- **前端**: 使用 Nginx 作为 Web 服务器，提供静态文件服务和反向代理
- **后端**: FastAPI 应用，提供 API 服务
- **PDF 服务**: 提供 PDF 处理功能
- **Supabase**: 在宿主机上运行，容器通过 `host.docker.internal` 访问

## 部署步骤

### 1. 启动 Supabase 服务

确保 Supabase 服务在宿主机上运行：

```bash
# 在项目根目录下
supabase start
```

### 2. 构建并启动 Docker 容器

```bash
# 在项目根目录下
docker compose up -d --build
```

这将构建并启动所有服务，包括：
- 后端 API 服务
- 前端 Web 应用
- PDF 处理服务
- Nginx 反向代理

### 3. 访问应用

应用将在以下地址可用：
- 前端应用: http://localhost:8080
- 后端 API: http://localhost:8080/api/
- PDF 服务: http://localhost:8080/pdf-service/

## 环境变量配置

各服务的环境变量配置文件：

- 后端: `backend/.env.docker`
- 前端: `frontend/.env.docker`

## 网络配置

- 所有服务都在 `app-network` 网络中
- 只有 Nginx 服务暴露端口到宿主机
- 容器间通信使用服务名称作为主机名
- 容器访问宿主机上的 Supabase 使用 `host.docker.internal`

## 数据持久化

- 前端构建产物通过 Docker 卷 `frontend-build` 共享给 Nginx

## 故障排除

### 查看容器日志

```bash
# 查看特定服务的日志
docker compose logs -f stooges-backend
docker compose logs -f stooges-pdf-service
docker compose logs -f stooges-nginx
```

### 重启服务

```bash
# 重启特定服务
docker compose restart stooges-backend
docker compose restart stooges-pdf-service
docker compose restart stooges-nginx
```

### 重建服务

```bash
# 重建特定服务
docker compose up -d --build backend
docker compose up -d --build pdf_service
docker compose up -d --build frontend
docker compose up -d --build nginx
```

### 检查容器状态

```bash
docker compose ps
```

### 进入容器内部

```bash
docker compose exec stooges-backend bash
docker compose exec stooges-pdf-service bash
docker compose exec stooges-nginx sh
```
