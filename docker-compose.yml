version: '3.8'

services:
  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: stooges-backend
    restart: always
    ports:
      - "8000:8000"  # 暴露后端端口
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # PDF 服务
  pdf_service:
    build:
      context: ./pdf_service
      dockerfile: Dockerfile
    container_name: stooges-pdf-service
    restart: always
    ports:
      - "8002:8002"  # 暴露 PDF 服务端口
    environment:
      - HF_ENDPOINT=https://hf-mirror.com  # 设置 Hugging Face 镜像站点
      - DOCKER_ENV=true  # 设置 Docker 环境变量
      - PRELOAD_MODELS=true  # 启用模型预加载
      - PYTHONUNBUFFERED=1  # 确保Python输出不被缓存，便于查看日志
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 前端服务 - 开发模式，支持热重载
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: stooges-frontend-dev
    ports:
      - "5173:5173"  # 暴露 Vite 开发服务器端口
    volumes:
      - ./frontend:/app  # 挂载本地代码目录，实现热重载
      - /app/node_modules  # 排除 node_modules 目录
    environment:
      - DOCKER_ENV=true  # 显式设置 Docker 环境变量
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Nginx 服务 - 仅用于生产环境，开发环境下注释掉
  # nginx:
  #   build:
  #     context: ./docker/nginx
  #     dockerfile: Dockerfile
  #   container_name: stooges-nginx
  #   restart: always
  #   ports:
  #     - "8080:80"
  #   depends_on:
  #     - backend
  #     - pdf_service
  #   volumes:
  #     - frontend-build:/usr/share/nginx/html
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  frontend-build: # 仅在生产环境使用，但保留定义以避免错误
