from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import pymupdf
import requests
import re
import time
from typing import List, Dict, Any
from urllib.parse import unquote
from docling.document_converter import DocumentConverter  # 添加Docling导入
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("pdf-service")

app = FastAPI(title="PDF Service")

# Add CORS middleware with specific settings
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 预加载模型，避免首次请求时的延迟
@app.on_event("startup")
async def startup_event():
    logger.info("PDF Service starting up...")

    # 检查是否需要预加载模型
    if os.environ.get("PRELOAD_MODELS", "true").lower() == "true":
        try:
            logger.info("Preloading models...")
            start_time = time.time()

            # 预加载 PyMuPDF
            logger.info("Initializing PyMuPDF...")
            doc = pymupdf.Document()
            page = doc.new_page()
            page.insert_text((50, 50), "Test PDF")
            doc.close()

            # 预加载 Docling 模型 (如果需要)
            try:
                logger.info("Initializing Docling models...")
                converter = DocumentConverter()
                # 只初始化模型，不进行完整转换
                converter._init_models()
            except Exception as e:
                logger.warning(f"Failed to preload Docling models: {str(e)}")
                logger.info("This is not critical as we're using PyMuPDF for conversion")

            elapsed = time.time() - start_time
            logger.info(f"Models preloaded successfully in {elapsed:.2f} seconds")
        except Exception as e:
            logger.error(f"Error preloading models: {str(e)}")
            logger.info("Service will continue, but first requests may be slow")
    else:
        logger.info("Model preloading disabled by environment variable")

def convert_url_for_docker(url: str) -> str:
    """
    转换 URL 以适应 Docker 环境

    在 Docker 容器中，localhost 指向容器自身，而不是宿主机。
    因此，需要将 localhost:54321 转换为 host.docker.internal:54321，
    这样容器就可以访问宿主机上的 Supabase 服务。

    Args:
        url: 原始 URL

    Returns:
        转换后的 URL
    """
    # 检查是否是 Docker 环境
    is_docker = os.environ.get('DOCKER_ENV', 'false').lower() == 'true'

    # 如果不是 Docker 环境，直接返回原始 URL
    if not is_docker:
        return url

    # 将 localhost:54321 或 127.0.0.1:54321 转换为 host.docker.internal:54321
    if 'localhost:54321' in url:
        return url.replace('localhost:54321', 'host.docker.internal:54321')

    # 处理 127.0.0.1:54321 的情况
    if '127.0.0.1:54321' in url:
        return url.replace('127.0.0.1:54321', 'host.docker.internal:54321')

    return url

@app.get("/extract-toc")
async def extract_table_of_contents_from_url(url: str):
    """Extract table of contents from PDF URL."""
    try:
        # Decode URL to handle special characters
        decoded_url = unquote(url)

        # 转换 URL 以适应 Docker 环境
        docker_url = convert_url_for_docker(decoded_url)
        print(f"原始 URL: {decoded_url}")
        print(f"转换后的 URL: {docker_url}")

        # Get PDF content from URL
        response = requests.get(docker_url)
        if response.status_code != 200:
            raise HTTPException(status_code=400, detail=f"Failed to fetch PDF from URL: {response.status_code} - {response.text}")

        # Open PDF from memory stream
        doc = pymupdf.Document(stream=response.content)
        try:
            toc = doc.get_toc()
            return {
                "table_of_contents": [
                    {
                        "level": level,
                        "title": title,
                        "page": page
                    }
                    for level, title, page in toc
                ],
                "message": "No table of contents found" if not toc else None
            }
        finally:
            doc.close()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")

@app.get("/convert-to-markdown")
async def convert_document_to_markdown(url: str, extract_outline: bool = True):
    """
    Convert document from URL to markdown format.

    This implementation uses PyMuPDF directly without Docling to avoid the issues
    with Docling's conversion process.

    Parameters:
    - url: URL of the document to convert
    - extract_outline: Whether to extract and return the outline (headings) from the converted Markdown
    """
    try:
        # Decode URL to handle special characters
        decoded_url = unquote(url)

        # 转换 URL 以适应 Docker 环境
        docker_url = convert_url_for_docker(decoded_url)
        print(f"原始 URL: {decoded_url}")
        print(f"转换后的 URL: {docker_url}")

        # First check if we can access the URL
        try:
            print(f"尝试访问 URL: {docker_url}")
            response = requests.get(docker_url, timeout=10)
            print(f"URL 访问状态码: {response.status_code}")
            if response.status_code != 200:
                raise HTTPException(status_code=400, detail=f"Failed to fetch document from URL: {response.status_code} - {response.text}")

            # Save the content to a temporary file for debugging
            content_type = response.headers.get('Content-Type', '')
            print(f"文档内容类型: {content_type}")
            print(f"文档大小: {len(response.content)} bytes")

        except requests.RequestException as req_err:
            print(f"URL 访问错误: {str(req_err)}")
            raise HTTPException(status_code=400, detail=f"Failed to access URL: {str(req_err)}")

        # Use PyMuPDF to extract text directly
        try:
            print("使用 PyMuPDF 提取文本")
            doc = pymupdf.Document(stream=response.content)

            # Extract document metadata
            metadata = doc.metadata
            title = metadata.get("title", "Untitled Document")
            author = metadata.get("author", "Unknown Author")
            subject = metadata.get("subject", "")
            keywords = metadata.get("keywords", "")

            print(f"文档元数据: 标题={title}, 作者={author}")

            # Extract table of contents
            toc = doc.get_toc()
            print(f"目录项数量: {len(toc)}")

            # Create markdown content with metadata
            markdown_content = f"# {title}\n\n"

            if author:
                markdown_content += f"**Author:** {author}\n\n"

            if subject:
                markdown_content += f"**Subject:** {subject}\n\n"

            if keywords:
                markdown_content += f"**Keywords:** {keywords}\n\n"

            # Add table of contents if available
            if toc:
                markdown_content += "## Table of Contents\n\n"
                for level, title, page in toc:
                    indent = "  " * (level - 1)
                    markdown_content += f"{indent}- [{title}](#page-{page})\n"
                markdown_content += "\n"

            # Extract text from each page
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_text = page.get_text()

                # Add page marker and text
                markdown_content += f"## Page {page_num + 1} {{{{{page_num + 1}}}}}\n\n"
                markdown_content += page_text + "\n\n"

            print(f"文本提取成功，Markdown 长度: {len(markdown_content)}")

            # Prepare response
            response = {
                "markdown_content": markdown_content,
                "status": "success",
                "conversion_method": "pymupdf"
            }

            # Extract outline if requested
            if extract_outline:
                print("提取大纲")
                headings = []

                # Use TOC from PDF if available
                if toc:
                    for level, title, page in toc:
                        headings.append({
                            "level": level,
                            "title": title
                        })
                else:
                    # Create a simple outline based on page numbers
                    headings = [{"level": 1, "title": title}]  # Document title
                    for page_num in range(len(doc)):
                        headings.append({
                            "level": 2,
                            "title": f"Page {page_num + 1}"
                        })

                # Add outline to response
                response["outline"] = headings
                response["outline_count"] = len(headings)
                print(f"提取到 {len(headings)} 个标题")

            doc.close()
            return response

        except Exception as pymupdf_err:
            print(f"PyMuPDF 提取失败: {str(pymupdf_err)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Error extracting text with PyMuPDF: {str(pymupdf_err)}")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error converting document: {str(e)}")


@app.get("/extract-markdown-outline")
async def extract_markdown_outline(url: str = None, markdown_content: str = None):
    """
    Extract outline (headings) from Markdown content.

    Either provide a URL to fetch Markdown content or directly provide the Markdown content.
    """
    try:
        # Get Markdown content either from URL or direct input
        content = ""
        if url:
            # Decode URL to handle special characters
            decoded_url = unquote(url)

            # 转换 URL 以适应 Docker 环境
            docker_url = convert_url_for_docker(decoded_url)
            print(f"原始 URL: {decoded_url}")
            print(f"转换后的 URL: {docker_url}")

            # Get Markdown content from URL
            response = requests.get(docker_url)
            if response.status_code != 200:
                raise HTTPException(status_code=400, detail=f"Failed to fetch Markdown from URL: {response.status_code} - {response.text}")
            content = response.text
        elif markdown_content:
            content = markdown_content
        else:
            raise HTTPException(status_code=400, detail="Either url or markdown_content must be provided")

        # Regular expression to match Markdown headings (# Heading 1, ## Heading 2, etc.)
        heading_pattern = re.compile(r'^(#{1,6})\s+(.+?)(?:\s+#{1,6})?$', re.MULTILINE)

        # Find all headings in the Markdown content
        headings = []
        for match in heading_pattern.finditer(content):
            level = len(match.group(1))  # Number of # characters
            title = match.group(2).strip()
            headings.append({
                "level": level,
                "title": title
            })

        return {
            "outline": headings,
            "count": len(headings),
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error extracting Markdown outline: {str(e)}")

@app.post("/extract-markdown-outline")
async def extract_markdown_outline_post(data: Dict[str, str]):
    """
    Extract outline (headings) from Markdown content using POST method.

    This endpoint accepts a JSON body with either a 'url' field or a 'markdown_content' field.
    """
    try:
        url = data.get("url")
        markdown_content = data.get("markdown_content")

        return await extract_markdown_outline(url, markdown_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error extracting Markdown outline: {str(e)}")

@app.get("/")
async def root():
    return {"message": "PDF Service is running"}