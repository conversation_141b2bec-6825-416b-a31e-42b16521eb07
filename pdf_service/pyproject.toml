[project]
name = "pdf-service"
version = "0.1.0"
description = "PDF Service for extracting table of contents"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    { name = "3stooges Team" }
]
license = { text = "MIT" }
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "pymupdf>=1.25.5",
    "requests>=2.32.3",
    "docling>=2.31.0",
]

[tool.uv]
[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"
