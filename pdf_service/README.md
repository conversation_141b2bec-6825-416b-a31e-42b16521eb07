# PDF Service

A microservice for extracting table of contents and converting PDF documents to Markdown format.

## Features

- Extract table of contents from PDF documents
- Convert PDF documents to Markdown format
- Support for remote PDF files via URL

## Requirements

- Python 3.12 or higher
- Dependencies listed in pyproject.toml

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/3stooges-portal-v2.git
cd 3stooges-portal-v2/pdf_service

# Install dependencies using uv
uv pip install -e .
```

## Usage

Start the service:

```bash
uv run uvicorn src.main:app --reload --port 8002
```

The service will be available at http://localhost:8002

## API Endpoints

- `GET /extract-toc?url={pdf_url}`: Extract table of contents from a PDF
- `GET /convert-to-markdown?url={pdf_url}`: Convert PDF to Markdown


## TODO

- [ ] docling支持批量转换(https://docling-project.github.io/docling/examples/batch_convert/)

## License

MIT
