FROM python:3.12-slim

WORKDIR /app

# 复制项目文件
COPY pyproject.toml README.md ./
COPY src ./src

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

# 安装基础依赖
RUN pip install --no-cache-dir setuptools wheel

# 安装项目依赖 (包含 docling)
RUN pip install --no-cache-dir fastapi[standard] python-multipart docling pymupdf huggingface_hub

# 设置 Hugging Face 镜像站点环境变量
ENV HF_ENDPOINT=https://hf-mirror.com
ENV DOCKER_ENV=true

# 创建模型目录
RUN mkdir -p /root/.cache/huggingface/hub/

# 下载 docling 模型 - 使用 --force 确保完全下载
RUN huggingface-cli download --resume-download --force ds4sd/docling-models

# 创建预热脚本，用于预先加载模型
COPY <<EOF /app/preload_models.py
import os
import sys
import pymupdf
import docling
from docling.document_converter import DocumentConverter

# 创建一个简单的PDF文件用于测试
def create_test_pdf():
    doc = pymupdf.Document()
    page = doc.new_page()
    page.insert_text((50, 50), "Test PDF for model preloading")
    test_pdf_path = "/tmp/test.pdf"
    doc.save(test_pdf_path)
    doc.close()
    return test_pdf_path

# 预加载 PyMuPDF
def preload_pymupdf():
    print("Preloading PyMuPDF...")
    test_pdf = create_test_pdf()
    doc = pymupdf.Document(test_pdf)
    doc.close()
    print("PyMuPDF preloaded successfully")

# 预加载 Docling 模型
def preload_docling():
    try:
        print("Preloading Docling models...")
        converter = DocumentConverter()
        test_pdf = create_test_pdf()
        # 只初始化模型，不进行完整转换
        converter._init_models()
        print("Docling models preloaded successfully")
    except Exception as e:
        print(f"Warning: Failed to preload Docling models: {str(e)}")
        print("This is not critical as we're using PyMuPDF for conversion")

if __name__ == "__main__":
    preload_pymupdf()
    preload_docling()
    print("All models preloaded successfully")
EOF

# 运行预热脚本，预加载所有模型
RUN python /app/preload_models.py

# 暴露端口
EXPOSE 8002

# 启动应用
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8002"]
