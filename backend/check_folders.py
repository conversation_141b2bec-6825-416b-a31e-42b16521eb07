from sqlmodel import Session, select
from app.models import Folder
from app.db.database import engine

def main():
    with Session(engine) as session:
        folders = session.exec(select(Folder)).all()
        print('Folders in database:')
        if not folders:
            print('No folders found')
        else:
            for folder in folders:
                print(f'ID: {folder.id}, Name: {folder.name}, User ID: {folder.user_id}')

if __name__ == "__main__":
    main()
