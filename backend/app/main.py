import logging

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.routers import auth, aimodels  # 添加这行
from app.routers import bookmark, folders, diagrams, documents, post, session, message
from app.routers import search_engine

logger = logging.getLogger(__name__)
logging.info("Starting FastAPI app")

from fastapi import FastAPI
from fastapi.openapi.utils import get_openapi

app = FastAPI()

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="3Stooges Portal API",
        version="1.0.0",
        description="三个臭皮匠Restful API",
        routes=app.routes,
    )

    # 修改 OAuth2 密码流的 tokenUrl
    openapi_schema["components"]["securitySchemes"]["OAuth2PasswordBearer"] = {
        "type": "oauth2",
        "flows": {
            "password": {
                "tokenUrl": "v1/api/auth/token",
                "scopes": {}
            }
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加版本前缀 /v1 到所有路由
app.include_router(auth.router, prefix="/v1")
app.include_router(bookmark.router, prefix="/v1")
app.include_router(folders.router, prefix="/v1")
app.include_router(diagrams.router, prefix="/v1")
app.include_router(documents.router, prefix="/v1")
app.include_router(aimodels.router, prefix="/v1")
app.include_router(post.router, prefix="/v1")
app.include_router(session.router, prefix="/v1")
app.include_router(message.router, prefix="/v1")
app.include_router(search_engine.router, prefix="/v1")
