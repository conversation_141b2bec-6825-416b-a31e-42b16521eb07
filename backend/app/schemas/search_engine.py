from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

class SearchEngineBase(BaseModel):
    name: str
    provider: str
    url: str
    token: str
    available_credit: Optional[float] = 0.0
    note: Optional[str] = None
    is_active: bool = True

class SearchEngineCreate(SearchEngineBase):
    pass

class SearchEngineUpdate(BaseModel):
    name: Optional[str] = None
    provider: Optional[str] = None
    url: Optional[str] = None
    token: Optional[str] = None
    available_credit: Optional[float] = None
    note: Optional[str] = None
    is_active: Optional[bool] = None

class SearchEngineRead(SearchEngineBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
