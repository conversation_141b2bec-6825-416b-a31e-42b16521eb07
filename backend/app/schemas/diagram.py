from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel


class DiagramBase(BaseModel):
    name: str
    description: Optional[str] = None
    source: Optional[str] = None
    type: Optional[str] = None


class DiagramCreate(DiagramBase):
    folder_id: Optional[UUID] = None


class DiagramUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    source: Optional[str] = None
    type: Optional[str] = None
    folder_id: Optional[UUID] = None


class DiagramResponse(DiagramBase):
    id: UUID
    content_type: str
    file_size: int
    image_url: str
    user_id: UUID
    folder_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True