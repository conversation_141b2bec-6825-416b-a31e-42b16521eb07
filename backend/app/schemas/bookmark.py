from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel

# TODO: 冗余信息不少，后续考虑优化
class BookmarkBase(BaseModel):
    title: Optional[str] = None
    url: Optional[str] = None
    desc: Optional[str] = None
    content: Optional[str] = None
    summary: Optional[str] = None
    bookmark_type: str = "bookmark"
    tags: Optional[str] = None
    is_ticked: bool = False
    content_type: Optional[str] = None
    rating: Optional[float] = 0.0
    is_public: bool = False
    folder_id: Optional[UUID] = None


class BookmarkCreate(BookmarkBase):
    pass


class BookmarkRead(BookmarkBase):
    id: UUID
    user_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None


class BookmarkUpdate(BaseModel):
    title: Optional[str] = None
    url: Optional[str] = None
    desc: Optional[str] = None
    content: Optional[str] = None
    summary: Optional[str] = None
    bookmark_type: Optional[str] = None
    tags: Optional[str] = None
    is_ticked: Optional[bool] = None
    content_type: Optional[str] = None
    rating: Optional[float] = None
    is_public: Optional[bool] = None
    folder_id: Optional[UUID] = None
