from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

class AIModelBase(BaseModel):
    name: str
    model_type: str
    model_name: str
    provider: str
    credential: str
    base_url: Optional[str] = None  # 新增字段

class AIModelCreate(AIModelBase):
    pass

class AIModelUpdate(BaseModel):
    name: Optional[str] = None
    model_type: Optional[str] = None
    model_name: Optional[str] = None
    provider: Optional[str] = None
    credential: Optional[str] = None
    base_url: Optional[str] = None  # 新增字段

class AIModelRead(AIModelBase):
    id: UUID
    user_id: UUID
    create_time: datetime
    update_time: datetime

    class Config:
        from_attributes = True