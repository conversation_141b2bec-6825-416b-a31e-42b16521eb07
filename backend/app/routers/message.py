# message路由暂时其实不需要

# TODO：
# 用户在一次对话中可能引用 bookmark，diagram，document，post等内容
# 这些内容的引用需要在message中记录


from fastapi import APIRouter, HTTPException, status, Body
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timezone
from pydantic import BaseModel

from sqlmodel import select
from app.models import Message, Session as ChatSession
from app.dependencies import DBSessionDependency, UserDependency

# 定义消息更新请求模型
class MessageUpdate(BaseModel):
    content: Optional[str] = None

router = APIRouter(prefix="/api/messages", tags=["messages"])

@router.get("/{message_id}", response_model=Message)
async def get_message(
    message_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """获取单条消息详情"""
    message = db.get(Message, message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    # 验证消息所属的会话是否属于当前用户
    session = db.get(ChatSession, message.session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    return message

@router.put("/{message_id}", response_model=Message)
async def update_message(
    message_id: UUID,
    message_update: MessageUpdate = Body(...),
    content: str = None,  # 保留查询参数以兼容旧版本
    current_user: UserDependency = None,
    db: DBSessionDependency = None
):
    """更新消息内容"""
    message = db.get(Message, message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    # 验证消息所属的会话是否属于当前用户
    session = db.get(ChatSession, message.session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    # 优先使用请求体中的内容，如果没有则使用查询参数
    new_content = message_update.content if message_update.content is not None else content

    if new_content is None:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail="Content is required"
        )

    # 更新消息
    message.content = new_content
    message.updated_at = datetime.now(timezone.utc)

    # 同时更新会话的更新时间
    session.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(message)

    return message

@router.delete("/{message_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message(
    message_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """删除消息"""
    message = db.get(Message, message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    # 验证消息所属的会话是否属于当前用户
    session = db.get(ChatSession, message.session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )

    # 删除消息
    db.delete(message)
    # 更新会话的更新时间
    session.updated_at = datetime.now(timezone.utc)
    db.commit()

    return None