import logging
from datetime import datetime
from typing import List
from uuid import UUID

from fastapi import APIRouter, HTTPException
from sqlmodel import select

from ..db.crud import update_db_element
from ..db.bookmark_crud import get_bookmark_by_id
from ..db.folder_crud import get_folder_by_id

from ..models import Bookmark, Folder
from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.bookmark import BookmarkCreate, BookmarkRead, BookmarkUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/bookmark",  tags=["bookmarks"])


@router.post("/")
def create_bookmark(
    bookmark: BookmarkCreate, db: DBSessionDependency, user: UserDependency
):
    try:
        # 如果没有提供folder_id，查找用户的默认根文件夹
        if not bookmark.folder_id:
            # 查询用户的第一个文件夹（假设是默认根文件夹）
            default_folder = db.exec(
                select(Folder)
                .where(Folder.user_id == user.id)
                .order_by(Folder.created_at)
                .limit(1)
            ).first()

            if default_folder:
                bookmark.folder_id = default_folder.id
                logging.info(f"Using default folder for user {user.id}: {default_folder.id}")
            else:
                # 如果用户没有任何文件夹，创建一个默认文件夹
                # 获取用户信息以个性化文件夹
                display_name = user.display_name or user.username or user.email.split('@')[0]

                default_folder = Folder(
                    name=f"{display_name}的工作区",  # 使用用户显示名称个性化文件夹名称
                    desc=f"{display_name}的个人空间 - 创建于{datetime.now().strftime('%Y-%m-%d')}",  # 包含用户名和创建日期
                    color="#4A90E2",
                    user_id=user.id,
                    created_at=datetime.now()
                )
                db.add(default_folder)
                db.commit()
                db.refresh(default_folder)

                bookmark.folder_id = default_folder.id
                logging.info(f"Created new default folder for user {user.id}: {default_folder.id}")
        else:
            # 如果提供了folder_id，检查文件夹是否存在
            try:
                # This will raise an HTTPException if folder doesn't exist
                get_folder_by_id(db, bookmark.folder_id, user_id=user.id)
            except HTTPException as folder_error:
                # Re-raise with a more specific message
                raise HTTPException(
                    status_code=404,
                    detail=f"Folder with ID {bookmark.folder_id} not found. Please select a valid folder."
                )

        # Create the bookmark
        db.add(Bookmark(**bookmark.model_dump(), user_id=user.id))
        db.commit()
    except HTTPException:
        # Re-raise HTTPException to preserve status code and detail
        raise
    except Exception as e:
        logging.error("Error during bookmark creation %s", e)
        raise HTTPException(status_code=400, detail="Error during bookmark creation")
    return {"message": "Bookmark created successfully", "data": bookmark}


@router.put("/{bookmark_id}")
def update_bookmark(
    bookmark_id: UUID,
    bookmark_update: BookmarkUpdate,
    db: DBSessionDependency,
    user: UserDependency,
):
    try:
        logging.info(f"更新书签: ID={bookmark_id}, 用户ID={user.id}")
        logging.info(f"更新数据: {bookmark_update.model_dump()}")

        # Check if folder exists if folder_id is provided in the update
        if bookmark_update.folder_id is not None:
            try:
                # This will raise an HTTPException if folder doesn't exist
                get_folder_by_id(db, bookmark_update.folder_id, user_id=user.id)
            except HTTPException as folder_error:
                # Re-raise with a more specific message
                raise HTTPException(
                    status_code=404,
                    detail=f"Folder with ID {bookmark_update.folder_id} not found. Please select a valid folder."
                )

        db_bookmark = get_bookmark_by_id(db=db, bookmark_id=bookmark_id, user_id=user.id)
        if not db_bookmark:
            logging.error(f"书签未找到: ID={bookmark_id}, 用户ID={user.id}")
            raise HTTPException(status_code=404, detail="书签未找到")

        logging.info(f"找到书签: {db_bookmark}")
        updated_bookmark = update_db_element(db, db_bookmark, bookmark_update)
        logging.info(f"更新成功: {updated_bookmark}")

        return {"message": "书签更新成功", "data": updated_bookmark}
    except HTTPException:
        # Re-raise HTTPException to preserve status code and detail
        raise
    except Exception as e:
        logging.error(f"更新书签时出错: {e}")
        raise HTTPException(status_code=400, detail=f"更新书签时出错: {str(e)}")


@router.delete("/{bookmark_id}")
def delete_bookmark(bookmark_id: UUID, db: DBSessionDependency, user: UserDependency):
    try:
        db_bookmark = get_bookmark_by_id(db=db, bookmark_id=bookmark_id, user_id=user.id)
        if not db_bookmark:
            raise HTTPException(status_code=404, detail="书签未找到")

        db.delete(db_bookmark)
        db.commit()

        return {"message": "书签删除成功", "data": db_bookmark}
    except Exception as e:
        logging.error("删除书签时出错 %s", e)
        db.rollback()
        raise HTTPException(status_code=400, detail="删除书签时出错")


@router.get("/", response_model=List[BookmarkRead])
def get_all_bookmarks(
    db: DBSessionDependency,
    user: UserDependency,
    skip: int = 0,
    limit: int = 100,
    folder_id: UUID = None,
    bookmark_type: str = None
):
    """获取用户的所有书签，支持分页和过滤"""
    try:
        query = db.query(Bookmark).filter(Bookmark.user_id == user.id)

        # 添加过滤条件
        if folder_id:
            query = query.filter(Bookmark.folder_id == folder_id)
        if bookmark_type:
            query = query.filter(Bookmark.bookmark_type == bookmark_type)

        # 添加排序和分页
        bookmarks = query.order_by(Bookmark.created_at.desc()).offset(skip).limit(limit).all()
        return bookmarks
    except Exception as e:
        logging.error("获取书签列表时出错 %s", e)
        raise HTTPException(status_code=400, detail="获取书签列表时出错")


@router.get("/{bookmark_id}", response_model=BookmarkRead)
def get_bookmark(bookmark_id: UUID, db: DBSessionDependency, user: UserDependency):
    """根据ID获取单个书签"""
    try:
        bookmark = get_bookmark_by_id(db=db, bookmark_id=bookmark_id, user_id=user.id)
        if not bookmark:
            raise HTTPException(status_code=404, detail="书签未找到")
        return bookmark
    except HTTPException:
        raise
    except Exception as e:
        logging.error("获取书签详情时出错 %s", e)
        raise HTTPException(status_code=400, detail="获取书签详情时出错")
