import logging
from uuid import UUID

from fastapi import APIRouter, HTTPException

from ..db.folder_crud import get_folder_by_id, get_folders
from ..models import Folder
from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.folder import FolderContent, FolderCreate, FolderUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/folders", tags=["workspaces"])


@router.get("/")
def get_folders_route(db: DBSessionDependency, user: UserDependency):
    return get_folders(db, user.id)  # 修改为传递 user.id 而不是 user_id=user.id


@router.get("/{folder_id}")
def get_folder(
    folder_id: UUID, db: DBSessionDependency, user: UserDependency
) -> FolderContent:
    """Return a folder and its content by id."""
    db_folder = get_folder_by_id(db, folder_id, user_id=user.id)
    if db_folder is None:
        raise HTTPException(status_code=404, detail="Folder not found")
    return db_folder


@router.post("/")
def create(folder: FolderCreate, db: DBSessionDependency, user: UserDependency):
    try:
        # 检查父文件夹是否存在（如果指定了父文件夹）
        if folder.parent_folder:
            parent = get_folder_by_id(db, folder.parent_folder, user_id=user.id)
            if parent is None:
                raise HTTPException(status_code=404, detail="Parent folder not found")
        
        db.add(Folder(**folder.model_dump(), user_id=user.id))
        db.commit()
    except HTTPException:
        # 重新抛出 HTTPException，保持原始状态码和详情
        raise
    except Exception as e:
        logging.error("Error during folder creation %s", e)
        raise HTTPException(status_code=400, detail="Error during folder creation")
    return {"message": "Folder created successfully", "data": folder}


@router.put("/update/{folder_id}")
def update(
    folder_id: UUID,
    folder_update: FolderUpdate,
    db: DBSessionDependency,
    user: UserDependency,
):
    db_folder = get_folder_by_id(db, folder_id, user_id=user.id)
    updated_folder = crud.update_db_element(
        db=db, original_element=db_folder, element_update=folder_update
    )
    return updated_folder


@router.delete("/delete/{folder_id}")
def delete(folder_id: UUID, db: DBSessionDependency, user: UserDependency):
    db_folder = get_folder_by_id(db, folder_id, user_id=user.id)
    if db_folder is None:
        raise HTTPException(status_code=404, detail="Folder not found")

    crud.delete_db_element(db=db, element=db_folder)
    return {"detail": "Folder and related bookmarks deleted successfully"}
