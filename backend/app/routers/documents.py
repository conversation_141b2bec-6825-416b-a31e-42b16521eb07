import logging
import uuid
import re
from typing import List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile
from sqlmodel import Session

# 修改导入语句，使用相对导入
from ..db.document_crud import create_document, get_document, get_documents, update_document, delete_document
# from ..db.models import Document
from ..dependencies import DBSessionDependency, SupabaseDependency, UserDependency
from ..schemas.document import DocumentRead, DocumentUpdate

logger = logging.getLogger(__name__)

# 修改路由前缀，添加 v1
router = APIRouter(prefix="/api", tags=["documents"])


@router.post("/document/upload", response_model=DocumentRead)
async def upload_document(
    db: DBSessionDependency,
    user: UserDependency,
    supabase: SupabaseDependency,
    title: str = Form(None),  # 修改为可选参数，默认值为None
    description: Optional[str] = Form(None),
    document_type: str = Form(...),
    file: UploadFile = File(...),
    markdown_file: Optional[UploadFile] = File(None),
):
    """上传文档"""
    logger.info(f"开始处理文档上传请求: 用户ID={user.id}, 文件名={file.filename}")
    
    try:
        # 生成唯一文件名，避免文件名冲突，并移除特殊字符
        from uuid import uuid4
        
        # 清理文件名，只保留字母、数字、点和常见扩展名
        original_filename = file.filename
        # 提取文件名（不含后缀）用作标题
        file_title = original_filename
        if "." in original_filename:
            file_title = original_filename.rsplit(".", 1)[0]
        
        # 如果没有提供标题，则使用文件名作为标题
        if not title or title == "test":
            title = file_title
            
        # 提取文件扩展名
        file_ext = ""
        if "." in original_filename:
            file_ext = original_filename.split(".")[-1]
        
        # 使用UUID作为文件名，添加原始扩展名
        safe_filename = f"{uuid4()}.{file_ext}" if file_ext else f"{uuid4()}"
        
        # 构建存储路径
        bucket_name = "documents"  # 存储桶名称
        storage_path = f"{user.id}/{safe_filename}"
        logger.info(f"存储路径: {storage_path}")
        
        # 上传到Supabase Storage
        logger.info("开始上传到Supabase Storage...")
        try:
            # 读取文件内容
            file_content = await file.read()
            file_size = len(file_content)
            
            # 上传文件内容到Supabase Storage
            response = supabase.storage.from_(bucket_name).upload(
                path=storage_path,
                file=file_content,
                file_options={"content-type": file.content_type}
            )
            logger.info(f"上传到Supabase Storage成功: {response}")
        except Exception as storage_error:
            logger.error(f"上传到Supabase Storage失败: {str(storage_error)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"存储文件失败: {str(storage_error)}")
        
        # 获取文档URL
        try:
            file_url = supabase.storage.from_(bucket_name).get_public_url(storage_path)
            logger.info(f"获取到公共URL: {file_url}")
        except Exception as url_error:
            logger.error(f"获取公共URL失败: {str(url_error)}")
            raise HTTPException(status_code=500, detail=f"获取文件URL失败: {str(url_error)}")
        
        # 处理Markdown文件（如果有）
        markdown_url = None
        if markdown_file:
            # 同样处理Markdown文件名
            md_original_filename = markdown_file.filename
            md_file_ext = ""
            if "." in md_original_filename:
                md_file_ext = md_original_filename.split(".")[-1]
            
            md_safe_filename = f"{uuid4()}.{md_file_ext}" if md_file_ext else f"{uuid4()}"
            md_storage_path = f"{user.id}/{md_safe_filename}"
            
            try:
                md_content = await markdown_file.read()
                supabase.storage.from_(bucket_name).upload(
                    path=md_storage_path,
                    file=md_content,
                    file_options={"content-type": "text/markdown"}
                )
                markdown_url = supabase.storage.from_(bucket_name).get_public_url(md_storage_path)
            except Exception as md_error:
                logger.error(f"上传Markdown文件失败: {str(md_error)}", exc_info=True)
                # 继续处理，不因为Markdown文件失败而中断整个上传
        
        # 创建数据库记录
        document_data = {
            "title": title,  # 现在这里使用的是处理后的标题
            "description": description,
            "type": document_type,
            "content_type": file.content_type,
            "file_path": file_url,
            "markdown_url": markdown_url,
            "file_size": file_size
        }
        
        db_document = create_document(db, document_data, user_id=user.id)
        logger.info(f"数据库记录创建成功: ID={db_document.id}")
        return db_document
        
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"文档上传过程中发生未知错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"文档上传失败: {str(e)}")


# 修改其他路由
# 其他路由也需要保持一致的路径格式
@router.get("/documents", response_model=List[DocumentRead])
def get_documents_route(
    db: DBSessionDependency, 
    user: UserDependency
):
    """获取用户的所有文档"""
    if not user:
        logger.warning("用户未认证，返回空列表")
        return []
    
    logger.info(f"获取用户 {user.id} 的所有文档")
    documents = get_documents(db, user_id=user.id)
    logger.info(f"找到 {len(documents)} 个文档")
    return documents


@router.get("/documents/{document_id}", response_model=DocumentRead)
def get_document_route(
    document_id: uuid.UUID,
    db: DBSessionDependency,
    user: UserDependency
):
    """获取特定文档"""
    logger.info(f"获取文档: ID={document_id}, 用户ID={user.id}")
    
    # 从数据库获取文档
    document = get_document(db, document_id)
    
    # 检查文档是否存在
    if not document:
        logger.warning(f"文档不存在: ID={document_id}")
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 检查用户是否有权限访问该文档
    if document.user_id != user.id:
        logger.warning(f"用户 {user.id} 无权访问文档 {document_id}")
        raise HTTPException(status_code=403, detail="无权访问此文档")
    
    logger.info(f"成功获取文档: {document.id}")
    return document

@router.put("/documents/{document_id}", response_model=DocumentRead)
def update_document_route(
    document_id: uuid.UUID,
    document_update: DocumentUpdate,
    db: DBSessionDependency,
    user: UserDependency,
):
    """更新文档信息"""
    logger.info(f"更新文档: ID={document_id}, 用户ID={user.id}")
    
    # 从数据库获取文档
    document = get_document(db, document_id)
    
    # 检查文档是否存在
    if not document:
        logger.warning(f"文档不存在: ID={document_id}")
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 检查用户是否有权限更新该文档
    if document.user_id != user.id:
        logger.warning(f"用户 {user.id} 无权更新文档 {document_id}")
        raise HTTPException(status_code=403, detail="无权更新此文档")
    
    try:
        # 更新文档 - 添加用户ID参数
        updated_document = update_document(db, document_id, document_update.dict(exclude_unset=True), user.id)
        if not updated_document:
            raise HTTPException(status_code=404, detail="文档更新失败")
        
        logger.info(f"成功更新文档: {updated_document.id}")
        return updated_document
    except Exception as e:
        logger.error(f"更新文档时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新文档失败: {str(e)}")

@router.delete("/documents/{document_id}")
async def delete_document_route(
    document_id: uuid.UUID,
    db: DBSessionDependency,
    user: UserDependency,
):
    """删除文档"""
    logger.info(f"删除文档: ID={document_id}, 用户ID={user.id}")
    
    # 从数据库获取文档
    document = get_document(db, document_id)
    
    # 检查文档是否存在
    if not document:
        logger.warning(f"文档不存在: ID={document_id}")
        raise HTTPException(status_code=404, detail="文档不存在")
    
    # 检查用户是否有权限删除该文档
    if document.user_id != user.id:
        logger.warning(f"用户 {user.id} 无权删除文档 {document_id}")
        raise HTTPException(status_code=403, detail="无权删除此文档")
    
    try:
        # 从Supabase Storage删除文件
        if document.file_path:
            # 从URL中提取存储路径
            storage_path = document.file_path.split("/")[-1]
            bucket_name = "documents"
            full_path = f"{user.id}/{storage_path}"
            
            try:
                supabase.storage.from_(bucket_name).remove([full_path])
                logger.info(f"从存储中删除文件: {full_path}")
            except Exception as storage_error:
                logger.error(f"从存储中删除文件失败: {str(storage_error)}")
                # 继续执行，即使存储删除失败
        
        # 确保传递用户ID
        result = delete_document(db, document_id, user.id)
        if not result:
            raise HTTPException(status_code=404, detail="文档不存在或无权删除")
        return {"message": "文档已成功删除"}

    except Exception as e:
        logger.error(f"删除文档时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")