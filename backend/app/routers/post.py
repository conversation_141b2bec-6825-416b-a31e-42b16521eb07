from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Body, status
from typing import List, Optional
from uuid import UUID
import os
from datetime import datetime, timezone
from pydantic import BaseModel

from sqlmodel import Session, select
from ..models.user import User
# from app.database import get_session
from app.models import Post, PostAttached, PostLike, PostRetweet
# from app.models.user import User
# from app.auth.auth import get_current_user
# from app.config import settings
from app.services.storage import upload_file_to_storage
# 添加依赖导入
from ..dependencies import DBSessionDependency, UserDependency, SupabaseDependency
from uuid import UUID, uuid4


class UserInfo(BaseModel):
    id: UUID
    username: Optional[str] = None
    display_name: Optional[str] = None
    email: Optional[str] = None
    thumbnail: Optional[str] = None

class PostResponse(BaseModel):
    post: Post
    images: List[str]
    owner: Optional[UserInfo] = None

router = APIRouter(prefix="/api/posts", tags=["posts"])

@router.post("/", response_model=Post, status_code=status.HTTP_201_CREATED)
async def create_post(
    current_user: UserDependency,
    db: DBSessionDependency,
    content: str = Form(...),
    images: List[UploadFile] = File(None)
):
    """
    创建一条新微博，可以包含文字和图片
    """
    # 创建新的Post记录
    new_post = Post(
        content=content,
        owner_id=current_user.id,
        timestamp=datetime.now(timezone.utc)
    )
    db.add(new_post)
    db.commit()
    db.refresh(new_post)

    # 处理图片上传
    if images:
        for image in images:
            # 构建存储路径: posts/user_id/filename
            storage_path = f"{current_user.id}"

            # 上传文件到storage
            file_ext = os.path.splitext(image.filename)[1]
            file_name = f"{new_post.id}_{datetime.now().timestamp()}{file_ext}"
            image_url = await upload_file_to_storage(
                file=image,
                # bucket="posts",
                bucket="diagrams",
                path=f"{storage_path}/{file_name}"
            )

            # 存储图片信息到数据库
            diagram_data = {
                "name": f"Post_{new_post.id}_{datetime.now().timestamp()}",
                "description": "Post attachment",
                "source": new_post.id,
                "type": "blog",
                "content_type": image.content_type,
                "file_size": image.size,
                "image_url": image_url,
                # 移除 user_id，通过参数传递
                # "user_id": current_user.id
            }

            # 创建diagram记录
            from ..db.diagram_crud import create_diagram
            db_diagram = create_diagram(db, diagram_data, user_id=current_user.id)

            # 确保 updated_at 字段有值
            if db_diagram.updated_at is None:
                db_diagram.updated_at = db_diagram.created_at

            # 创建post-attached附件关联记录
            post_attached = PostAttached(
                post_id=new_post.id,
                attached_id=db_diagram.id,  # 使用新创建的diagram记录的ID
                attached_type="image"  # 指定为图片类型
            )
            db.add(post_attached)

        db.commit()
        db.refresh(new_post)

    return new_post

@router.get("/", response_model=List[PostResponse])
async def get_posts(
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20,
):
    """
    获取微博列表，包含关联的附件信息和作者信息
    """
    # 使用 select 语句并通过 options 加载关联的附件
    statement = select(Post).offset(skip).limit(limit).order_by(Post.timestamp.desc())

    # 执行查询
    posts = db.exec(statement).all()

    # 构建响应列表
    response_list = []
    for post in posts:
        # 加载附件关系
        db.refresh(post, ["attachments"])

        # 获取附件信息列表
        attached_files = []
        for attached in post.attachments:
            # 根据附件类型获取URL
            if attached.attached_type == "image":
                # 从diagram表获取图片URL
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                # 从document表获取文档URL
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)

        # 获取作者信息
        owner_info = None
        if post.owner_id:
            user = db.get(User, post.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )

        # 构建响应对象
        response_list.append(PostResponse(post=post, images=attached_files, owner=owner_info))

    return response_list

@router.get("/likes", response_model=List[PostResponse])
async def get_liked_posts(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取用户点赞的微博列表
    """
    # 查询用户点赞的所有微博ID
    liked_post_ids = db.exec(
        select(PostLike.post_id).where(PostLike.user_id == current_user.id)
    ).all()

    # 查询这些微博的详细信息
    posts = db.exec(
        select(Post).where(Post.id.in_(liked_post_ids)).offset(skip).limit(limit)
    ).all()

    # 构建响应列表，包含图片信息
    response_list = []
    for post in posts:
        db.refresh(post, ["images"])
        image_urls = [image.image_url for image in post.images]
        response_list.append(PostResponse(post=post, images=image_urls))

    return response_list

@router.get("/retweets", response_model=List[PostResponse])
async def get_retweeted_posts(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取用户转发的微博列表
    """
    # 查询用户转发的所有微博ID
    retweeted_post_ids = db.exec(
        select(PostRetweet.post_id).where(PostRetweet.user_id == current_user.id)
    ).all()

    # 查询这些微博的详细信息
    posts = db.exec(
        select(Post).where(Post.id.in_(retweeted_post_ids)).offset(skip).limit(limit)
    ).all()

    return posts

# Move these routes after /likes and /retweets
@router.get("/{post_id}", response_model=PostResponse)
async def get_post(
    post_id: UUID,
    db: DBSessionDependency
):
    """
    获取单条微博详情，包含关联的图片信息和作者信息
    """
    # 获取微博
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 加载附件关系
    db.refresh(post, ["attachments"])

    # 获取附件信息列表
    attached_files = []
    for attached in post.attachments:
        # 根据附件类型获取URL
        if attached.attached_type == "image":
            # 从diagram表获取图片URL
            from ..models.diagram import Diagram
            diagram = db.get(Diagram, attached.attached_id)
            if diagram and diagram.image_url:
                attached_files.append(diagram.image_url)
        elif attached.attached_type == "document":
            # 从document表获取文档URL
            from ..models.document import Document
            document = db.get(Document, attached.attached_id)
            if document and document.file_url:
                attached_files.append(document.file_url)

    # 获取作者信息
    owner_info = None
    if post.owner_id:
        user = db.get(User, post.owner_id)
        if user:
            owner_info = UserInfo(
                id=user.id,
                username=user.username,
                display_name=user.display_name,
                email=user.email,
                thumbnail=user.thumbnail
            )

    return PostResponse(
        post=post,
        images=attached_files,
        owner=owner_info
    )

@router.delete("/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    删除微博
    """
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是微博所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this post"
        )

    # 删除相关的附件记录
    # 先查询出所有相关的附件记录
    attached_records = db.exec(select(PostAttached).where(PostAttached.post_id == post_id)).all()

    # 逐个删除附件记录
    for attached in attached_records:
        db.delete(attached)

    # 删除微博
    db.delete(post)
    db.commit()

    return None

@router.put("/{post_id}", response_model=Post)
async def update_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    content: str = Body(..., description="更新后的微博内容")
):
    """
    更新微博内容（仅文字内容，不修改附件）
    """
    # 获取微博
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是微博所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this post"
        )

    # 更新微博内容
    post.content = content
    db.add(post)
    db.commit()
    db.refresh(post)

    return post


@router.post("/{post_id}/like", status_code=status.HTTP_201_CREATED)
async def like_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    点赞微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否已经点赞
    existing_like = db.exec(
        select(PostLike).where(
            PostLike.post_id == post_id,
            PostLike.user_id == current_user.id
        )
    ).first()

    if existing_like:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You have already liked this post"
        )

    # 创建点赞记录
    new_like = PostLike(
        user_id=current_user.id,
        post_id=post_id
    )
    db.add(new_like)
    db.commit()

    return {"detail": "Post liked successfully"}


@router.delete("/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
async def unlike_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    取消点赞微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 查找点赞记录
    like = db.exec(
        select(PostLike).where(
            PostLike.post_id == post_id,
            PostLike.user_id == current_user.id
        )
    ).first()

    if not like:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="You have not liked this post"
        )

    # 删除点赞记录
    db.delete(like)
    db.commit()

    return None

# TODO：目前功能不正确，待修复
@router.post("/{post_id}/retweet", status_code=status.HTTP_201_CREATED)
async def retweet_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    content: str = Body(...),  # 添加评论内容
    images: List[str] = Body(default=[])  # 可选的图片列表
):
    """
    跟贴微博
    """
    # 检查原始微博是否存在
    original_post = db.get(Post, post_id)
    if not original_post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Original post not found"
        )

    # 1. 首先创建一个新的评论帖子
    comment_post = Post(
        content=content,
        owner_id=current_user.id,
        timestamp=datetime.now(timezone.utc),
        type="COMMENT",  # 直接使用字符串而不是枚举
        parent_id=post_id  # 设置父帖子ID
    )
    db.add(comment_post)
    db.commit()
    db.refresh(comment_post)

    # 2. 处理图片（如果有）
    if images:
        for image_url in images:
            post_attached = PostAttached(
                post_id=comment_post.id,
                attached_id=uuid4(),  # 动态生成UUID
                attached_type="image"  # 指定为图片类型
            )
            db.add(post_attached)
        db.commit()

    # 3. 创建转发关系，将评论帖子与原始帖子关联
    new_retweet = PostRetweet(
        id=uuid4(),  # 显式设置 ID 字段
        user_id=current_user.id,
        post_id=comment_post.id,  # 这是新创建的评论帖子ID
        original_post_id=post_id,  # 这是原始帖子ID
        timestamp=datetime.now(timezone.utc)
    )
    db.add(new_retweet)
    db.commit()

    return {
        "detail": "Post retweeted successfully",
        "comment_post_id": str(comment_post.id)
    }


@router.delete("/{post_id}/retweet", status_code=status.HTTP_204_NO_CONTENT)
async def undo_retweet(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    取消转发微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 查找转发记录
    retweet = db.exec(
        select(PostRetweet).where(
            PostRetweet.post_id == post_id,
            PostRetweet.user_id == current_user.id
        )
    ).first()

    if not retweet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="You have not retweeted this post"
        )

    # 删除转发记录
    db.delete(retweet)
    db.commit()

    return None


@router.get("/{post_id}/comments", response_model=List[PostResponse])
async def get_post_comments(
    post_id: UUID,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取指定帖子的所有评论，包含作者信息
    """
    comments = db.exec(
        select(Post).where(Post.parent_id == post_id).offset(skip).limit(limit).order_by(Post.timestamp.desc())
    ).all()

    response_list = []
    for comment in comments:
        # 加载附件关系
        db.refresh(comment, ["attachments"])

        # 获取附件信息列表
        attached_files = []
        for attached in comment.attachments:
            # 根据附件类型获取URL
            if attached.attached_type == "image":
                # 从diagram表获取图片URL
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                # 从document表获取文档URL
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)

        # 获取作者信息
        owner_info = None
        if comment.owner_id:
            user = db.get(User, comment.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )

        # 构建响应对象
        response_list.append(PostResponse(post=comment, images=attached_files, owner=owner_info))

    return response_list