import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, HTTPException, status

from ..db.search_engine_crud import (
    create_search_engine,
    get_search_engines,
    get_search_engine_by_id,
    update_search_engine,
    delete_search_engine
)
from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.search_engine import SearchEngineCreate, SearchEngineRead, SearchEngineUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/search/engines", tags=["search_engines"])


@router.post("/", response_model=SearchEngineRead)
def create_search_engine_route(
    search_engine: SearchEngineCreate,
    db: DBSessionDependency,
    user: UserDependency
):
    """创建新的搜索引擎配置"""
    try:
        return create_search_engine(db, search_engine, user.id)
    except Exception as e:
        logger.error(f"创建搜索引擎配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="创建搜索引擎配置失败"
        )


@router.get("/", response_model=List[SearchEngineRead])
def get_search_engines_route(
    db: DBSessionDependency,
    user: UserDependency
):
    """获取用户的所有搜索引擎配置"""
    try:
        return get_search_engines(db, user.id)
    except Exception as e:
        logger.error(f"获取搜索引擎配置列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="获取搜索引擎配置列表失败"
        )


@router.get("/{search_engine_id}", response_model=SearchEngineRead)
def get_search_engine_route(
    search_engine_id: UUID,
    db: DBSessionDependency,
    user: UserDependency
):
    """获取特定的搜索引擎配置"""
    search_engine = get_search_engine_by_id(db, search_engine_id, user.id)
    if not search_engine:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="搜索引擎配置不存在"
        )
    return search_engine


@router.put("/{search_engine_id}", response_model=SearchEngineRead)
def update_search_engine_route(
    search_engine_id: UUID,
    search_engine: SearchEngineUpdate,
    db: DBSessionDependency,
    user: UserDependency
):
    """更新搜索引擎配置"""
    updated_search_engine = update_search_engine(db, search_engine_id, search_engine, user.id)
    if not updated_search_engine:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="搜索引擎配置不存在"
        )
    return updated_search_engine


@router.delete("/{search_engine_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_search_engine_route(
    search_engine_id: UUID,
    db: DBSessionDependency,
    user: UserDependency
):
    """删除搜索引擎配置"""
    success = delete_search_engine(db, search_engine_id, user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="搜索引擎配置不存在"
        )
    return None
