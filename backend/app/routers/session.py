from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
from uuid import UUID
from datetime import datetime, timezone

from sqlmodel import Session, select
from app.models import Session as ChatSession, Message
from app.dependencies import DBSessionDependency, UserDependency

router = APIRouter(prefix="/api/sessions", tags=["sessions"])

@router.post("/", response_model=ChatSession, status_code=status.HTTP_201_CREATED)
async def create_session(
    title: str,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """创建新的会话"""
    new_session = ChatSession(
        title=title,
        user_id=current_user.id
    )
    db.add(new_session)
    db.commit()
    db.refresh(new_session)
    return new_session

@router.get("/", response_model=List[ChatSession])
async def get_sessions(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """获取用户的会话列表"""
    sessions = db.exec(
        select(ChatSession)
        .where(ChatSession.user_id == current_user.id)
        .offset(skip)
        .limit(limit)
        .order_by(ChatSession.updated_at.desc())
    ).all()
    return sessions

@router.get("/{session_id}", response_model=ChatSession)
async def get_session(
    session_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """获取单个会话详情"""
    session = db.get(ChatSession, session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    return session

@router.delete("/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """删除会话"""
    session = db.get(ChatSession, session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    db.delete(session)
    db.commit()
    return None

@router.post("/{session_id}/messages", response_model=Message)
async def create_message(
    session_id: UUID,
    content: str,
    role: str,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """在会话中创建新消息"""
    # 验证会话存在且属于当前用户
    session = db.get(ChatSession, session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # 创建新消息
    new_message = Message(
        content=content,
        role=role,
        session_id=session_id
    )
    db.add(new_message)
    
    # 更新会话的更新时间
    session.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(new_message)
    return new_message

@router.get("/{session_id}/messages", response_model=List[Message])
async def get_messages(
    session_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 50
):
    """获取会话中的消息列表"""
    # 验证会话存在且属于当前用户
    session = db.get(ChatSession, session_id)
    if not session or session.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # 获取消息列表
    messages = db.exec(
        select(Message)
        .where(Message.session_id == session_id)
        .offset(skip)
        .limit(limit)
        .order_by(Message.created_at.asc())  # 按时间正序排列
    ).all()
    return messages