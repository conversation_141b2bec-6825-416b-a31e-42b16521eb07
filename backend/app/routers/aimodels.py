import logging
from typing import List
from uuid import UUID

from fastapi import APIRouter, HTTPException

from ..db.aimodel_crud import create_ai_model, get_ai_models, get_ai_model_by_id, update_ai_model, delete_ai_model
from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.aimodel import AIModelCreate, AIModelRead, AIModelUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/models", tags=["models"])

@router.post("", response_model=AIModelRead)
def create_model(
    model: AIModelCreate,
    db: DBSessionDependency,
    user: UserDependency
):
    """创建新的AI模型"""
    try:
        return create_ai_model(db, model, user.id)
    except Exception as e:
        logger.error(f"创建AI模型失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"创建AI模型失败: {str(e)}")

@router.get("", response_model=List[AIModelRead])
def get_models(
    db: DBSessionDependency,
    user: UserDependency
):
    """获取所有AI模型"""
    return get_ai_models(db, user.id)

@router.get("/{model_id}", response_model=AIModelRead)
def get_model(
    model_id: UUID,
    db: DBSessionDependency,
    user: UserDependency
):
    """获取特定AI模型"""
    model = get_ai_model_by_id(db, model_id, user.id)
    if not model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    return model

@router.put("/{model_id}", response_model=AIModelRead)
def update_model(
    model_id: UUID,
    model_update: AIModelUpdate,
    db: DBSessionDependency,
    user: UserDependency
):
    """更新AI模型"""
    updated_model = update_ai_model(db, model_id, model_update, user.id)
    if not updated_model:
        raise HTTPException(status_code=404, detail="AI模型不存在")
    return updated_model

@router.delete("/{model_id}")
def delete_model(
    model_id: UUID,
    db: DBSessionDependency,
    user: UserDependency
):
    """删除AI模型"""
    if not delete_ai_model(db, model_id, user.id):
        raise HTTPException(status_code=404, detail="AI模型不存在")
    return {"message": "AI模型已删除"}