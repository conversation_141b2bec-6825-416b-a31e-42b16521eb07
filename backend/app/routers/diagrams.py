import logging
import os
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile, Header
from sqlmodel import Session

from ..db.diagram_crud import get_diagrams, get_diagram_by_id, create_diagram, update_diagram, delete_diagram
# from ..db.models import Diagram
from ..dependencies import DBSessionDependency, SupabaseDependency, UserDependency
from ..schemas.diagram import DiagramCreate, DiagramResponse, DiagramUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["diagrams"])


@router.get("/diagrams/", response_model=List[DiagramResponse])
def get_diagrams_route(
    db: DBSessionDependency, 
    user: UserDependency
):
    if not user:
        logger.warning("用户未认证，返回空列表")
        return []
    
    logger.info(f"获取用户 {user.id} 的所有图片")
    diagrams = get_diagrams(db, user_id=user.id)
    
    # 处理 updated_at 为 None 的情况
    for diagram in diagrams:
        if diagram.updated_at is None:
            diagram.updated_at = diagram.created_at  # 使用创建时间作为更新时间
    
    logger.info(f"找到 {len(diagrams)} 张图片")
    return diagrams


@router.get("/diagram/{diagram_id}", response_model=DiagramResponse)
def get_diagram_route(diagram_id: UUID, db: DBSessionDependency, user: UserDependency):
    """根据ID获取图片"""
    db_diagram = get_diagram_by_id(db, diagram_id, user_id=user.id)
    if db_diagram is None:
        raise HTTPException(status_code=404, detail="Diagram not found")
    return db_diagram


@router.post("/diagram/upload", response_model=DiagramResponse)
async def upload_diagram(
    db: DBSessionDependency,
    user: UserDependency,
    supabase: SupabaseDependency,
    name: str,
    description: str = None,
    source: str = None,
    type: str = None,
    file: UploadFile = File(...),
):
    """上传新图片"""
    logger.info(f"开始处理图片上传请求: 用户ID={user.id}, 文件名={file.filename}")
    
    try:
        # 生成唯一文件名，避免文件名冲突
        from uuid import uuid4
        unique_filename = f"{uuid4()}-{file.filename}"
        
        # 构建存储路径
        bucket_name = "diagrams"  # 存储桶名称
        storage_path = f"{user.id}/{unique_filename}"
        logger.info(f"存储路径: {storage_path}")
        
        # 上传到Supabase Storage
        logger.info("开始上传到Supabase Storage...")
        try:
            # 读取文件内容
            file_content = await file.read()
            file_size = len(file_content)
            
            # 上传文件内容到Supabase Storage
            response = supabase.storage.from_(bucket_name).upload(
                path=storage_path,
                file=file_content,
                file_options={"content-type": file.content_type}
            )
            logger.info(f"上传到Supabase Storage成功: {response}")
        except Exception as storage_error:
            logger.error(f"上传到Supabase Storage失败: {str(storage_error)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"存储文件失败: {str(storage_error)}")
        
        # 获取图片URL
        try:
            image_url = supabase.storage.from_(bucket_name).get_public_url(storage_path)
            logger.info(f"获取到公共URL: {image_url}")
        except Exception as url_error:
            logger.error(f"获取公共URL失败: {str(url_error)}")
            raise HTTPException(status_code=500, detail=f"获取文件URL失败: {str(url_error)}")
        
        # 创建数据库记录
        diagram_data = {
            "name": name,
            "description": description,
            "source": source,
            "type": type,
            "content_type": file.content_type,
            "file_size": file_size,  # 使用计算出的文件大小
            "image_url": image_url
        }
        
        db_diagram = create_diagram(db, diagram_data, user_id=user.id)
        
        # 确保 updated_at 字段有值
        if db_diagram.updated_at is None:
            db_diagram.updated_at = db_diagram.created_at
            
        logger.info(f"数据库记录创建成功: ID={db_diagram.id}")
        return db_diagram
        
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"图片上传过程中发生未知错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"图片上传失败: {str(e)}")


@router.put("/diagram/{diagram_id}", response_model=DiagramResponse)
def update_diagram_route(
    diagram_id: UUID,
    diagram_update: DiagramUpdate,
    db: DBSessionDependency,
    user: UserDependency,
):
    """更新图片信息"""
    db_diagram = update_diagram(db, diagram_id, diagram_update, user_id=user.id)
    if db_diagram is None:
        raise HTTPException(status_code=404, detail="Diagram not found")
    return db_diagram


@router.delete("/diagram/{diagram_id}")
async def delete_diagram_route(
    diagram_id: UUID,
    db: DBSessionDependency,
    user: UserDependency,
    supabase: SupabaseDependency,
):
    """删除图片"""
    db_diagram = get_diagram_by_id(db, diagram_id, user_id=user.id)
    if db_diagram is None:
        raise HTTPException(status_code=404, detail="Diagram not found")
    
    # 从存储中删除文件
    try:
        # 从image_url中提取存储路径
        storage_path = db_diagram.image_url.split("/")[-1]
        supabase.storage.from_("diagrams").remove([f"{user.id}/{storage_path}"])
    except Exception as e:
        logger.error("Error deleting file from storage: %s", e)
    
    # 从数据库中删除记录
    success = delete_diagram(db, diagram_id, user_id=user.id)
    if not success:
        raise HTTPException(status_code=500, detail="Error deleting diagram")
    
    return {"detail": "Diagram deleted successfully"}