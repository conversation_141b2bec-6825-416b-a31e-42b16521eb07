import logging
import os
from datetime import datetime
from sys import prefix
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Header, Security
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel

from app.models import User, Folder
from ..dependencies import DBSessionDependency, SupabaseDependency
from ..schemas.auth import UserSign

import random

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth",  tags=["auth"])

# 刷新 token 的请求模型
class RefreshTokenRequest(BaseModel):
    refresh_token: str


@router.post("/token")
async def login_for_swagger(
    supabase: SupabaseDependency, form_data: OAuth2PasswordRequestForm = Depends()
):
    """Swagger specific route to get the JWT token. To be used with
    the "Authorize" button in the Swagger UI."""
    if os.environ.get("DEV_ENV") != "dev":
        raise HTTPException(
            status_code=404, detail="This route is only available in dev."
        )
    try:
        # Use Supabase to authenticate the user
        response = supabase.auth.sign_in_with_password(
            {"email": form_data.username, "password": form_data.password}
        )
        # Return the JWT token
        return {"access_token": response.session.access_token, "token_type": "bearer"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/sign_up")
def sign_up(user: UserSign, supabase: SupabaseDependency, db: DBSessionDependency):
    try:
        auth_response = supabase.auth.sign_up(
            {"email": user.email, "password": user.password}
        )
    except Exception as e:
        logger.error("Error during signup %s", e)
        raise HTTPException(status_code=400, detail="Error during signup")

    # 创建用户并初始化基本信息
    db_user = User(
        id=auth_response.user.id,
        email=user.email,  # 从注册信息中获取邮箱
        username=user.email.split('@')[0],  # 默认使用邮箱前缀作为用户名
        display_name=user.email.split('@')[0],  # 默认显示名
        is_active=False,  # 默认设置为未激活状态，需要管理员激活
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

    logger.warning("Mail verification is not checked !")
    try:
        # 添加用户到数据库
        db.add(db_user)
        db.commit()
        db.refresh(db_user)  # 刷新以获取数据库生成的值

        # 为用户创建默认根文件夹，使用用户名称个性化文件夹
        username = db_user.username or db_user.email.split('@')[0]
        display_name = db_user.display_name or username

        default_folder = Folder(
            name=f"{display_name}的工作区",  # 使用用户显示名称个性化文件夹名称
            desc=f"{display_name}的个人空间 - 创建于{datetime.now().strftime('%Y-%m-%d')}",  # 包含用户名和创建日期
            color="#4A90E2",  # 默认颜色 - 蓝色
            user_id=db_user.id,  # 关联到新创建的用户
            created_at=datetime.now()
        )

        # 添加默认文件夹到数据库
        db.add(default_folder)
        db.commit()
        logger.info(f"Created default root folder for user {db_user.id}")

    except Exception as e:
        logger.error("Error during user creation or folder creation %s", e)
        # 回滚事务
        db.rollback()
        raise HTTPException(status_code=400, detail="Error during user creation")

    return {"message": "User signed up successfully, waiting for confirmation."}

# TODO: 返回详细信息（id, email,name, token）
@router.post("/sign_in")
def sign_in(user: UserSign, supabase: SupabaseDependency, db: DBSessionDependency):
    try:
        # 打印更多调试信息
        logger.info(f"Attempting to sign in user with email: {user.email}")

        auth_response = supabase.auth.sign_in_with_password(
            {"email": user.email, "password": user.password}
        )

        # 打印认证响应信息
        logger.info(f"Auth response received, user ID: {auth_response.user.id}")

        # 更新用户的最后登录时间
        user_id = auth_response.user.id
        db_user = db.query(User).filter(User.id == user_id).first()

        if db_user:
            logger.info(f"User found in database: {db_user.username}")
            db_user.last_login = datetime.now()
            db_user.updated_at = datetime.now()
            db.commit()

            # 返回更详细的用户信息和认证令牌
            return {
                "message": "User signed in successfully",
                "token": auth_response.session.access_token,
                "refresh_token": auth_response.session.refresh_token,
                "user": {
                    "id": db_user.id,
                    "email": db_user.email,
                    "username": db_user.username,
                    "display_name": db_user.display_name,
                    "is_admin": db_user.role == "admin",  # 根据role字段判断是否为管理员
                    "role": db_user.role,  # 同时返回原始角色值
                    "is_active": db_user.is_active,  # 返回用户激活状态
                    "avatar_url": db_user.thumbnail if hasattr(db_user, 'thumbnail') else None
                }
            }
        else:
            logger.warning(f"User with ID {user_id} authenticated but not found in database")

    except Exception as e:
        # 提供更详细的错误信息
        error_msg = str(e)
        logger.error(f"Error during signin: {error_msg}")
        raise HTTPException(status_code=400, detail=f"登录失败: {error_msg}")

    # 如果没有找到用户记录但认证成功，返回基本信息
    logger.info("Returning basic user info (no DB record found)")
    return {
        "message": "User signed in successfully",
        "token": auth_response.session.access_token,
        "refresh_token": auth_response.session.refresh_token,
        "user": {
            "id": auth_response.user.id,
            "email": auth_response.user.email
        }
    }

# 添加 OAuth2 安全机制
security = HTTPBearer(auto_error=False)  # 设置 auto_error=False 以便我们可以自定义错误消息

# 根据前端传递的token，调用supabase的函数获取用户信息
@router.get("/me")
async def get_current_user(
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security)
):
    # 检查是否提供了认证凭据
    if credentials is None:
        logger.warning("尝试访问 /me 接口但未提供认证令牌")
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 直接从认证凭据中获取 token
    token = credentials.credentials

    try:
        # 使用 Supabase 验证 token 并获取用户信息
        user_response = supabase.auth.get_user(token)
        user_id = user_response.user.id

        # 从数据库获取用户详细信息
        db_user = db.query(User).filter(User.id == user_id).first()

        if not db_user:
            logger.warning(f"User with ID {user_id} exists in auth system but not in database")
            raise HTTPException(status_code=404, detail="用户在系统中不存在，请联系管理员")

        # 返回用户信息
        return {
            "id": db_user.id,
            "email": db_user.email,
            "username": db_user.username,
            "display_name": db_user.display_name,
            "is_admin": db_user.role == "admin",
            "role": db_user.role,
            "is_active": db_user.is_active,
            "avatar_url": db_user.thumbnail if hasattr(db_user, 'thumbnail') else None,
            "created_at": db_user.created_at,
            "updated_at": db_user.updated_at,
            "last_login": db_user.last_login
        }
    except Exception as e:
        logger.error(f"验证用户令牌时出错: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"无效的认证令牌: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )



    # 从用户表中，返回随机的用户列表信息
@router.get("/random_users")
async def get_random_users(
    db: DBSessionDependency,
    min_limit: int = 3,  # 最少返回3个用户
    max_limit: int = 5   # 最多返回5个用户
):
    try:
        # 从数据库中获取所有用户
        all_users = db.query(User).all()

        # 随机选择3到5个用户
        selected_users = random.sample(all_users, k=min(len(all_users), random.randint(min_limit, max_limit)))

        return [{
            "id": user.id,
            "username": user.username,
            "display_name": user.display_name,
            "avatar_url": user.thumbnail if hasattr(user, 'thumbnail') else None,
        } for user in selected_users]
    except Exception as e:
        logger.error(f"获取随机用户列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail="获取用户列表失败")

@router.post("/refresh")
async def refresh_token(
    request: RefreshTokenRequest,
    supabase: SupabaseDependency
):
    """
    刷新认证 token
    """
    try:
        # 使用 Supabase 刷新 token
        refresh_response = supabase.auth.refresh_session(request.refresh_token)

        # 获取新的 token 和 refresh_token
        new_token = refresh_response.session.access_token
        new_refresh_token = refresh_response.session.refresh_token

        return {
            "token": new_token,
            "refresh_token": new_refresh_token,
            "message": "Token 刷新成功"
        }
    except Exception as e:
        logger.error(f"刷新 token 失败: {str(e)}")
        raise HTTPException(status_code=401, detail=f"刷新 token 失败: {str(e)}")
