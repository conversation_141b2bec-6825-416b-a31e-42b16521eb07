from fastapi import UploadFile, HTTPException, status
import os
import uuid
from ..dependencies import get_supabase_client

async def upload_file_to_storage(
    file: UploadFile, 
    bucket: str, 
    path: str
) -> str:
    """
    上传文件到Supabase Storage
    
    Args:
        file: 要上传的文件
        bucket: 存储桶名称
        path: 存储路径
        
    Returns:
        str: 文件的公共URL
    """
    try:
        # 获取Supabase客户端
        supabase = get_supabase_client()
        
        # 读取文件内容
        file_content = await file.read()
        
        # 上传到Supabase Storage
        response = supabase.storage.from_(bucket).upload(
            path=path,
            file=file_content,
            file_options={"content-type": file.content_type}
        )
        
        # 获取公共URL
        file_url = supabase.storage.from_(bucket).get_public_url(path)
        
        return file_url
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )