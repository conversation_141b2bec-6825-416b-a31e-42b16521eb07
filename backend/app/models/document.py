from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import Field, Relationship, SQLModel

# 只在类型检查时导入
if TYPE_CHECKING:
    from .user import User
    from .folder import Folder
    from .diagram import Diagram

class Document(SQLModel, table=True):
    __tablename__ = "document"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    title: str
    description: Optional[str] = None
    type: str
    content_type: str
    file_path: str
    markdown_url: Optional[str] = None
    file_size: Optional[int] = None
    folder_id: Optional[UUID] = Field(default=None, foreign_key="folder.id", nullable=True)
    user_id: UUID = Field(foreign_key="user.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None, sa_column_kwargs={"onupdate": datetime.utcnow})
    
    # 关系 - 确保 back_populates 属性名与 Folder 模型中的一致
    user: "User" = Relationship(back_populates="documents")
    folder: Optional["Folder"] = Relationship(back_populates="documents")
    diagrams: List["Diagram"] = Relationship(back_populates="document")
