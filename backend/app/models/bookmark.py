from datetime import datetime
from typing import Optional, TYPE_CHECKING, Any
from uuid import UUID, uuid4

from sqlmodel import Field, Relationship, SQLModel

# 只在类型检查时导入
if TYPE_CHECKING:
    from .user import User
    from .folder import Folder

class Bookmark(SQLModel, table=True):
    __tablename__ = "bookmark"

    id: UUID = Field(default_factory=uuid4, primary_key=True)

    # 基本信息
    title: Optional[str] = Field(default=None)  # 标题
    url: Optional[str] = Field(default=None)    # 链接，对于想法可以为空
    desc: Optional[str] = Field(default=None)   # 简短描述
    content: Optional[str] = Field(default=None)  # 详细内容，可以是Markdown格式
    summary: Optional[str] = Field(default=None)  # 摘要或总结
    
    # 分类和标签
    bookmark_type: str = Field(default="bookmark")  # 类型: bookmark, idea, digest, code, tool等
    tags: Optional[str] = Field(default=None)       # 标签，以逗号分隔
    is_ticked: bool = Field(default=False)          # 是否标记为重要
    content_type: Optional[str] = Field(default=None)  # 内容MIME类型
    
    # 评分和可见性
    rating: Optional[float] = Field(default=0.0)    # 评分
    is_public: bool = Field(default=False)          # 是否公开
    
    # 关联
    folder_id: Optional[UUID] = Field(
        default=None, foreign_key="folder.id", nullable=True
    )
    user_id: UUID = Field(foreign_key="user.id", nullable=False)
    
    # 时间戳
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None, sa_column_kwargs={"onupdate": datetime.utcnow})

    # 关系 - 使用字符串引用而不是运行时导入
    user: "User" = Relationship(back_populates="bookmarks")
    folder: Optional["Folder"] = Relationship(back_populates="bookmarks")