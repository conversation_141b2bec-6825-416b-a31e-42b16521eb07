from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, UniqueConstraint

class SearchEngine(SQLModel, table=True):
    __tablename__ = "search_engine"
    
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str = Field(max_length=128)  # 引擎名称
    provider: str = Field(max_length=128)  # 提供商
    url: str = Field(max_length=1024)  # API URL
    token: str = Field(max_length=5120)  # API Token
    available_credit: Optional[float] = Field(default=0.0)  # 可用额度
    note: Optional[str] = Field(default=None, max_length=1024)  # 备注
    is_active: bool = Field(default=True)  # 是否激活
    user_id: UUID = Field(foreign_key="user.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow, sa_column_kwargs={"onupdate": datetime.utcnow})

    class Config:
        table_args = (UniqueConstraint('name', 'user_id', name='unique_search_engine_name_user'),)
