from datetime import datetime, timezone
from typing import List, Optional
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, Relationship

class Session(SQLModel, table=True):
    __tablename__ = "sessions"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="user.id")
    title: str = Field(max_length=255, nullable=False)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 关系
    messages: List["Message"] = Relationship(back_populates="session")

class Message(SQLModel, table=True):
    __tablename__ = "messages"

    # TODO： 
    # 用户在一次对话中可能引用 bookmark，diagram，document，post等内容
    # 这些内容的引用需要在message中记录
    # reference_id: UUID = Field(foreign_key="reference.id")
    # reference_type: str = Field(max_length=50, nullable=False)

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    role: str = Field(max_length=50, nullable=False)
    content: str = Field(nullable=False)
    session_id: UUID = Field(foreign_key="sessions.id")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # 关系
    session: Optional[Session] = Relationship(back_populates="messages")