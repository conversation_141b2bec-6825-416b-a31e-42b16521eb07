from datetime import datetime
from typing import Optional, TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import Field, Relationship, SQLModel

# 只在类型检查时导入
if TYPE_CHECKING:
    from .user import User
    from .folder import Folder
    from .document import Document

class Diagram(SQLModel, table=True):
    __tablename__ = "diagram"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str = Field(max_length=255)
    description: Optional[str] = Field(default=None)
    source: Optional[str] = Field(default=None, max_length=255)
    type: Optional[str] = Field(default=None, max_length=50)
    content_type: str = Field(max_length=50)
    file_size: int
    image_url: str = Field(max_length=255)
    document_id: Optional[UUID] = Field(default=None, foreign_key="document.id", nullable=True)
    folder_id: Optional[UUID] = Field(default=None, foreign_key="folder.id", nullable=True)
    user_id: UUID = Field(foreign_key="user.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None, sa_column_kwargs={"onupdate": datetime.utcnow})
    
    # 关系 - 使用字符串引用
    user: "User" = Relationship(back_populates="diagrams")
    folder: Optional["Folder"] = Relationship(back_populates="diagrams")
    document: Optional["Document"] = Relationship(back_populates="diagrams")