from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import Field, Relationship, SQLModel

# 只在类型检查时导入
if TYPE_CHECKING:
    from .bookmark import Bookmark
    from .folder import Folder
    from .diagram import Diagram
    from .document import Document
    # 如果不需要 AIModel 关系，应该注释掉这行
    # from .aimodel import AIModel

class User(SQLModel, table=True):
    __tablename__ = "user"

    id: UUID = Field(default_factory=uuid4, primary_key=True, index=True)

    # 基本信息
    username: Optional[str] = Field(default=None)
    display_name: Optional[str] = Field(default=None)
    email: Optional[str] = Field(default=None)
    thumbnail: Optional[str] = Field(default=None)  # 用户头像URL
    bio: Optional[str] = Field(default=None)  # 用户简介
    
    # 组织信息
    team_id: Optional[UUID] = Field(default=None)  # 所属团队ID
    role: Optional[str] = Field(default="user")  # 用户角色: admin, user等
    
    # 状态信息
    is_active: bool = Field(default=True)
    last_login: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None, sa_column_kwargs={"onupdate": datetime.utcnow})

    # 关系 - 使用字符串引用而不是运行时导入
    bookmarks: List["Bookmark"] = Relationship(back_populates="user")
    folders: List["Folder"] = Relationship(back_populates="user")
    diagrams: List["Diagram"] = Relationship(back_populates="user")
    documents: List["Document"] = Relationship(back_populates="user")
    # 注释掉或删除这行，因为 AIModel 模型可能不存在或没有正确定义
    # aimodels: List["AIModel"] = Relationship(back_populates="user")
