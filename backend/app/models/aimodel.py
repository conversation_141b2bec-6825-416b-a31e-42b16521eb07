from datetime import datetime
from typing import Optional
from uuid import UUID, uuid1
from sqlmodel import SQLModel, Field, UniqueConstraint

class AIModel(SQLModel, table=True):
    __tablename__ = "aimodel"  # 修改表名为 aimodel
    
    id: UUID = Field(default_factory=uuid1, primary_key=True)
    name: str = Field(max_length=128)
    model_type: str = Field(max_length=128)
    model_name: str = Field(max_length=128)
    provider: str = Field(max_length=128)
    credential: str = Field(max_length=5120)
    base_url: Optional[str] = Field(default=None, max_length=1024)  # 新增字段
    user_id: UUID = Field(foreign_key="user.id")
    create_time: datetime = Field(default_factory=datetime.utcnow)
    update_time: datetime = Field(default_factory=datetime.utcnow, sa_column_kwargs={"onupdate": datetime.utcnow})

    class Config:
        table_args = (UniqueConstraint('name', 'user_id', name='unique_name_user'),)