from datetime import datetime
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID, uuid4

from sqlmodel import Field, Relationship, SQLModel

# 只在类型检查时导入
if TYPE_CHECKING:
    from .bookmark import Bookmark
    from .document import Document
    from .diagram import Diagram
    from .user import User

class Folder(SQLModel, table=True):
    __tablename__ = "folder"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str
    desc: Optional[str] = None
    color: Optional[str] = None
    parent_folder: Optional[UUID] = Field(default=None, foreign_key="folder.id", nullable=True)

    user_id: UUID = Field(foreign_key="user.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # 关系
    user: "User" = Relationship(back_populates="folders")
    bookmarks: List["Bookmark"] = Relationship(back_populates="folder")
    documents: List["Document"] = Relationship(back_populates="folder")
    diagrams: List["Diagram"] = Relationship(back_populates="folder")

    # 修复自引用关系
    # 使用字符串形式的 remote_side 参数
    parent: Optional["Folder"] = Relationship(
        back_populates="children",
        sa_relationship_kwargs={"remote_side": "Folder.id"}
    )
    children: List["Folder"] = Relationship(back_populates="parent")
