from typing import Dict, List, Optional, Any
from uuid import UUID
from sqlmodel import Session, select
from ..models import Document

def create_document(db: Session, document_data: Dict[str, Any], user_id: UUID) -> Document:
    """创建新文档"""
    document = Document(**document_data, user_id=user_id)
    db.add(document)
    db.commit()
    db.refresh(document)
    return document


def get_document(db: Session, document_id: UUID) -> Optional[Document]:
    """根据ID获取单个文档"""
    return db.query(Document).filter(Document.id == document_id).first()


def get_document_by_id(db: Session, document_id: UUID, user_id: UUID) -> Optional[Document]:
    """根据ID获取文档"""
    return db.exec(
        select(Document).where(Document.id == document_id, Document.user_id == user_id)
    ).first()


def get_documents(db: Session, user_id: UUID) -> List[Document]:
    """获取用户的所有文档"""
    return db.exec(select(Document).where(Document.user_id == user_id)).all()


def update_document(db: Session, document_id: UUID, document_data: Dict[str, Any], user_id: UUID) -> Optional[Document]:
    """更新文档"""
    document = get_document_by_id(db, document_id, user_id)
    if not document:
        return None
    
    for key, value in document_data.items():
        if hasattr(document, key) and value is not None:
            setattr(document, key, value)
    
    db.add(document)
    db.commit()
    db.refresh(document)
    return document


def delete_document(db: Session, document_id: UUID, user_id: UUID) -> bool:
    """删除文档"""
    document = get_document_by_id(db, document_id, user_id)
    if not document:
        return False
    
    db.delete(document)
    db.commit()
    return True