from typing import List, Optional
from uuid import UUID

from sqlmodel import Session, select

from app.models import SearchEngine
from app.schemas.search_engine import SearchEngineCreate, SearchEngineUpdate


def create_search_engine(db: Session, search_engine: SearchEngineCreate, user_id: UUID) -> SearchEngine:
    """创建新的搜索引擎配置"""
    db_search_engine = SearchEngine(
        name=search_engine.name,
        provider=search_engine.provider,
        url=search_engine.url,
        token=search_engine.token,
        available_credit=search_engine.available_credit,
        note=search_engine.note,
        is_active=search_engine.is_active,
        user_id=user_id
    )
    db.add(db_search_engine)
    db.commit()
    db.refresh(db_search_engine)
    return db_search_engine


def get_search_engines(db: Session, user_id: UUID) -> List[SearchEngine]:
    """获取用户的所有搜索引擎配置"""
    return db.exec(select(SearchEngine).where(SearchEngine.user_id == user_id)).all()


def get_search_engine_by_id(db: Session, search_engine_id: UUID, user_id: UUID) -> Optional[SearchEngine]:
    """根据ID获取特定的搜索引擎配置"""
    return db.exec(
        select(SearchEngine)
        .where(SearchEngine.id == search_engine_id)
        .where(SearchEngine.user_id == user_id)
    ).first()


def update_search_engine(
    db: Session, search_engine_id: UUID, search_engine: SearchEngineUpdate, user_id: UUID
) -> Optional[SearchEngine]:
    """更新搜索引擎配置"""
    db_search_engine = get_search_engine_by_id(db, search_engine_id, user_id)
    if not db_search_engine:
        return None

    # 更新非空字段
    update_data = search_engine.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_search_engine, key, value)

    db.add(db_search_engine)
    db.commit()
    db.refresh(db_search_engine)
    return db_search_engine


def delete_search_engine(db: Session, search_engine_id: UUID, user_id: UUID) -> bool:
    """删除搜索引擎配置"""
    db_search_engine = get_search_engine_by_id(db, search_engine_id, user_id)
    if not db_search_engine:
        return False

    db.delete(db_search_engine)
    db.commit()
    return True
