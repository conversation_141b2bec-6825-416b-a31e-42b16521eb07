from typing import List
from uuid import <PERSON><PERSON>D
from fastapi import HTTPException
from sqlmodel import Session, select
from ..models import Folder
from ..schemas.folder import Folder<PERSON>ontent

def get_folder_by_id(db: Session, folder_id: UUID, user_id: UUID) -> FolderContent:
    """Returns a folder and its content by id."""
    db_folder = db.exec(
        select(Folder).where(Folder.id == folder_id, Folder.user_id == user_id)
    ).first()
    if not db_folder:
        raise HTTPException(status_code=404, detail="Folder not found")

    return FolderContent(folder=db_folder, bookmarks=db_folder.bookmarks)


def get_folders(db: Session, user_id: UUID) -> List[Folder]:
    """Returns all folders for a user."""
    folders = db.exec(select(Folder).where(Folder.user_id == user_id)).all()
    return folders