from typing import List, Optional
from uuid import UUID
from sqlmodel import Session, select
from ..models import AIModel
from ..schemas.aimodel import AIModelCreate, AIModelUpdate

def create_ai_model(db: Session, model_data: AIModelCreate, user_id: UUID) -> AIModel:
    """创建新的AI模型"""
    db_model = AIModel(**model_data.model_dump(), user_id=user_id)
    db.add(db_model)
    db.commit()
    db.refresh(db_model)
    return db_model

def get_ai_models(db: Session, user_id: UUID) -> List[AIModel]:
    """获取用户的所有AI模型"""
    return db.exec(select(AIModel).where(AIModel.user_id == user_id)).all()

def get_ai_model_by_id(db: Session, model_id: UUID, user_id: UUID) -> Optional[AIModel]:
    """根据ID获取AI模型"""
    return db.exec(
        select(AIModel).where(AIModel.id == model_id, AIModel.user_id == user_id)
    ).first()

def update_ai_model(db: Session, model_id: UUID, model_data: AIModelUpdate, user_id: UUID) -> Optional[AIModel]:
    """更新AI模型"""
    db_model = get_ai_model_by_id(db, model_id, user_id)
    if not db_model:
        return None
    
    update_data = model_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_model, key, value)
    
    db.add(db_model)
    db.commit()
    db.refresh(db_model)
    return db_model

def delete_ai_model(db: Session, model_id: UUID, user_id: UUID) -> bool:
    """删除AI模型"""
    db_model = get_ai_model_by_id(db, model_id, user_id)
    if not db_model:
        return False
    
    db.delete(db_model)
    db.commit()
    return True