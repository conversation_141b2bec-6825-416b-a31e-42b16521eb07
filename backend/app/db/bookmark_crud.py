import logging
from uuid import UUID
from fastapi import HTTPException
from sqlmodel import Session, select
from ..models import Bookmark

def get_bookmark_by_id(db: Session, bookmark_id: UUID, user_id: UUID) -> Bookmark:
    """Returns a bookmark by id and user id."""
    logging.info(f"查询书签: ID={bookmark_id}, 用户ID={user_id}")

    try:
        query = select(Bookmark).where(Bookmark.id == bookmark_id, Bookmark.user_id == user_id)
        logging.info(f"SQL查询: {query}")

        db_bookmark = db.exec(query).first()

        if db_bookmark:
            logging.info(f"找到书签: {db_bookmark}")
        else:
            logging.warning(f"未找到书签: ID={bookmark_id}, 用户ID={user_id}")
            raise HTTPException(status_code=404, detail="Bookmark not found")

        return db_bookmark
    except Exception as e:
        logging.error(f"查询书签时出错: {e}")
        raise