from pydantic import BaseModel
from sqlmodel import Session, SQLModel, select


######################################################
# Generic CRUD operations
######################################################


import logging

def update_db_element(
    db: Session, original_element: SQLModel, element_update: BaseModel
) -> BaseModel:
    """Updates an element in database.
    Note that it doesn't take care of user ownership.
    """
    logging.info(f"更新数据库元素: {original_element}")
    update_data = element_update.model_dump(exclude_unset=True)
    logging.info(f"更新数据: {update_data}")

    for key, value in update_data.items():
        logging.info(f"设置属性: {key} = {value}")
        setattr(original_element, key, value)

    try:
        db.add(original_element)
        logging.info("添加到会话成功")
        db.commit()
        logging.info("提交事务成功")
        db.refresh(original_element)
        logging.info("刷新对象成功")
    except Exception as e:
        logging.error(f"数据库操作失败: {e}")
        db.rollback()
        raise

    return original_element


def delete_db_element(db: Session, element: SQLModel):
    """Deletes an element from database."""
    db.delete(element)
    db.commit()

