FROM python:3.12-slim

WORKDIR /app

# 复制项目文件
COPY pyproject.toml alembic.ini ./
COPY alembic ./alembic
COPY app ./app

# 复制 Docker 环境变量文件
COPY .env.docker .env

# 配置pip使用国内镜像源
RUN pip config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple

# 安装基础依赖
RUN pip install --no-cache-dir setuptools wheel

# 安装项目依赖
RUN pip install --no-cache-dir fastapi[standard] sqlmodel supabase python-dotenv alembic psycopg2-binary

# 运行数据库迁移
RUN alembic upgrade head

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
