FROM node:20-alpine as build

WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 lock 文件
COPY package.json pnpm-lock.yaml ./

# 使用 pnpm 安装依赖
RUN pnpm install

# 复制应用代码
COPY . .

# 复制 Docker 环境变量文件
COPY .env.docker .env.production

# 构建应用 (跳过TypeScript检查)
RUN pnpm vite build

# 生产阶段 - 只需要构建产物
FROM scratch as production

# 复制构建产物
COPY --from=build /app/dist /dist

# 开发阶段 - 用于热重载开发环境
FROM node:20-alpine as development

WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 lock 文件
COPY package.json pnpm-lock.yaml ./

# 使用 pnpm 安装依赖
RUN pnpm install

# 复制 Docker 环境变量文件
COPY .env.docker .env.development

# 暴露 Vite 开发服务器端口
EXPOSE 5173

# 启动开发服务器
CMD ["pnpm", "dev", "--host", "0.0.0.0"]
