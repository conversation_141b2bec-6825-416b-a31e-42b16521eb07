import { useState, useEffect } from 'react';
import { Route, Switch } from 'wouter';
import { LoginDialog } from "@/components/auth/LoginDialog";
import { useAuth } from "@/utils/AuthContext";
import { Home } from '@/pages/Home';
import { Toaster } from 'sonner';
import { WeiboPage } from './pages/WeiboPage';
import { Diagrams } from './pages/Diagrams';
import { Documents } from './pages/Documents';
import { Academic } from './pages/Academic';
import { ThoughtsPage } from './pages/ThoughtsPage';
import { ChatHistory } from './pages/ChatHistory';
import { SettingsPage } from './pages/SettingsPage';
import { WelcomePage } from './pages/WelcomePage';
import { Card, CardContent } from './components/ui/card';
import { AlertCircle, Loader2 } from 'lucide-react';
import { TokenRefresher } from './components/auth/TokenRefresher';

function App() {
  const { isAuthenticated, user, loading } = useAuth();
  const [showLogin, setShowLogin] = useState(false);
  const [appReady, setAppReady] = useState(false);

  // 使用 useEffect 来处理登录对话框的显示逻辑
  useEffect(() => {
    // 只有当认证状态确定后（loading 完成）才更新 showLogin 和 appReady
    if (!loading) {
      setShowLogin(!isAuthenticated);
      setAppReady(true);
    }
  }, [isAuthenticated, loading]);

  // 显示加载状态，避免闪烁
  if (loading || !appReady) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
      </div>
    );
  }

  // 如果未登录，显示登录对话框
  if (!isAuthenticated) {
    return (
      <>
        <Toaster position="top-right" richColors />
        <LoginDialog open={showLogin} onOpenChange={setShowLogin} />
      </>
    );
  }

  // 如果已登录但用户未激活，显示欢迎页面
  if (user && !user.is_active) {
    return (
      <>
        <Toaster position="top-right" richColors />
        <TokenRefresher />
        <WelcomePage />
      </>
    );
  }

  // 用户已登录且已激活，显示正常应用内容
  return (
    <>
      <Toaster position="top-right" richColors />
      {/* 添加 TokenRefresher 组件，用于自动刷新 token */}
      <TokenRefresher />
      <Switch>
        <Route path="/" component={Home} />
        <Route path="/weibo" component={WeiboPage} />
        <Route path="/diagrams" component={Diagrams} />
        <Route path="/documents" component={Documents} />
        <Route path="/academic" component={Academic} />
        <Route path="/thoughts" component={ThoughtsPage} />
        <Route path="/chat-history" component={ChatHistory} />
        <Route path="/settings" component={SettingsPage} />
        <Route component={NotFound} />
        {/* Add more routes here */}
      </Switch>
    </>
  );
}

function NotFound() {
  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-background">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="pt-6">
          <div className="flex mb-4 gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <h1 className="text-2xl font-bold">404 Page Not Found</h1>
          </div>

          <p className="mt-4 text-muted-foreground">
            The page you're looking for doesn't exist.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

export default App;
