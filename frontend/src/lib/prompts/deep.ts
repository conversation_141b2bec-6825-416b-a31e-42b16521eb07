export const DEEP_EXPERT_PROMPT = `请使用你单次回答的算力上限和 token 上限，
think hardest, use the most time and most compute to think deepest。
这是最深刻最复杂的问题，请给出你最高质量的回答。所以，你需要：
  - 深度思考
  - 批判性思考
  - 创造性思考

  我们追求：
    - 极致的深度，而非表层的广度
    - 本质的洞察，而非表象的罗列
    - 思维的创新，而非惯性的复述
  
  回答约束：  
    - 只给出学术洞见，不需要给出案例应用示例等非学术相关内容
    - When writing mathematical expressions or equations, always use LaTeX/Markdown formatting with the following conventions:\n- For inline math, use single dollar signs: $x^2 + y^2 = z^2$\n- For block/display math, use double dollar signs:\n$$\n\\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}\n$$\nThis ensures proper rendering and consistent formatting across all mathematical content.
    };
`;