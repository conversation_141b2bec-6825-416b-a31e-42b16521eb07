// TODO: 废弃

// 提取文档内容的工具函数
export const extractAbstractFromMarkdown = (markdown: string | undefined): string => {
  if (!markdown) return '';

  // 只在文档开始的 1000 个字符内查找摘要起始位置
  const startPart = markdown.slice(0, 1000);
  const abstractStartPattern = /(?:abstract[\s]*[:：]|abstract\n+|摘要[\s]*[:：]|摘要\n+)/i;
  const startMatch = startPart.match(abstractStartPattern);
  
  if (!startMatch) return '';
  
  // 从匹配位置开始截取后续内容
  const contentAfterAbstract = markdown.slice(startMatch.index);
  
  // 查找摘要结束位置的多种可能模式
  const endPatterns = [
    /\n\s*(?:keywords?[:：]|关键词[:：])/i,  // 关键词标记
    /\n\s*(?:introduction|引言)[\s]*[:：]/i,  // 引言标记
    /\n\s*(?:1[\s\.、]|I[\s\.、]|一[\s\.、])/,  // 各种形式的第一章节标记
    /\n\s*(?=\d+[\s\.、])/,  // 任意数字章节标记
    /\n\s*参考文献/,  // 参考文献标记
    /\n\s*references/i,  // 英文参考文献标记
    /\n\n\n/,  // 连续三个换行
  ];

  let endIndex = -1;
  for (const pattern of endPatterns) {
    const endMatch = contentAfterAbstract.match(pattern);
    if (endMatch?.index !== undefined && (endIndex === -1 || endMatch.index < endIndex)) {
      endIndex = endMatch.index;
    }
  }

  // 如果没找到结束标记，限制在合理范围内（如500字符）
  if (endIndex === -1) {
    endIndex = Math.min(500, contentAfterAbstract.length);
  }

  // 提取并清理文本
  const abstractText = contentAfterAbstract.slice(startMatch[0].length, endIndex)
    .trim()
    .replace(/^\n+|\n+$/g, '')
    .replace(/\n(?!\n)/g, ' ')
    .replace(/\n{2,}/g, '\n')
    .replace(/\s+/g, ' ')
    .trim();

  return abstractText;
};

// 后续可以添加更多文档内容提取函数
export const documentExtractUtils = {
  extractAbstract: extractAbstractFromMarkdown,
};