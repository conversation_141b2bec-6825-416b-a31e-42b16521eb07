/**
 * API Configuration
 *
 * This file centralizes all API endpoints and configuration settings.
 * Use this instead of hardcoding URLs throughout the application.
 */

// Base API configuration
// 不要使用硬编码的URL，使用相对路径
const API_BASE = import.meta.env.VITE_API_BASE || '/api';

// 打印环境变量
console.log('API配置初始化 - 环境变量:', {
  VITE_API_BASE: import.meta.env.VITE_API_BASE,
  VITE_API_VERSION: import.meta.env.VITE_API_VERSION,
  MODE: import.meta.env.MODE
});

// Construct the base API path
// 使用相对路径，不要硬编码URL
const API_PATH = `${API_BASE}`;

// 打印API路径
console.log('API路径:', API_PATH);

// 确保所有API请求都使用相对路径，不要硬编码localhost:8000
if (API_PATH.includes('localhost:8000')) {
  console.error('警告: API_PATH 包含硬编码的 localhost:8000，这可能会导致问题');
}

// LLM Configuration - no default values needed as they are stored in the database
const LLM_HOST = import.meta.env.VITE_LLM_HOST || '';
const LLM_MODEL = import.meta.env.VITE_LLM_MODEL || '';

// Document Services
const MINERU_API_URL = import.meta.env.VITE_MINERU_API_URL || '';
const MINERU_API_KEY = import.meta.env.VITE_MINERU_API_KEY || '';
const PDF_SERVICE_URL = import.meta.env.VITE_PDF_SERVICE_URL || '/pdf-service';

// No need for a separate SEARCH_API_URL variable

// API Endpoints
export const API = {
  // Base URLs
  BASE_URL: API_BASE,
  API_PATH,

  // Auth endpoints
  AUTH: {
    SIGN_IN: `${API_PATH}/auth/sign_in`,
    SIGN_UP: `${API_PATH}/auth/sign_up`,
    TOKEN: `${API_PATH}/auth/token`,
    ME: `${API_PATH}/auth/me`,
    REFRESH: `${API_PATH}/auth/refresh`,
    RANDOM_USERS: `${API_PATH}/auth/random_users`,
  },

  // User endpoints
  USER: {
    LIST: `${API_PATH}/users`,
    PROFILE: (userId: string) => `${API_PATH}/users/${userId}`,
    STATS: (userId: string) => `${API_PATH}/users/${userId}/stats`,
  },

  // Bookmark endpoints
  BOOKMARK: {
    LIST: `${API_PATH}/bookmark`,
    DETAIL: (id: string) => `${API_PATH}/bookmark/${id}`,
    CREATE: `${API_PATH}/bookmark`,
  },

  // Document endpoints
  DOCUMENTS: {
    LIST: `${API_PATH}/documents`,
    DETAIL: (id: string) => `${API_PATH}/documents/${id}`,
    CONVERT: `${API_PATH}/markitdown/document`,
    UPLOAD: `${API_PATH}/document/upload`,
  },

  // Diagram endpoints
  DIAGRAMS: {
    LIST: `${API_PATH}/diagrams`,
    DETAIL: (id: string) => `${API_PATH}/diagrams/${id}`,
  },

  // Post (Weibo) endpoints
  POSTS: {
    LIST: `${API_PATH}/posts`,
    DETAIL: (id: string) => `${API_PATH}/posts/${id}`,
  },

  // Session endpoints
  SESSIONS: {
    LIST: `${API_PATH}/sessions`,
    DETAIL: (id: string) => `${API_PATH}/sessions/${id}`,
    MESSAGES: (id: string) => `${API_PATH}/sessions/${id}/messages`,
  },

  // Search endpoints
  SEARCH: {
    ENGINES: `${API_PATH}/search/engines`,
    BY_ENGINE: (engine: string) => `${API_PATH}/search/${engine}`,
    BING: `${API_PATH}/search/bing`,
    SERPER: `${API_PATH}/search/serper`,
    TAVILY: `${API_PATH}/search/tavily`,
  },

  // Model endpoints
  MODELS: {
    LIST: `${API_PATH}/models`,
    DETAIL: (id: string) => `${API_PATH}/models/${id}`,
  },

  // External services
  SERVICES: {
    LLM: {
      HOST: LLM_HOST,
      MODEL: LLM_MODEL,
      CHAT_COMPLETIONS: (baseUrl: string) => {
        // 处理两种不同的API格式
        // 1. 不带尾部斜杠的标准OpenAI接口：${baseUrl}/v1/chat/completions
        // 2. 带尾部斜杠的另一种OpenAI接口：${baseUrl}chat/completions (没有/v1/)
        if (baseUrl.endsWith('/')) {
          // 带尾部斜杠的URL，直接添加chat/completions
          return `${baseUrl}chat/completions`;
        } else {
          // 不带尾部斜杠的URL，添加/v1/chat/completions
          return `${baseUrl}/v1/chat/completions`;
        }
      },
    },
    DOCUMENT: {
      MINERU_API_URL,
      MINERU_API_KEY,
      DOCUMENT_ENDPOINT: `${MINERU_API_URL}/document`,
      IMAGES_ENDPOINT: `${MINERU_API_URL}/document/images`,
      IMAGE_ENDPOINT: `${MINERU_API_URL}/document/image`,
    },
    PDF: {
      BASE_URL: PDF_SERVICE_URL,
      EXTRACT_TOC: (url: string) => `${PDF_SERVICE_URL}/extract-toc?url=${encodeURIComponent(url)}`,
      CONVERT_TO_MARKDOWN: (url: string) => `${PDF_SERVICE_URL}/convert-to-markdown?url=${encodeURIComponent(url)}`,
    }
  }
};

export default API;
