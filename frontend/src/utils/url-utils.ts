/**
 * URL 工具函数
 * 用于处理各种 URL 转换和格式化
 */

/**
 * 将 Supabase 存储 URL 转换为浏览器可访问的 URL
 *
 * @param url 原始 URL
 * @returns 转换后的 URL
 */
export function convertSupabaseUrl(url: string): string {
  if (!url) return url;

  // 检查是否是 Supabase 存储 URL
  if (url.includes('host.docker.internal:54321')) {
    // 替换为 localhost:54321
    return url.replace('host.docker.internal:54321', 'localhost:54321');
  }

  // 检查是否是 127.0.0.1:54321
  if (url.includes('127.0.0.1:54321')) {
    // 替换为 localhost:54321
    return url.replace('127.0.0.1:54321', 'localhost:54321');
  }

  return url;
}

/**
 * 转换 URL 以适应浏览器环境
 *
 * 在 Docker 容器中，服务使用 host.docker.internal 访问宿主机，
 * 但在浏览器中需要使用 localhost，
 * 因此需要将 host.docker.internal 转换为 localhost。
 *
 * @param url 原始 URL
 * @returns 转换后的 URL
 */
export function convertUrlForDocker(url: string): string {
  if (!url) return url;

  // 检查是否包含 host.docker.internal
  if (url.includes('host.docker.internal:')) {
    // 替换为 localhost:
    return url.replace('host.docker.internal:', 'localhost:');
  }

  return url;
}

/**
 * 格式化 API URL
 * 确保 URL 格式正确，避免重复的斜杠等问题
 *
 * @param baseUrl 基础 URL
 * @param path 路径
 * @returns 格式化后的 URL
 */
export function formatApiUrl(baseUrl: string, path: string): string {
  // 移除 baseUrl 末尾的斜杠
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

  // 确保 path 以斜杠开头
  const cleanPath = path.startsWith('/') ? path : `/${path}`;

  return `${cleanBaseUrl}${cleanPath}`;
}
