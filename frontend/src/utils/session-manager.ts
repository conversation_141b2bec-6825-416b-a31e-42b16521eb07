/**
 * 会话管理工具
 *
 * 用于管理用户会话状态，包括：
 * 1. 会话活跃状态监控
 * 2. 会话刷新
 * 3. 会话超时处理
 */

import API from '@/config/api';

// 会话配置
const SESSION_CONFIG = {
  // 会话刷新间隔（毫秒）- 默认5分钟刷新一次
  REFRESH_INTERVAL: 5 * 60 * 1000,
  // 会话超时时间（毫秒）- 默认30分钟
  TIMEOUT: 30 * 60 * 1000,
  // 活动检测间隔（毫秒）- 默认1分钟检查一次
  ACTIVITY_CHECK_INTERVAL: 1 * 60 * 1000,
};

// 存储最后活动时间
let lastActivityTime = Date.now();
// 存储定时器ID
let refreshTimerId: number | null = null;
let activityCheckTimerId: number | null = null;

/**
 * 更新最后活动时间
 */
const updateLastActivityTime = () => {
  lastActivityTime = Date.now();
  // 可以在这里添加日志，用于调试
  // console.log('用户活动，更新最后活动时间:', new Date(lastActivityTime).toLocaleTimeString());
};

/**
 * 刷新会话
 * 向后端发送请求以刷新会话状态
 */
const refreshSession = async () => {
  try {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');

    if (!token || !refreshToken) {
      console.warn('无法刷新会话：未找到令牌');
      return;
    }

    console.log('开始刷新会话，使用 refreshToken:', refreshToken.substring(0, 10) + '...');

    // 使用刷新令牌获取新的访问令牌
    const response = await fetch(API.AUTH.REFRESH, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    console.log('刷新会话响应状态:', response.status);

    if (!response.ok) {
      // 尝试读取错误信息
      let errorMessage = '会话刷新失败';
      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorMessage;
      } catch (e) {
        // 如果无法解析JSON，使用状态文本
        errorMessage = `会话刷新失败: ${response.status} ${response.statusText}`;
      }

      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log('会话刷新成功，获取新令牌');

    // 确保响应中包含所需的字段
    if (!data.token || !data.refresh_token) {
      console.error('会话刷新响应缺少必要字段:', data);
      throw new Error('会话刷新响应格式错误');
    }

    // 更新本地存储的令牌
    localStorage.setItem('token', data.token);
    localStorage.setItem('refreshToken', data.refresh_token);

    console.log('令牌已更新到 localStorage');
    return data;
  } catch (error) {
    console.error('会话刷新失败:', error);

    // 检查错误是否是 "Already Used" 错误
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('Already Used')) {
      console.log('检测到 refresh token 已被使用，尝试重新登录');

      // 清除当前的 token 和 refresh token
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');

      // 重定向到登录页面
      // 注意：这里不直接重定向，而是设置一个标志，让用户知道需要重新登录
      localStorage.setItem('needRelogin', 'true');

      // 如果在 5 秒内没有其他代码处理这个标志，则自动重新加载页面
      setTimeout(() => {
        if (localStorage.getItem('needRelogin') === 'true') {
          console.log('自动重新加载页面以触发登录流程');
          localStorage.removeItem('needRelogin');
          window.location.reload();
        }
      }, 5000);
    }

    // 如果刷新失败，不要立即清除令牌，让 AuthContext 处理这个问题
    // 这样可以避免在页面加载时多次尝试刷新令牌导致的问题
    throw error; // 重新抛出错误，让调用者处理
  }
};

// 记录上次刷新时间
let lastRefreshTime = 0;

/**
 * 检查用户活动状态
 * 如果用户在一定时间内没有活动，则刷新会话
 */
const checkActivity = () => {
  const currentTime = Date.now();
  const timeSinceLastActivity = currentTime - lastActivityTime;
  const timeSinceLastRefresh = currentTime - lastRefreshTime;

  // 如果距离上次活动时间超过刷新间隔，且距离上次刷新时间也超过刷新间隔，则刷新会话
  if (timeSinceLastActivity >= SESSION_CONFIG.REFRESH_INTERVAL &&
      timeSinceLastRefresh >= SESSION_CONFIG.REFRESH_INTERVAL) {
    console.log('检测到用户活动间隔超过刷新间隔，尝试刷新会话');
    refreshSession().then(() => {
      lastRefreshTime = Date.now();
    });
  }

  // 如果距离上次活动时间超过超时时间，可以选择登出用户
  if (timeSinceLastActivity >= SESSION_CONFIG.TIMEOUT) {
    console.log('检测到用户活动间隔超过超时时间，可能需要登出');
    // 可以在这里添加登出逻辑
    // logout();
  }
};

/**
 * 启动会话管理
 * 设置事件监听器和定时器
 */
export const startSessionManager = () => {
  // 检查是否已经启动了会话管理
  if (activityCheckTimerId !== null || refreshTimerId !== null) {
    console.log('会话管理器已经在运行中，不重复启动');
    return;
  }

  console.log('启动会话管理器');

  // 初始化最后活动时间
  updateLastActivityTime();

  // 设置用户活动事件监听器
  const activityEvents = ['mousedown', 'keydown', 'scroll', 'touchstart'];
  activityEvents.forEach(eventType => {
    window.addEventListener(eventType, updateLastActivityTime);
  });

  // 设置定期检查活动状态的定时器
  activityCheckTimerId = window.setInterval(checkActivity, SESSION_CONFIG.ACTIVITY_CHECK_INTERVAL);

  // 设置定期刷新会话的定时器
  refreshTimerId = window.setInterval(refreshSession, SESSION_CONFIG.REFRESH_INTERVAL);

  // 不在启动时立即刷新会话，而是等待第一个刷新间隔
  console.log(`会话管理器已启动，将在 ${SESSION_CONFIG.REFRESH_INTERVAL / 1000 / 60} 分钟后首次刷新会话`);
};

/**
 * 停止会话管理
 * 清除事件监听器和定时器
 */
export const stopSessionManager = () => {
  // 清除事件监听器
  const activityEvents = ['mousedown', 'keydown', 'scroll', 'touchstart'];
  activityEvents.forEach(eventType => {
    window.removeEventListener(eventType, updateLastActivityTime);
  });

  // 清除定时器
  if (activityCheckTimerId !== null) {
    clearInterval(activityCheckTimerId);
    activityCheckTimerId = null;
  }

  if (refreshTimerId !== null) {
    clearInterval(refreshTimerId);
    refreshTimerId = null;
  }
};

/**
 * 手动刷新会话
 * 可以在重要操作（如保存数据）前调用
 */
export const manualRefreshSession = () => {
  updateLastActivityTime();
  return refreshSession();
};

// 导出会话管理工具
export default {
  startSessionManager,
  stopSessionManager,
  manualRefreshSession,
  updateLastActivityTime,
};
