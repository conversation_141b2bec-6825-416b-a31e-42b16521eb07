interface HealthCheckResult {
  status: "ok" | "error";
  latency: number;
}

interface HealthCheckOptions {
  serviceId: string;
  baseUrl: string;
}

export const checkLLMHealth = async ({ serviceId, baseUrl }: HealthCheckOptions): Promise<HealthCheckResult> => {
  try {
    const endpoint = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    const startTime = Date.now();
    
    const response = await fetch(`${endpoint}/v1/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer sk-YyiBg6DSn1Fc2KBNU6ZYtw`,
        'accept': 'application/json'
      }
    });
    const latency = Date.now() - startTime;
    
    const result = await response.json();
    
    return {
      status: response.ok && Array.isArray(result.data) && result.data.length > 0 ? "ok" : "error",
      latency
    };
  } catch (error) {
    return {
      status: "error",
      latency: -1
    };
  }
};