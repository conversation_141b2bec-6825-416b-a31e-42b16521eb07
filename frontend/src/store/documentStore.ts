import { create } from 'zustand';
import API from '@/config/api';
import { convertSupabaseUrl } from '@/utils/url-utils';

export interface Document {
  id: string;  // 修改为 string 类型
  title: string;  // 从 name 改为 title
  description: string;  // 新增字段
  type: string;
  content_type: string;
  file_path: string;  // 新增字段
  file_size: number;
  markdown_url: string | null;  // 从 markdown_content 改为 markdown_url
  user_id: string;  // 新增字段
  created_at: string;
  updated_at: string;  // 新增字段
}

interface DocumentStore {
  documents: Document[];
  isLoading: boolean;
  error: string | null;
  fetchDocuments: () => Promise<void>;
  deleteDocument: (id: string) => Promise<void>;  // 修改参数类型为 string
}

export const useDocumentStore = create<DocumentStore>((set) => ({
  documents: [],
  isLoading: false,
  error: null,
  fetchDocuments: async () => {
    try {
      set({ isLoading: true, error: null });
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      // 使用 API 配置
      const response = await fetch(API.DOCUMENTS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // 转换文档 URL
      const documentsWithConvertedUrls = data.map((doc: Document) => ({
        ...doc,
        file_path: convertSupabaseUrl(doc.file_path),
        markdown_url: doc.markdown_url ? convertSupabaseUrl(doc.markdown_url) : null
      }));

      set({ documents: documentsWithConvertedUrls });
    } catch (error) {
      console.error('Error fetching documents:', error);
      set({ error: '获取文档失败，请稍后重试' });
    } finally {
      set({ isLoading: false });
    }
  },
  deleteDocument: async (id: string) => {  // 修改参数类型为 string
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      // 使用 API 配置
      const response = await fetch(API.DOCUMENTS.DETAIL(id), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '删除失败');
      }

      set((state) => ({
        documents: state.documents.filter((d) => d.id !== id),
      }));
    } catch (error) {
      console.error('Error:', error);
      throw new Error(error instanceof Error ? error.message : '删除文档失败，请重试');
    }
  },
}));