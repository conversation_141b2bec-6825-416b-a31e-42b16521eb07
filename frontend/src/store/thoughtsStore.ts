import { create } from 'zustand';
import API from '@/config/api';

interface Thought {
  id: string;
  title: string;
  url: string;
  desc: string | null;
  content: string | null;
  summary: string | null;
  bookmark_type: string;
  tags: string;
  is_ticked: boolean;
  content_type: string;
  rating: number;
  is_public: boolean;
  folder_id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

interface ThoughtStore {
  thoughts: Thought[];
  loading: boolean;
  error: string | null;
  fetchThoughts: () => Promise<void>;
  addThought: (thought: Partial<Thought>) => Promise<void>;
  updateThoughtPin: (id: string, isPinned: boolean) => Promise<void>;
  deleteThought: (id: string) => Promise<void>; // 新增删除书签的方法
}

export const useThoughtStore = create<ThoughtStore>((set) => ({
  thoughts: [],
  loading: false,
  error: null,
  fetchThoughts: async () => {
    set({ loading: true, error: null });
    try {
      const token = localStorage.getItem('token');

      // Check if token exists
      if (!token) {
         // 清除无效的令牌
         localStorage.removeItem('token');
        throw new Error('未登录或会话已过期，请重新登录');
      }

      // 使用相对路径，确保通过 Vite 的代理配置处理
      const response = await fetch(`/api/bookmark/?skip=0&limit=100`, {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        // Handle specific error codes
        if (response.status === 401) {
          // Clear invalid token
          localStorage.removeItem('token');
          throw new Error('认证失败，请重新登录');
        }

        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      set({ thoughts: data, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取书签失败';
      console.error('获取书签失败:', errorMessage);
      set({ error: errorMessage, thoughts: [] }); // Clear thoughts on error
    } finally {
      set({ loading: false });
    }
  },

  addThought: async (thought: Partial<Thought>) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法添加书签');
      }

      // 获取用户ID，如果存储了用户信息
      let userId = '';
      const userJson = localStorage.getItem('user');
      if (userJson) {
        const user = JSON.parse(userJson);
        userId = user.id;
      }

      // 使用相对路径，确保通过 Vite 的代理配置处理
      const response = await fetch('/api/bookmark', {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          ...thought,
          title: thought.title || null,
          url: thought.url || '',
          desc: thought.desc || null,
          content: thought.content || null,
          summary: thought.summary || null,
          bookmark_type: thought.bookmark_type || 'bookmark',
          tags: thought.tags || 'string',
          is_ticked: thought.is_ticked !== undefined ? thought.is_ticked : false,
          content_type: thought.content_type || 'idea',
          is_public: thought.is_public !== undefined ? thought.is_public : false,
          rating: thought.rating || 0,
          // 不设置默认的folder_id，让后端处理
          folder_id: thought.folder_id || null,
          user_id: userId || undefined // 后端会自动处理用户ID
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `添加书签失败: ${response.status}`);
      }

      // 添加成功后刷新书签列表
      await useThoughtStore.getState().fetchThoughts();
      return await response.json(); // 返回新添加的书签数据
    } catch (error) {
      console.error('添加书签失败:', error);
      throw error;
    }
  },

  // 新增更新置顶状态的方法
  updateThoughtPin: async (id: string, isPinned: boolean) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法更新书签');
      }

      // 使用相对路径，确保通过 Vite 的代理配置处理
      const response = await fetch(`/api/bookmark/${id}`, {
        method: 'PUT',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          is_ticked: isPinned
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `更新书签失败: ${response.status}`);
      }

      // 更新本地状态，避免重新获取全部数据
      set((state) => ({
        thoughts: state.thoughts.map(thought =>
          thought.id === id
            ? { ...thought, is_ticked: isPinned }
            : thought
        )
      }));

      return await response.json();
    } catch (error) {
      console.error('更新书签置顶状态失败:', error);
      throw error;
    }
  },

  // 新增删除书签的方法
  deleteThought: async (id: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法删除书签');
      }

      // 使用相对路径，确保通过 Vite 的代理配置处理
      const response = await fetch(`/api/bookmark/${id}`, {
        method: 'DELETE',
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `删除书签失败: ${response.status}`);
      }

      // 更新本地状态，从列表中移除已删除的书签
      set((state) => ({
        thoughts: state.thoughts.filter(thought => thought.id !== id)
      }));

      return await response.json();
    } catch (error) {
      console.error('删除书签失败:', error);
      throw error;
    }
  },
}));
