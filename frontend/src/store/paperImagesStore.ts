import { create } from 'zustand';

// 添加图片类型枚举
export type DiagramType = 'flowchart' | 'framework' | 'architecture' | 'sequence' | 'other';

interface SelectedImage {
  name: string;          
  file_name: string;     
  content?: {            
    content_type: string;
    base64_content: string;
  };
  size: number;
  path: string;
  type: DiagramType;     // 添加类型字段
}

interface PaperImagesStore {
  selectedImages: Map<string, SelectedImage>;
  addImage: (image: SelectedImage) => void;
  removeImage: (name: string) => void;
  clearImages: () => void;
  updateImageContent: (name: string, content: SelectedImage['content']) => void;
}

export const usePaperImagesStore = create<PaperImagesStore>((set) => ({
  selectedImages: new Map(),
  addImage: (image) => 
    set((state) => ({
      // 确保添加时设置默认类型
      selectedImages: new Map(state.selectedImages).set(image.name, {
        ...image,
        type: image.type || 'flowchart' // 设置默认类型
      })
    })),
  removeImage: (name) =>
    set((state) => {
      const newMap = new Map(state.selectedImages);
      newMap.delete(name);
      return { selectedImages: newMap };
    }),
  clearImages: () => set({ selectedImages: new Map() }),
  updateImageContent: (name, content) =>
    set((state) => {
      const image = state.selectedImages.get(name);
      if (!image) return state;
      
      const newMap = new Map(state.selectedImages);
      newMap.set(name, { ...image, content });
      return { selectedImages: newMap };
    }),
}));