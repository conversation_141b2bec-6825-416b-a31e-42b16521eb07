import { generateUUID } from '@/lib/utils';
import { create } from 'zustand';

interface SelectedText {
  id: string;
  content: string;
}

interface SelectedTextStore {
  selectedTexts: SelectedText[];
  addSelectedText: (content: string) => void;
  removeSelectedText: (id: string) => void;
  clearSelectedTexts: () => void;
}

export const useSelectedTextStore = create<SelectedTextStore>((set) => ({
  selectedTexts: [],
  addSelectedText: (content) => 
    set((state) => ({
      selectedTexts: [...state.selectedTexts, {
        id: generateUUID(),
        content
      }]
    })),
  removeSelectedText: (id) =>
    set((state) => ({
      selectedTexts: state.selectedTexts.filter(text => text.id !== id)
    })),
  clearSelectedTexts: () => set({ selectedTexts: [] })
}));