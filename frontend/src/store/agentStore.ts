import { create } from 'zustand';
import { systemPrompts } from '@/lib/prompts';

interface Agent {
  name: string;
  content: string;
}

interface AgentStore {
  agents: Record<string, Agent>;
  currentAgent: string;
  setCurrentAgent: (agent: string) => void;
}

export const useAgentStore = create<AgentStore>((set) => ({
  agents: {
    deep: {
      name: "Deep Thinker",
      content: systemPrompts.deep.content
    },
    academic: {
      name: "Anthropic",
      content: systemPrompts.anthropic.content
    },
    digital: {
      name: "论文审读",
      content: systemPrompts.digital.content
    },
    stooges:{
      name: "三个臭皮匠",
      content: systemPrompts.stooges.content
    }
  },
  currentAgent: 'deep',
  setCurrentAgent: (agent) => set({ currentAgent: agent }),
}));