import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 定义消息类型
export interface Message {
  role: 'user' | 'assistant';
  content: string;
}

// 定义搜索结果类型
export interface SearchResult {
  id?: string;
  title: string;
  url: string;
  snippet: string;
  content?: string;
  score?: number;
}

// 定义 Academic 页面的状态
interface AcademicState {
  messages: Message[];
  inputValue: string;
  results: SearchResult[];
  showSearch: boolean;
  isLoading: boolean;
  isThinking: boolean; // 是否正在思考
  isOrganizing: boolean; // 是否正在整理回答
  selectedResults: Set<string>;

  // 设置消息
  setMessages: (messages: Message[]) => void;

  // 添加消息
  addMessage: (message: Message) => void;

  // 添加多条消息
  addMessages: (messages: Message[]) => void;

  // 更新最后一条助手消息
  updateLastAssistantMessage: (content: string) => void;

  // 设置输入值
  setInputValue: (value: string) => void;

  // 设置搜索结果
  setResults: (results: SearchResult[]) => void;

  // 设置是否显示搜索
  setShowSearch: (show: boolean) => void;

  // 设置是否加载中
  setIsLoading: (loading: boolean) => void;

  // 设置是否思考中
  setIsThinking: (thinking: boolean) => void;

  // 设置是否正在整理回答
  setIsOrganizing: (organizing: boolean) => void;

  // 设置选中的搜索结果
  setSelectedResults: (results: Set<string>) => void;

  // 添加选中的搜索结果
  addSelectedResult: (id: string) => void;

  // 移除选中的搜索结果
  removeSelectedResult: (id: string) => void;

  // 清空选中的搜索结果
  clearSelectedResults: () => void;

  // 清空所有状态
  clearAll: () => void;
}

// 创建 store
export const useAcademicStore = create<AcademicState>()(
  persist(
    (set) => ({
      messages: [],
      inputValue: '',
      results: [],
      showSearch: false,
      isLoading: false,
      isThinking: false,
      isOrganizing: false,
      selectedResults: new Set<string>(),

      setMessages: (messages) => set({ messages }),

      addMessage: (message) => set((state) => ({
        messages: [...state.messages, message],
      })),

      addMessages: (messages) => set((state) => ({
        messages: [...state.messages, ...messages],
      })),

      updateLastAssistantMessage: (content) => set((state) => {
        const messages = [...state.messages];
        // 找到最后一条助手消息
        for (let i = messages.length - 1; i >= 0; i--) {
          if (messages[i].role === 'assistant') {
            messages[i] = { ...messages[i], content };
            break;
          }
        }
        return { messages };
      }),

      setInputValue: (inputValue) => set({ inputValue }),

      setResults: (results) => set({ results }),

      setShowSearch: (showSearch) => set({ showSearch }),

      setIsLoading: (isLoading) => set({ isLoading }),

      setIsThinking: (isThinking) => set({ isThinking }),

      setIsOrganizing: (isOrganizing) => set({ isOrganizing }),

      setSelectedResults: (selectedResults) => set({ selectedResults }),

      addSelectedResult: (id) => set((state) => {
        const newSelectedResults = new Set(state.selectedResults);
        newSelectedResults.add(id);
        return { selectedResults: newSelectedResults };
      }),

      removeSelectedResult: (id) => set((state) => {
        const newSelectedResults = new Set(state.selectedResults);
        newSelectedResults.delete(id);
        return { selectedResults: newSelectedResults };
      }),

      clearSelectedResults: () => set({ selectedResults: new Set<string>() }),

      clearAll: () => set({
        messages: [],
        inputValue: '',
        results: [],
        showSearch: false,
        isLoading: false,
        isThinking: false,
        isOrganizing: false,
        selectedResults: new Set<string>(),
      }),
    }),
    {
      name: 'academic-storage', // 存储的名称
      partialize: (state) => ({
        messages: state.messages,
        // 不持久化其他状态
      }),
    }
  )
);
