import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { API } from '../config/api';

export interface User {
  id: string;
  email: string;
  username?: string;
  display_name?: string;
  is_admin?: boolean;
  is_active?: boolean;
  role?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  last_login?: string;
}

interface AuthState {
  token: string | null;
  refreshToken: string | null;
  user: User | null;
  isAuthenticated: boolean;

  // 登录方法
  login: (email: string, password: string) => Promise<void>;
  // 登出方法
  logout: () => void;
  // 刷新令牌
  refreshAuth: () => Promise<void>;
  // 获取用户信息
  fetchUserProfile: () => Promise<void>;
}

// 修复类型错误，使用正确的类型定义
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      token: null,
      refreshToken: null,
      user: null,
      isAuthenticated: false,

      login: async (email: string, password: string) => {
        try {
          // 使用已导入的 API 配置
          const loginUrl = API.AUTH.SIGN_IN;

          console.log('authStore 登录请求 URL:', loginUrl);

          const response = await fetch(loginUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            // 尝试读取错误信息
            let errorMessage = '登录失败';
            try {
              const errorData = await response.json();
              errorMessage = errorData.detail || errorMessage;
            } catch (e) {
              // 如果无法解析JSON，使用状态文本
              errorMessage = `登录失败: ${response.status} ${response.statusText}`;
            }

            console.error(errorMessage);
            throw new Error(errorMessage);
          }

          const data = await response.json();
          console.log('登录成功，获取认证信息');

          // 确保响应中包含所需的字段
          if (!data.token || !data.refresh_token || !data.user) {
            console.error('登录响应缺少必要字段:', data);
            throw new Error('登录响应格式错误');
          }

          // 保存认证信息
          set({
            token: data.token,
            refreshToken: data.refresh_token,
            user: data.user,
            isAuthenticated: true,
          });

          // 将令牌和用户信息保存到 localStorage
          localStorage.setItem('token', data.token);
          localStorage.setItem('refreshToken', data.refresh_token);
          localStorage.setItem('user', JSON.stringify(data.user));

          console.log('认证信息已保存到 store 和 localStorage');
          return data;

        } catch (error) {
          console.error('登录错误:', error);
          throw error;
        }
      },

      logout: () => {
        console.log('执行登出操作，清除所有认证信息');
        // 清除所有认证相关的本地存储
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('username');
        localStorage.removeItem('user');

        // 更新状态
        set({
          token: null,
          refreshToken: null,
          user: null,
          isAuthenticated: false,
        });

        console.log('登出完成，所有认证信息已清除');
      },

      refreshAuth: async () => {
        const { refreshToken } = get();

        if (!refreshToken) {
          console.error('刷新令牌失败: 没有刷新令牌');
          throw new Error('没有刷新令牌');
        }

        try {
          // 使用 API 配置中的 REFRESH 路径
          const refreshUrl = API.AUTH.REFRESH;

          console.log('authStore 刷新令牌请求 URL:', refreshUrl);
          console.log('使用的刷新令牌:', refreshToken.substring(0, 10) + '...');

          // 调用后端的刷新令牌端点
          const response = await fetch(refreshUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ refresh_token: refreshToken }),
          });

          // 记录响应状态
          console.log('刷新令牌响应状态:', response.status);

          if (!response.ok) {
            // 尝试读取错误信息
            let errorMessage = '刷新令牌失败';
            try {
              const errorData = await response.json();
              errorMessage = errorData.detail || errorMessage;
            } catch (e) {
              // 如果无法解析JSON，使用状态文本
              errorMessage = `刷新令牌失败: ${response.status} ${response.statusText}`;
            }

            console.error(errorMessage);
            throw new Error(errorMessage);
          }

          const data = await response.json();
          console.log('刷新令牌成功，获取新令牌');

          // 确保响应中包含所需的字段
          if (!data.token || !data.refresh_token) {
            console.error('刷新令牌响应缺少必要字段:', data);
            throw new Error('刷新令牌响应格式错误');
          }

          // 更新认证信息
          set({
            token: data.token,
            refreshToken: data.refresh_token,
            isAuthenticated: true,
          });

          // 更新 localStorage 中的令牌
          localStorage.setItem('token', data.token);
          localStorage.setItem('refreshToken', data.refresh_token);

          console.log('令牌已更新到 store 和 localStorage');
          return data;

        } catch (error) {
          console.error('刷新令牌错误:', error);
          // 如果刷新失败，登出用户
          get().logout();
          throw error;
        }
      },

      // 新增获取用户信息的方法
      fetchUserProfile: async () => {
        const { token } = get();

        if (!token) {
          throw new Error('未登录，无法获取用户信息');
        }

        try {
          // 使用 API 配置中的 ME 路径
          const meUrl = API.AUTH.ME;

          console.log('authStore 获取用户信息请求 URL:', meUrl);

          // 检查 URL 是否包含硬编码的 localhost
          if (meUrl.includes('localhost:8000')) {
            console.error('警告: 获取用户信息 URL 包含硬编码的 localhost:8000，这可能会导致问题');
            console.log('尝试使用相对路径替代');
          }

          const response = await fetch(meUrl, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.detail || '获取用户信息失败');
          }

          const userData = await response.json();

          // 更新用户信息
          set({
            user: userData,
            isAuthenticated: true,
          });

          return userData;
        } catch (error) {
          console.error('获取用户信息错误:', error);

          // 如果是认证错误，可能是令牌过期，尝试刷新令牌
          if (error instanceof Error && error.message.includes('认证令牌')) {
            try {
              await get().refreshAuth();
              // 刷新成功后重新获取用户信息
              return get().fetchUserProfile();
            } catch (refreshError) {
              // 如果刷新也失败，则登出用户
              get().logout();
              throw new Error('会话已过期，请重新登录');
            }
          }

          throw error;
        }
      },
    }),
    {
      name: '3stooges-auth-storage', // 持久化存储的名称
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        token: state.token,
        refreshToken: state.refreshToken,
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 导出一个获取当前用户ID的辅助函数
export const getUserId = () => {
  const { user } = useAuthStore.getState();
  return user?.id || null;
};