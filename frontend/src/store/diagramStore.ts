import { create } from 'zustand';
import API from '@/config/api';
import { convertSupabaseUrl } from '@/utils/url-utils';

// 定义 Diagram 和 DiagramType 接口
export interface Diagram {
  id: string;  // 修改为 string 类型
  name: string;
  description: string;
  source: string;
  type: string;
  content_type: string;
  file_size: number;
  image_url: string;
  user_id: string;
  folder_id: string | null;
  created_at: string;
  updated_at: string;
}

export type DiagramType = 'JPG' | 'PNG' | 'SVG' | 'PDF' | 'user_uploads';

interface DiagramStore {
  diagrams: Diagram[];
  isLoading: boolean;
  error: string | null;
  selectedType: DiagramType | 'all';
  setSelectedType: (type: DiagramType | 'all') => void;
  fetchDiagrams: () => Promise<void>;
  deleteDiagram: (id: string) => Promise<void>;  // 修改为 string 类型
}

export const useDiagramStore = create<DiagramStore>((set, get) => ({
  diagrams: [],
  isLoading: false,
  error: null,
  selectedType: 'all',
  setSelectedType: (type) => set({ selectedType: type }),

  fetchDiagrams: async () => {
    try {
      set({ isLoading: true, error: null });

      // 获取 token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录或会话已过期，请重新登录');
      }

      // 使用 API 配置
      const response = await fetch(API.DIAGRAMS.LIST, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.error('获取图表失败，状态码:', response.status);

        // 尝试获取更详细的错误信息
        let errorMessage = `HTTP 错误! 状态码: ${response.status || '未知'}`;
        try {
          const errorData = await response.json();
          if (errorData?.detail) {
            errorMessage = errorData.detail;
          }
        } catch (e) {
          // 如果无法解析 JSON，使用默认错误消息
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      // 确保每个图表对象都有必需的字段，并处理可能的 null 值
      const diagramsWithDisplayImage = data.map((diagram: any) => {
        // 转换 image_url 使其在浏览器中可访问
        const convertedImageUrl = convertSupabaseUrl(diagram.image_url || '');

        return {
          ...diagram,
          // 确保所有必需字段都有值
          id: diagram.id || '',
          name: diagram.name || '',
          description: diagram.description || '',
          source: diagram.source || '',
          type: diagram.type || '',
          content_type: diagram.content_type || '',
          file_size: diagram.file_size || 0,
          image_url: convertedImageUrl,
          user_id: diagram.user_id || '',
          folder_id: diagram.folder_id || null,
          created_at: diagram.created_at || new Date().toISOString(),
          updated_at: diagram.updated_at || diagram.created_at || new Date().toISOString(),
          displayImage: convertedImageUrl || 'default-image-url'
        };
      });

      set({ diagrams: diagramsWithDisplayImage });
    } catch (error) {
      console.error('获取图表失败:', error);
      set({ error: error instanceof Error ? error.message : '获取图表失败，请稍后重试' });
    } finally {
      set({ isLoading: false });
    }
  },

  deleteDiagram: async (id: string) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录，请先登录');
      }

      // 使用 API 配置
      const response = await fetch(API.DIAGRAMS.DETAIL(id), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (!response.ok) throw new Error('删除失败');

      set((state) => ({
        diagrams: state.diagrams.filter((d) => d.id !== id),
      }));
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  },
}));
