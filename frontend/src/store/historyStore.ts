import { create } from 'zustand'
import { generateUUID } from '@/lib/utils'


export interface Conversation {
  id: string;
  createdAt: number;
  summary: string;
  messages: Array<{
    type: 'user' | 'system';
    content: string;
    timestamp: number;
  }>;
}

export interface HistoryStore {
  isOpen: boolean;
  conversations: Conversation[];
  currentConversationId: string | null;
  toggleDialog: () => void;
  startNewConversation: () => string;
  addMessage: (conversationId: string, message: { type: 'user' | 'system', content: string }) => void;
  updateSummary: (conversationId: string, summary: string) => void;
  clearHistory: () => void;
}

export const useHistoryStore = create<HistoryStore>((set, get) => ({
  isOpen: false,
  conversations: [],
  currentConversationId: null,
  
  toggleDialog: () => set((state) => ({ isOpen: !state.isOpen })),
  
  startNewConversation: () => {
    const newConversation = {
      id: generateUUID(),
      createdAt: Date.now(),
      summary: '',
      messages: []
    };
    set((state) => ({
      conversations: [newConversation, ...state.conversations],
      currentConversationId: newConversation.id
    }));
    return newConversation.id;
  },
  
  addMessage: (conversationId, message) => {
    // console.log('addMessage called with conversationId:', conversationId, 'and message:', message);
    // console.log('Current conversations:', get().conversations);
    
    set((state) => {
      const existingConversation = state.conversations.find(conv => conv.id === conversationId);
      
      if (!existingConversation) {
        // 如果找不到对话，创建一个新的
        const newConversation: Conversation = {
          id: conversationId,
          createdAt: Date.now(),
          summary: '',
          messages: [{
            ...message,
            timestamp: Date.now()
          }]
        };
        return {
          conversations: [newConversation, ...state.conversations],
          currentConversationId: conversationId
        };
      }
      
      // 如果找到对话，添加消息
      return {
        conversations: state.conversations.map(conv => 
          conv.id === conversationId ? {
            ...conv,
            messages: [...conv.messages, {
              ...message,
              timestamp: Date.now()
            }]
          } : conv
        )
      };
    });
  },
  
  updateSummary: (conversationId, summary) => {
    set((state) => ({
      conversations: state.conversations.map(conv => 
        conv.id === conversationId ? {
          ...conv,
          summary
        } : conv
      )
    }));
  },
  
  clearHistory: () => set({ conversations: [], currentConversationId: null }),
}));
