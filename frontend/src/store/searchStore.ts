import { create } from 'zustand';

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
}

interface SearchStore {
  results: SearchResult[];
  selectedResults: Set<number>;
  setResults: (results: SearchResult[]) => void;
  toggleSelected: (index: number) => void;
  clearSelected: () => void;
}

export const useSearchStore = create<SearchStore>((set) => ({
  results: [],
  selectedResults: new Set(),
  setResults: (results) => set({ 
    results,
    selectedResults: new Set() // 重置选中状态
  }),
  toggleSelected: (index) => set((state) => {
    const newSelected = new Set(state.selectedResults);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    return { selectedResults: newSelected };
  }),
  clearSelected: () => set({ selectedResults: new Set() }),
}));