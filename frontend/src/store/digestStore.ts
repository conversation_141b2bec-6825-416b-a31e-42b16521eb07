import { generateUUID } from '@/lib/utils';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Digest {
  id: string;
  title: string;
  content: string;
  created_at: string;
  user_id: number;
}

interface DigestStore {
  digests: Digest[];
  addDigest: (digest: Omit<Digest, 'id' | 'created_at'>) => void;
  removeDigest: (id: string) => void;
  clearDigests: () => void;
}

export const useDigestStore = create<DigestStore>()(
  persist(
    (set) => ({
      digests: [
      ],
      
      addDigest: (digest) => set((state) => ({
        digests: [
          ...state.digests,
          {
            ...digest,
            id: generateUUID(),
            created_at: new Date().toISOString(),
          }
        ]
      })),

      removeDigest: (id) => set((state) => ({
        digests: state.digests.filter((d) => d.id !== id)
      })),

      clearDigests: () => set({ digests: [] }),
    }),
    {
      name: 'digest-storage',
    }
  )
);