// TODO: 
// ModelType Model_Categories融合
// ModelConfig修改为BaseModelConfig, 而CustomModel里面的内容和BaseModel有依存关系

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ModelType = "language" | "vision" | "embedding" | "ocr" | "document";
export const MODEL_CATEGORIES = [
  {
    id: "language",
    name: "语言模型",
    description: "用于自然语言处理的大规模语言模型"
  },
  {
    id: "vision",
    name: "视觉模型",
    description: "处理图像和视频的视觉模型"
  },
  {
    id: "embedding",
    name: "嵌入服务",
    description: "文本向量化服务"
  },
  {
    id: "ocr",
    name: "OCR服务",
    description: "光学字符识别服务"
  },
  {
    id: "document",
    name: "文档转换",
    description: "文档格式转换服务"
  }
] as const;

export interface ModelConfig {
  host: string;
  model: string;
  name: string; 
  apiKey: string;
  type: ModelType;
}

export interface CustomModel {
  id: string;
  type: ModelType;
  name: string;
  description: string;
  baseModel: string;
  baseService: string;
  temperature: number;
  maxTokens: number;
  agentId: string;  // 添加 agentId 字段
  status: "training" | "ready" | "failed";
  createdAt: string;
}

interface ModelConfigState {
  configs: ModelConfig[];
  currentConfig: ModelConfig | null;
  setCurrentConfig: (config: ModelConfig) => void;
  addConfig: (config: ModelConfig) => void;
  removeConfig: (config: ModelConfig) => void;
  updateConfig: (oldConfig: ModelConfig, newConfig: ModelConfig) => void;

  customModels: CustomModel[];
  addCustomModel: (model: CustomModel) => void;
  removeCustomModel: (model: CustomModel) => void;
}

export const useModelConfigStore = create<ModelConfigState>()(
  persist(
    (set) => ({
      configs: [],
      currentConfig: null,
      setCurrentConfig: (config) => set({ currentConfig: config }),
      addConfig: (config) =>
        set((state) => ({
          configs: [...state.configs, config],
          currentConfig: state.currentConfig || config // 如果是第一个配置，自动设为当前配置
        })),
      removeConfig: (config) =>
        set((state) => ({
          configs: state.configs.filter(
            (c) => c.host !== config.host || c.model !== config.model
          ),
          currentConfig: state.currentConfig?.host === config.host ? null : state.currentConfig
        })),
      updateConfig: (oldConfig, newConfig) =>
        set((state) => ({
          configs: state.configs.map(c => 
            (c.host === oldConfig.host && c.model === oldConfig.model) ? newConfig : c
          ),
          currentConfig: state.currentConfig?.host === oldConfig.host ? newConfig : state.currentConfig
        })),
        customModels: [],
        addCustomModel: (model) => set((state) => ({
          customModels: [...state.customModels, model]
        })),
        removeCustomModel: (model) =>
          set((state) => ({
            customModels: state.customModels.filter((m) => m.id !== model.id),
          })),
    }),
    {
      name: 'model-config-storage',
    }
  )
)