import { create } from 'zustand';

export interface Team {
  id: number;
  name: string;
  description: string;
  members: TeamMember[];
}

export interface TeamMember {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  avatar_url: string;
  role: string;
  team_id: number;  // 添加 team_id
  user_id: number;  // 添加 user_id
}

interface TeamState {
  teams: Team[];
  isLoading: boolean;
  error: string | null;
  fetchTeams: () => Promise<void>;
  fetchTeamMembers: (teamId: number) => Promise<void>;
}

const getToken = () => localStorage.getItem('token') || sessionStorage.getItem('token');

export const useTeamStore = create<TeamState>((set, get) => ({
  teams: [],
  isLoading: false,
  error: null,

  fetchTeams: async () => {
    try {
      set({ isLoading: true, error: null });
      const token = getToken();
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/teams`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const teams = await response.json();

      set({ teams: teams.map((team: Team) => ({ ...team, members: [] })) });

      // 获取每个团队的成员
      for (const team of teams) {
        await get().fetchTeamMembers(team.id);
      }
    } catch (error) {
      set({ error: error instanceof Error ? error.message : '获取团队列表失败' });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchTeamMembers: async (teamId: number) => {
    try {
      const token = getToken();
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/teams/${teamId}/members`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const members = await response.json();

      // console.log("members:", members)

      set((state) => ({
        teams: state.teams.map(team => 
          team.id === teamId ? { ...team, members } : team
        )
      }));
    } catch (error) {
      console.error(`获取团队 ${teamId} 成员失败:`, error);
    }
  }
}));