import { create } from 'zustand';

interface Message {
  role: 'user' | 'assistant';
  content: string;
}

interface MessageStore {
  currentSessionId: string | null;
  messages: Record<string, Message[]>;
  setCurrentSessionId: (sessionId: string) => void;
  addMessage: (sessionId: string, message: Message) => void;
  addMessages: (sessionId: string, messages: Message[]) => void;
  getMessages: (sessionId: string) => Message[];
}

export const useMessageStore = create<MessageStore>((set, get) => ({
  currentSessionId: null,
  messages: {},
  setCurrentSessionId: (sessionId) => set({ currentSessionId: sessionId }),
  addMessage: (sessionId, message) => set((state) => ({
    messages: {
      ...state.messages,
      [sessionId]: [...(state.messages[sessionId] || []), message],
    },
  })),
  addMessages: (sessionId, messages) => set((state) => ({
    messages: {
      ...state.messages,
      [sessionId]: [...(state.messages[sessionId] || []), ...messages],
    },
  })),
  getMessages: (sessionId) => get().messages[sessionId] || [],
}));