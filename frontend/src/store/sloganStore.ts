import { create } from "zustand";

const slogans = [
  "如何提问，是开启研究之旅的前提",
  "突破学科的边界",
  "天马行空，不要拘泥",
  "可怕的是，问题的贫穷",
  "未来已来，只是分布不均",
  "还是休息一会吧，年轻人",
  "既然选择远方，当不负青春，砥砺前行",
  "青春由磨砺而出彩，人生因奋斗而升华",
  "但行前路，不负韶华",
  "每一个裂缝都是为透出光而努力",
  "美好的一天，需自己去创造",
  "要做冲出的黑马，而非坠落的星星",
  "纵然世间黑暗，仍有一点星光",
  "试一下，会比想象中强大",
  "眼里有不朽光芒，心里有永恒希望",
  "等待的不仅是未来，还有希望",
  "只有极致拼搏，才配得上极致风景",
  "若痛恨所处黑暗，就成为想要的光",
  "以蝼蚁之行，展鸿鹄之志",
  "至少拥有一个梦想，有理由去坚强",
  "可以一无所有，但不能一无是处",
  "努力时间不够，哪有时间绝望",
  "彗星般人生可短暂，却不能黯淡沉沦",
  "抱怨身处黑暗，不如提灯前行",
  "黑暗笼罩更凸显光明可贵",
  "要成长，绝处也能逢生",
  "心态决定高度，细节决定成败",
  "没人会嘲笑竭尽全力的人",
  "上天公平，有付出就有收获",
  "生活苦，莫放弃爱与希望",
  "过去价值不代表未来地位",
  "惟有主动付出，才有丰富果实收获",
  "站在死亡面前不可怕，可怕的是牺牲无价值",
  "存在因价值创造，淘汰因价值丧失",
  "价值在于宁愿牺牲自己，也不愿拖累他人",
  "人生有追求、盼望、珍视、向往、护卫之物，愿为之活乃至献身，此即价值",
  "什么都不懂的人毫无价值",
  "无私奉献可助他人，还能提升自身价值",
  "价值不等同付出，付出却能换来价值",
  "任务无形中完成，价值无形中升华",
  "个人与不同人、不同平台在一起会体现不同价值",
  "面对生活，不能无欲无求，也不能过度强求，贫富不代表生存价值",
  "远离言语与是非，存在便显露真实价值",
  "若喜爱自身价值，就得给世界创造价值",
  "人们常高估所没有之物价值，忽视自身拥有之物",
  "精彩人生是在有限生命中实现无限价值的人生",
  "人一生价值，不应以时间衡量，而应以深度衡量",
  "成功最重要因素是有健康身体和旺盛精力",
  "成功之花，人们惊羡其明艳，芽儿却浸透奋斗泪泉、洒满牺牲血雨",
  "成功是陡峭阶梯，两手插兜爬不上去",
  "成功没有奇迹，只有轨迹；不靠条件，只靠信念",
  "成功就是失败到失败，热情不减当初",
  "成功关键在于勇敢承担责任",
  "成功道路别独自摸索，多问路才不会迷路",
  "努力不一定成功，但放弃一定会失败",
  "自己战胜自己是最可贵的胜利",
  "成功无秘诀，若有，一是坚持到底、永不放弃，二是想放弃时看第一个秘诀",
  "人生虽曲折，记得活出精彩",
  "怕的不是做不到，而是想都不敢想",
  "此刻打盹，将做梦；此刻学习，将圆梦",
  "得之坦然，失之淡然，争取必然，顺其自然",
  "世界模样，取决于凝视它的目光",
  "现在睡觉会做梦，现在学习会让梦实现",
  "比我强大的人都在拼命，我没理由不努力",
  "想放弃时想想当初为何坚持到这里",
  "以良好心态面对生活，生活才美好",
  "把握现在，就是创造未来",
  "人最大的对手，是自己的懒惰",
  "人生不求与人相比，但求超越自己",
  "无论何时，绝不允许自己有一点灰心丧气",
  "笨鸟先飞早入林，笨人勤学早成材",
  "成功由日复一日点滴努力汇聚而成",
  "有坚强心志的人，财产可被掠夺，勇气不会被剥夺",
  "迈开脚步，再长的路也不在话下；停滞不前，再短的路也难以到达",
  "梦想从这一刻起，靠自己能成为现实的一部分",
  "为了未来好一点，现在苦一点无妨",
  "只有承担旅途风雨，最终才能守得住彩虹满天",
  "苦想没盼头，苦干才有奔头",
  "所有努力，是为让自己过得充实有追求，而非让别人觉得了不起",
  "坚持伟大事业需要始终不渝的精神",
  "不要垂头丧气，即使失去一切，明天仍在手中",
  "既已踏上道路，任何东西都不应妨碍前行",
  "欲穷千里目，更上一层楼",
  "第一个青春是上天给的，第二个青春靠自己努力",
  "每个梦想，都在现实中坚持不懈才能实现",
  "只要路是对的，就不怕路远",
  "成功秘诀之一是不让暂时挫折击垮我们",
  "只有不断找寻机会的人，才会及时把握机会",
  "没有口水与汗水，就没有成功的泪水",
  "勤奋是生命密码，能译出壮丽史诗",
  "志在山顶的人，不会贪念山腰风景",
  "选定一条路，坚定不移走下去，坚定梦想，死胡同也能开辟新道路",
  "所有胜利，与征服自己的胜利相比，都微不足道",
  "人生最宝贵的不是拥有多少物质，而是陪伴身边的人",
  "奋斗者在汗水汇集的江河里，将事业之舟驶到理想彼岸",
  "帮别人的事做完就忘记，别人为自己做的事时时记着",
  "人生不可能总顺心如意，持续朝阳光走，影子就躲在后面，刺眼却表明方向",
  "努力做该做的，不期待回报，做了就别后悔，不做才后悔",
  "天才是百分之一的灵感加上百分之九十九的努力",
  "知其不可奈何而安之若命，德之至也。",
  "只有经历地狱般的磨练，才能炼出创造天堂的力量",
  "吾生也有涯，而知也无涯。以有涯随无涯，殆已！",
  "萤火虫光点虽微弱，但亮着便是向黑暗挑战",
  "在很多人看来，失败可耻，其实失败才是常态",
  "人不是因为没有信念而失败，而是因为不能把信念化成行动并坚持到底",
  "一个人失败的最大原因，是对自己能力不敢充分信任，甚至认为必将失败无疑",
  "让花绽放其美，让树展现其姿",
  "欲成参天大树，勿与小草争短长",
  "举世而誉之而不加劝，举世而非之而不加沮。",
  "耐住寂寞时光，守住繁华盛景",
  "持续向上生长，方能闪闪发光",
  "天地有大美而不言，四时有明法而不议，万物有成理而不说。",
  "适合自身需求，便是最佳选择",
  "遵循自己节奏，过好每日生活",
  "井蛙不可以语于海者，拘于虚也；夏虫不可以语于冰者，笃于时也；曲士不可以语于道者，束于教也。",
  "热爱生活点滴，亦被生活厚爱",
  "温柔对待岁月，温暖拥抱世界",
  "心中存有光亮，不惧人生荒凉",
  "莫让今日慵懒，成为明日阻碍",
  "一边奋力拼搏，一边享受快乐",
  "生活本显沉闷，奔跑自有清风",
  "莫因少许负面，影响十足努力",
  "人生天地之间，若白驹之过隙，忽然而已。",
  "莫因缺少掌声，便将梦想抛弃",
  "人人皆是平凡，平凡亦是日常",
  "不走心的努力，实则敷衍自己",
  "易行之路下坡，奋进方为上坡",
  "无人原地等候，全靠自身努力",
  "开心之事自寻，快乐自己给予",
  "你得下定决心，停下手头其他事，把时间、资源、人力都投入进去，才能真的把未来造出来。我们现在就在做这件事。",
  "内心充实富足，独行亦不孤单",
  "努力奋斗模样，藏着父母幸福",
  "努力仅达及格，拼命才会卓越",
  "审视自我认知，愉悦自我身心，超越自我局限",
  "若神未曾助力，只因相信你行",
  "生活没有绝境，只有蓄势待发",
  "人生逆旅多艰，坚韧渡越难关",
  "看似无底深渊，实则前程万里",
  "夜色虽显黑凉，前行定有曙光",
  "无人问津处练，万众瞩目时现",
  "自律带来的苦，会让人生变甜",
  "Be in a STORY",
  "尊重每一个人，但不被其左右",
  "心态决定胜负，观念顺应趋势",
  "欲望激发热忱，毅力战胜艰难",
  "世界如此美好，青春正好绽放",
  "唯有不懈努力，才不负好时光",
  "空想千遍无益，行动一次为佳，即便跌倒亦强，胜过无谓徘徊",
  "点亮自身光芒，照亮前行道路",
  "创佳绩先行动，赢未来勇攀登",
  "道路坎坷无常，磨炼坚毅男儿",
  "人勤春早耕耘，功到秋实收获",
  "前路风和日暖，万物皆可期许",
  "给予一片绿荫，唤醒万物生机",
  "愿春日皆安好，往后尽是繁花",
  "在最美的春光，莫负自我前行",
  "积蓄满满能量，共同攻克难关",
  "前路风和日暖，万物皆可期许",
  "心若花木向阳，人生积极向上",
  "拥有快乐时光，积蓄热爱力量",
  "坚守匠心逐梦，不忘初心前行",
  "心向远方征程，进取永不止步",
  "时光匆匆不停，以梦为马前行，不负青春韶华",
];

interface SloganStore {
  slogan: string;
  loading: boolean;
  error: string | null;
  fetchSlogan: () => void;
}

export const useSloganStore = create<SloganStore>((set) => ({
  slogan: slogans[0],
  loading: false,
  error: null,
  fetchSlogan: () => {
    const randomSlogan = slogans[Math.floor(Math.random() * slogans.length)];
    set({ slogan: randomSlogan });
  },
}));
