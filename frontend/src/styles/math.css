/* 数学公式样式 */
.math-content .katex-display {
  margin: 2em 0;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 1em 0;
  max-width: 100%;
  display: block !important;
}

.math-content .katex {
  font-size: 1.15em;
  line-height: 1.5;
}

.math-content .katex-display > .katex {
  display: block !important;
  text-align: center;
  white-space: nowrap;
}

/* 确保数学公式不被分割 */
.math-content .katex-display .katex-html {
  display: block;
  text-align: center;
}

/* 确保行内公式垂直对齐 */
.math-content .katex-inline,
.math-content .math-inline {
  display: inline-block !important;
  vertical-align: middle !important;
  padding: 0 0.2em !important;
  margin: 0 0.1em !important;
  font-size: 1.05em !important;
  line-height: 1.5 !important;
}

/* 添加背景色，使公式更加突出 */
.math-content .katex-display {
  background-color: rgba(245, 247, 250, 0.7);
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .math-content .katex-display {
  background-color: rgba(30, 41, 59, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 确保公式在移动设备上可以滚动 */
@media (max-width: 768px) {
  .math-content .katex-display {
    max-width: 100%;
    overflow-x: auto;
  }
}

/* 修复特定的 LaTeX 命令显示问题 */
.math-content .katex .mord.mathcal {
  font-family: 'KaTeX_Math', serif;
  font-style: italic;
}

.math-content .katex .mord.mathbb {
  font-family: 'KaTeX_AMS', serif;
}

/* 确保分数正确显示 */
.math-content .katex .mfrac {
  margin: 0 0.2em;
}

/* 确保矩阵正确显示 */
.math-content .katex .mord.mtable {
  vertical-align: middle;
}

/* 确保公式中的文本正确显示 */
.math-content .katex .mord.text {
  font-family: inherit;
  font-style: normal;
}

/* 确保 cases 环境正确显示 */
.math-content .katex .mord.mtable.cases {
  margin: 0.5em 0;
  display: block;
}

/* 确保换行符在 cases 环境中正确显示 */
.math-content .katex .mord.mtable.cases .mtr {
  display: block;
  margin: 0.2em 0;
}

/* 确保对齐环境正确显示 */
.math-content .katex .align {
  display: block;
  text-align: center;
  margin: 1em 0;
}

/* 确保复杂公式能够正确显示 */
.math-content .katex-display .katex {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 0.5em;
}

/* 添加一些额外的空间，使公式更加突出 */
.math-content p:has(.katex-display) {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}

/* 确保行间公式正确显示 */
.math-content .math-display,
.math-content .katex-display {
  display: block !important;
  text-align: center !important;
  margin: 1.5em 0 !important;
  padding: 1em 0 !important;
  overflow-x: auto !important;
  background-color: rgba(245, 247, 250, 0.7) !important;
  border-radius: 6px !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  max-width: 100% !important;
}

.dark .math-content .math-display,
.dark .math-content .katex-display {
  background-color: rgba(30, 41, 59, 0.7) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 确保公式内容居中显示 */
.math-content .katex-display > .katex {
  display: block !important;
  text-align: center !important;
  max-width: 100% !important;
  overflow-x: auto !important;
  padding: 0 1em !important;
}

/* 确保公式内容不会被截断 */
.math-content .katex-display > .katex > .katex-html {
  display: block !important;
  position: relative !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  padding-bottom: 0.5em !important;
  max-width: 100% !important;
}

/* 确保公式内容能够正确换行 */
.math-content .katex-display > .katex > .katex-html > .tag {
  position: relative !important;
  display: block !important;
  text-align: right !important;
  margin-top: 0.5em !important;
}

/* 确保 cases 环境中的内容能够正确对齐 */
.math-content .katex .mord.mtable.cases > .col-align-l {
  text-align: left !important;
}

/* 确保 cases 环境中的文本能够正确显示 */
.math-content .katex .mord.text > .textsf {
  font-family: inherit !important;
  font-style: normal !important;
}

/* 确保公式中的操作符正确显示 */
.math-content .katex .mbin,
.math-content .katex .mrel {
  margin: 0 0.2em;
}
