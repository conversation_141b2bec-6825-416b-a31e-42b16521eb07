/* PDF Text Layer Styles - 基于 PDF.js 官方样式优化 */
.textLayer {
  position: absolute;
  text-align: initial;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  line-height: 1.0;
  text-size-adjust: none;
  forced-color-adjust: none;
  transform-origin: 0 0;
  z-index: 2;

  /* 文本层样式 */
  opacity: 0.25;
  mix-blend-mode: screen;
  pointer-events: none;
  user-select: text;

  /* 确保文本层在 canvas 之上 */
  z-index: 200;
}

.textLayer span,
.textLayer br {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;

  /* 确保文本可选择 */
  pointer-events: all;
  user-select: text;

  /* 添加一些内边距，使选择更容易 */
  padding: 1px;
}

.textLayer .highlight {
  margin: -1px;
  padding: 1px;
  background-color: rgba(180, 0, 170, 0.2);
  border-radius: 4px;
}

.textLayer .highlight.appended {
  position: initial;
}

.textLayer .highlight.begin {
  border-radius: 4px 0 0 4px;
}

.textLayer .highlight.end {
  border-radius: 0 4px 4px 0;
}

.textLayer .highlight.middle {
  border-radius: 0;
}

.textLayer .highlight.selected {
  background-color: rgba(0, 100, 255, 0.3);
}

.textLayer ::selection {
  background: rgba(0, 100, 255, 0.4);
  color: transparent;
}

.textLayer br::selection {
  background: transparent;
}

/* 增强选择样式 */
::selection {
  background: rgba(0, 100, 255, 0.4);
}

/* PDF Viewer specific styles */
.pdf-container {
  position: relative;
}

.pdf-page {
  position: relative;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-pages-wrapper {
  width: 100%;
  padding: 20px 0;
}

/* Make text selectable */
.textLayer span {
  pointer-events: all;
  user-select: text;
  cursor: text;
}

/* PDF pages wrapper */
.pdf-pages-wrapper {
  width: 100%;
}

/* Selection styles */
::selection {
  background: rgba(0, 100, 255, 0.3);
}
