import { useState } from "react";
import { NaviBar } from "@/components/NaviBar";
import { ToolBox } from "@/components/ToolBox";
import { useSloganStore } from "@/store/sloganStore";
import { Jumbotron } from "@/components/Jumbotron";
import { DocumentGraph } from "@/components/document/DocumentGraph";
import { PaperList } from "@/components/document/PaperList";
import { FileText, BookOpen } from "lucide-react";
import { Document } from "@/types/document";

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { PreviewDocumentDialog } from "@/components/document/PreviewDocumentDialog";

export function Documents() {
  const [activeView, setActiveView] = useState<"documents" | "graph">("documents");
  const { slogan } = useSloganStore();
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  const handlePreviewDocument = (document: Document | null) => {
    console.log('handlePreviewDocument called:', document);
    setSelectedDocument(document);
    setIsPreviewVisible(document !== null);
  };

  const handlePreviewVisibilityChange = (visible: boolean) => {
    console.log('Preview visibility changed:', visible);
    setIsPreviewVisible(visible);
    if (!visible) {
      setSelectedDocument(null);
    }
  };

  return (
    <ResizablePanelGroup
      direction="horizontal"
      className="rounded-lg min-h-screen"
    >
      <ResizablePanel
        defaultSize={60}
        minSize={30}
      >
        <div className="min-h-screen bg-background">
          <NaviBar />

          <Jumbotron
            title="Paper Top 100"
            subtitle={slogan}
            bgClassName="bg-white dark:bg-gray-900"
          />

          <div className="border-b border-gray-200 dark:border-neutral-700">
            <nav className="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto" aria-label="Tabs">
              <div className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveView("documents")}
                  className={`
                py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center gap-2
                ${activeView === "documents"
                      ? "border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }
              `}
                >
                  <FileText className="h-4 w-4" />
                  文档库
                </button>
                <button
                  onClick={() => setActiveView("graph")}
                  className={`
                py-4 px-1 border-b-2 font-medium text-sm inline-flex items-center gap-2
                ${activeView === "graph"
                      ? "border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }
              `}
                >
                  <BookOpen className="h-4 w-4" />
                  我的文档图谱
                </button>
              </div>
            </nav>
          </div>

          {/* 内容区域 */}
          {activeView === "documents" ? <PaperList onPreviewDocument={handlePreviewDocument} /> : <DocumentGraph />}

          <ToolBox />
        </div>
      </ResizablePanel>
      <ResizableHandle />
      <ResizablePanel
        defaultSize={40}
        minSize={0}
        className="flex h-screen overflow-hidden"
        style={{ display: isPreviewVisible ? 'block' : 'none' }}
      >
        <PreviewDocumentDialog
          open={isPreviewVisible}
          onOpenChange={handlePreviewVisibilityChange}
          document={selectedDocument}
          className="w-full h-full"
        />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
