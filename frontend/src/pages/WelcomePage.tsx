import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/utils/AuthContext";
import logoImage from '@/assets/3Stooges.jpg';
import { AlertCircle, Clock, LogOut } from 'lucide-react';

export function WelcomePage() {
  const { user, logout } = useAuth();
  const [, setLocation] = useLocation();

  // 如果用户已激活，重定向到首页
  useEffect(() => {
    if (user?.is_active) {
      setLocation('/');
    }
  }, [user, setLocation]);

  const handleLogout = async () => {
    await logout();
    setLocation('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <img
            src={logoImage}
            alt="3Stooges Logo"
            className="mx-auto mb-4 h-16 w-auto"
          />
          <CardTitle className="text-xl">欢迎加入三个臭皮匠</CardTitle>
          <CardDescription>您的账户正在等待管理员激活</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3 p-4 border rounded-lg bg-muted/50">
            <Clock className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
            <div className="space-y-1">
              <h4 className="font-medium">账户待激活</h4>
              <p className="text-sm text-muted-foreground">
                您的账户已成功注册，但需要等待管理员审核并激活。
                激活后您将可以使用所有功能。
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3 p-4 border rounded-lg bg-muted/50">
            <AlertCircle className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
            <div className="space-y-1">
              <h4 className="font-medium">请稍后再试</h4>
              <p className="text-sm text-muted-foreground">
                管理员会尽快处理您的账户激活请求。
                您可以稍后再次登录查看账户状态。
              </p>
            </div>
          </div>
          
          {user && (
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">账户信息</h4>
              <div className="space-y-1 text-sm">
                <p><span className="text-muted-foreground">用户名:</span> {user.username || user.email}</p>
                <p><span className="text-muted-foreground">邮箱:</span> {user.email}</p>
                <p><span className="text-muted-foreground">注册时间:</span> {new Date(user.created_at).toLocaleString()}</p>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={handleLogout}
          >
            <LogOut className="mr-2 h-4 w-4" />
            退出登录
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export default WelcomePage;
