
import { Jumbotron } from "@/components/Jumbotron"
import { NaviBar } from "@/components/NaviBar"
import { PostInput } from "@/components/weibo/PostInput"
import { ContentTabs } from "@/components/weibo/ContentTabs"
import { RightSidebar } from "@/components/weibo/RightSidebar"
import { useState, useEffect } from "react"
import API from "@/config/api"

export function WeiboPage() {
  // 添加状态和fetchPosts函数
  const [posts, setPosts] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // 获取所有帖子
  const fetchPosts = async () => {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(API.POSTS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        // 过滤出类型为 NORMAL 的帖子
        const normalPosts = data.filter((post: any) => post.post.type === 'NORMAL')
        setPosts(normalPosts)
      }
    } catch (error) {
      console.error('获取微博列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取特定帖子
  const fetchTargetPost = async (postId: string) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${API.API_PATH}/posts/${postId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()

        // 将目标帖子添加到帖子列表的最前面
        setPosts(prevPosts => {
          // 检查帖子是否已经在列表中
          const exists = prevPosts.some(post => post.post.id === data.post.id)
          if (exists) {
            return prevPosts
          }
          return [data, ...prevPosts]
        })

        // 设置一个短暂的延时，确保DOM已经更新
        setTimeout(() => {
          const postElement = document.getElementById(`post-${postId}`)
          if (postElement) {
            postElement.scrollIntoView({ behavior: 'smooth' })
            // 添加高亮效果
            postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20')
            // 3秒后移除高亮效果
            setTimeout(() => {
              postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20')
            }, 3000)
          }
        }, 100)
      }
    } catch (error) {
      console.error('获取目标帖子失败:', error)
    }
  }

  // 组件挂载时获取微博列表和处理URL参数
  useEffect(() => {
    fetchPosts()

    // 检查URL中是否有post参数
    const urlParams = new URLSearchParams(window.location.search)
    const postId = urlParams.get('post')
    if (postId) {
      fetchTargetPost(postId)
    }

    // 添加自定义事件监听器，用于处理从侧边栏点击微博的情况
    const handleLoadPost = (event: CustomEvent) => {
      const { postId } = event.detail;
      if (postId) {
        fetchTargetPost(postId);
      }
    };

    // 添加事件监听器
    window.addEventListener('loadPost', handleLoadPost as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('loadPost', handleLoadPost as EventListener);
    };
  }, [])

  return (
    <div className="min-h-screen bg-background">
      <NaviBar />
      <div className="container max-w-screen-2xl mx-auto px-8 py-8 ">
        <Jumbotron title="学术微博"
        // subtitle="分享并传播知识"
        />

        <main className="container grid grid-cols-1 md:grid-cols-12 gap-4 py-4">
          {/* 主内容区 - 扩展宽度 */}
          <div className="md:col-span-9 lg:col-span-9 space-y-4">
            {/* 发布框 */}
            <PostInput onPostSuccess={() => fetchPosts()} />

            {/* 内容标签 */}
            <ContentTabs posts={posts} loading={loading} />
          </div>

          {/* 右侧边栏 */}
          <RightSidebar />
        </main>
      </div>
    </div>
  )
}
