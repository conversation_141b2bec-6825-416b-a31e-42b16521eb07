.thinking-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.thinking-animation .dot {
  width: 4px;
  height: 4px;
  margin: 0 2px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: inline-block;
  animation: thinking 1.4s infinite ease-in-out both;
}

.dark .thinking-animation .dot {
  background-color: #93c5fd;
}

.thinking-animation .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.thinking-animation .dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes thinking {
  0%, 80%, 100% { 
    transform: scale(0);
  }
  40% { 
    transform: scale(1);
  }
}