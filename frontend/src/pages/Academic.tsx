import { useState, useEffect ,useRef} from 'react';
import { cn } from "@/lib/utils";
import { v4 as uuidv4 } from 'uuid';  // 需要安装 uuid 包
import { toast } from "sonner"; // 导入 toast 组件

import { Jumbotron } from "@/components/Jumbotron";
import { NaviBar } from "@/components/NaviBar";
import { ToolBox } from '@/components/ToolBox';
import { ChatInput } from '@/components/ChatInput';
import { ChatMessage } from '@/components/ChatMessage';
import { ChatTimeline } from "@/components/ChatTimeline";
import { SearchResultsGrid } from '@/components/search/SearchResultsGrid';

import { performSearch } from '@/utils/search_utils';
import { systemPrompts } from '@/lib/prompts';
import './thinking-animation.css';

import { useSearchEngineStore } from '@/store/searchEngineStore';
import { useSearchStore } from '@/store/searchStore';
import { useAgentStore } from '@/store/agentStore';
import { useMessageStore } from '@/store/messageStore';
import { useLLMStore } from '@/store/llmStore';
import { useAcademicStore, Message as AcademicMessage } from '@/store/academicStore';

export function Academic() {
  // 使用 academicStore 替代本地状态
  const {
    messages,
    inputValue,
    setInputValue,
    results,
    setResults,
    showSearch,
    setShowSearch,
    isLoading,
    setIsLoading,
    isThinking,
    setIsThinking,
    isOrganizing,
    setIsOrganizing,
    selectedResults,
    addMessage,
    updateLastAssistantMessage,
    clearAll
  } = useAcademicStore();

  const [activeMessageId, setActiveMessageId] = useState<number | null>(null);

  // 添加一个 ref 来追踪是否已经执行过初始对话
  const initialDialogExecuted = useRef(false);

  const params = new URLSearchParams(window.location.search);
  const prompt = params.get('prompt') || '';
  const modelFromUrl = params.get('model');
  const baseUrlFromUrl = params.get('baseUrl');

  // 添加 LLM Store
  const { selectedModel, setSelectedModel } = useLLMStore();

  // 添加状态来存储从URL获取的模型信息
  const [urlModelInfo, setUrlModelInfo] = useState<ModelInfo | null>(null);

  // 修改useEffect，将URL中的模型信息保存到状态中
  useEffect(() => {
    if (modelFromUrl && baseUrlFromUrl) {
      // 直接设置模型信息到 llmStore
      const { providers } = useLLMStore.getState();

      // 查找是否有匹配的提供商
      const matchingProvider = providers.find(p =>
        p.models.some(m => m.id === modelFromUrl)
      );

      if (matchingProvider) {
        // 如果找到匹配的提供商，使用正常的 setSelectedModel 函数
        setSelectedModel(modelFromUrl);
        // console.log('找到匹配的提供商，使用正常的 setSelectedModel 函数');
      } else {
        // 如果没有找到匹配的提供商，手动设置 selectedModel 状态
        useLLMStore.setState({
          selectedModel: {
            id: modelFromUrl,
            providerName: "URL Provider", // 临时提供商名称
            baseUrl: baseUrlFromUrl
          }
        });
        console.log('未找到匹配的提供商，手动设置 selectedModel 状态');
      }

      // 保存完整的模型信息到状态中，用于后续使用
      setUrlModelInfo({
        model: modelFromUrl,
        baseUrl: baseUrlFromUrl
      });

      // console.log('从URL获取模型信息:', {
      //   model: modelFromUrl,
      //   baseUrl: baseUrlFromUrl,
      //   foundMatchingProvider: !!matchingProvider
      // });
    }
  }, [modelFromUrl, baseUrlFromUrl, setSelectedModel]);

  // 添加 messageStore
  const { addMessage: addMessageToStore, setCurrentSessionId } = useMessageStore();
  const sessionId = useRef(uuidv4());  // 为每个对话生成唯一ID

  useEffect(() => {
      setCurrentSessionId(sessionId.current);
  }, [setCurrentSessionId]);

  // 存储上下文
  const { currentAgent } = useAgentStore();

  // 处理初始对话
  useEffect(() => {
    if (prompt && !initialDialogExecuted.current) {
      const params = new URLSearchParams(window.location.search);
      const isInitial = params.get('initial') === 'true';

      if (isInitial) {
        // 如果是从 Home 页面跳转过来的新对话，清空对话历史
        // console.log('检测到从 Home 页面跳转过来的新对话，清空对话历史');
        clearAll();

        // 初始对话时使用URL中的模型信息或默认配置
        const initialModelInfo = {
          baseUrl: baseUrlFromUrl || selectedModel?.baseUrl || import.meta.env.VITE_LLM_HOST || "",
          model: modelFromUrl || selectedModel?.id || import.meta.env.VITE_LLM_MODEL || ""
        };

        // console.log('初始对话使用模型配置:', initialModelInfo);

        // 添加用户消息
        addMessage({ role: 'user', content: safeDecodeURIComponent(prompt) });

        // 提交对话
        handleSubmit(safeDecodeURIComponent(prompt), true, initialModelInfo);
        initialDialogExecuted.current = true;
      } else {
        // 如果不是初始对话，只添加用户消息
        addMessage({ role: 'user', content: safeDecodeURIComponent(prompt) });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prompt, baseUrlFromUrl, modelFromUrl, selectedModel, addMessage, clearAll]);

  const safeDecodeURIComponent = (str: string) => {
    try {
      return decodeURIComponent(str);
    } catch (e) {
      return str;
    }
  };



  const handleTextSelect = (text: string) => {
    setInputValue(text);
  };

  // 在组件顶部添加类型定义
  interface ModelInfo {
    baseUrl: string;
    model: string;
  }



  // 简单估算token数量的函数（每4个字符约为1个token）
  const estimateTokens = (text: string): number => {
    return Math.ceil(text.length / 4);
  };

  // 截断消息内容，确保不超过最大token限制
  const truncateContent = (content: string, maxTokens: number = 1000): string => {
    const estimatedTokens = estimateTokens(content);
    if (estimatedTokens <= maxTokens) {
      return content;
    }

    // 如果超出限制，截断内容
    const ratio = maxTokens / estimatedTokens;
    const truncatedLength = Math.floor(content.length * ratio);
    const truncated = content.substring(0, truncatedLength);

    // console.warn(`内容已被截断，原始估计token: ${estimatedTokens}，截断后估计token: ${maxTokens}`);
    return truncated + "... [内容过长，已截断]";
  };

  // 修改 handleSubmit 函数，确保正确使用传入的模型配置
  const handleSubmit = async (message: string, isInitialMessage = false, modelInfo?: ModelInfo) => {
    setIsLoading(true);
    setIsThinking(true); // 开始思考
    setIsOrganizing(false); // 重置整理状态

    try {
      // 截断用户消息，确保不超过token限制
      const truncatedMessage = truncateContent(message, 1000);

      // 如果消息被截断，在UI中显示提示
      if (truncatedMessage !== message) {
        toast.warning("您的消息过长，已自动截断以适应模型限制");
      }

      // 获取系统提示词
      // 需要处理 agent ID 到 systemPrompts key 的映射
      let systemPromptKey: keyof typeof systemPrompts;

      // 从 agentStore 获取当前 agent 的信息
      const { agents } = useAgentStore.getState();
      const currentAgentInfo = agents[currentAgent];

      // 添加调试日志
      // console.log('当前选择的 Agent:', currentAgent);
      // console.log('Agent 信息:', currentAgentInfo);

      // 根据 agent ID 确定系统提示词的 key
      if (currentAgent === 'academic') {
        // academic agent 使用 anthropic 系统提示词
        systemPromptKey = 'anthropic';
      } else {
        // 其他 agent 直接使用 agent ID 作为 key
        systemPromptKey = currentAgent as keyof typeof systemPrompts;
      }

      const systemPrompt = systemPrompts[systemPromptKey];
      // console.log('使用系统提示词 key:', systemPromptKey);
      // console.log('系统提示词:', systemPrompt);

      const apiMessages = [
        systemPrompt,
        ...(isInitialMessage ? [] : messages),
        { role: 'user', content: truncatedMessage }
      ];

      // Ensure baseUrl and model are properly set with fallbacks
      // 优先使用传入的 modelInfo，其次使用 URL 中的模型信息，再次使用全局选择的模型
      const baseUrl = modelInfo?.baseUrl || urlModelInfo?.baseUrl || baseUrlFromUrl || selectedModel?.baseUrl || import.meta.env.VITE_LLM_HOST || "";
      const model = modelInfo?.model || urlModelInfo?.model || modelFromUrl || selectedModel?.id || import.meta.env.VITE_LLM_MODEL || "";

      // console.log('使用模型配置:', {
      //   baseUrl,
      //   model,
      //   source: modelInfo ? 'modelInfo' : urlModelInfo ? 'urlModelInfo' : baseUrlFromUrl ? 'baseUrlFromUrl' : selectedModel ? 'selectedModel' : 'env'
      // });

      // Validate baseUrl before making the request
      if (!baseUrl) {
        throw new Error('LLM base URL is not configured');
      }

      // 处理 baseUrl 的格式，根据URL格式使用不同的端点格式
      let endpoint;

      // 根据URL格式确定正确的端点
      if (baseUrl.endsWith('/')) {
        // 带尾部斜杠的URL，直接添加chat/completions（不需要/v1/）
        endpoint = `${baseUrl}chat/completions`;
      } else {
        // 不带尾部斜杠的URL，添加/v1/chat/completions
        endpoint = `${baseUrl}/v1/chat/completions`;
      }

      console.log('使用 API 端点:', endpoint);
      console.log('使用模型:', model);

      // 获取当前选中模型的提供商信息
      const { providers } = useLLMStore.getState();
      const provider = providers.find((p: any) =>
        p.models.some((m: any) => m.id === model)
      );

      // 获取 API 密钥
      let apiKey = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'; // 默认密钥

      if (provider && provider.apiKey) {
        apiKey = provider.apiKey;
        console.log('使用提供商 API 密钥');
      } else {
        console.log('使用默认 API 密钥');
      }

      // 准备请求体
      const requestBody = {
        model: model,
        messages: apiMessages,
        stream: true,
        temperature: 0.7,
        max_tokens: 4000
      };

      // console.log('发送请求体:', JSON.stringify(requestBody));

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error('Chat request failed');
      }

      // 只有在非初始化消息时才添加用户消息
      if (!isInitialMessage) {
        // 添加用户消息
        addMessage({ role: 'user', content: message });
        // 添加空的助手消息
        addMessage({ role: 'assistant', content: '' });

        // 在下一个事件循环中滚动到新的 assistant 消息位置
        setTimeout(() => {
          scrollToNewAssistantMessage(messages.length + 1);
        }, 100);
      } else {
        // 初始化消息只添加助手消息
        addMessage({ role: 'assistant', content: '' });

        // 在下一个事件循环中滚动到新的 assistant 消息位置
        setTimeout(() => {
          scrollToNewAssistantMessage(messages.length);
        }, 100);
      }

      // 处理流式响应
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let currentResponse = '';

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          // if (done) break;
          if (done) {
            // 流式响应结束，此时消息已完整，将完整消息添加到 store
            const { currentSessionId, addMessage } = useMessageStore.getState();
            if (currentSessionId) {
              addMessage(currentSessionId, {
                role: 'user',
                content: message
              });
              addMessage(currentSessionId, {
                role: 'assistant',
                content: currentResponse
              });
            }

            // 设置消息完成状态，触发滚动到底部
            setMessageComplete(true);
            break;
          }

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(5));
                if (data.choices && data.choices[0].delta.content) {
                  const newContent = data.choices[0].delta.content;
                  currentResponse += newContent;

                  // 检测是否包含 </think> 标签，如果有则结束思考状态，开始整理回答
                  if (newContent.includes('</think>')) {
                    setIsThinking(false);
                    setIsOrganizing(true);
                  }

                  // 更新最后一条助手消息的内容
                  updateLastAssistantMessage(currentResponse);
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
      // 即使出错也设置消息完成状态，触发滚动到底部
      setMessageComplete(true);
    } finally {
      setIsThinking(false); // 结束思考
      setIsOrganizing(false); // 结束整理
      setIsLoading(false);
    }
  };

  const handleSearch = async (query: string) => {
    setIsLoading(true);
    setIsThinking(false); // 搜索不需要思考状态

    // 截断搜索查询，确保不超过token限制
    const truncatedQuery = truncateContent(query, 1000);

    // 如果查询被截断，在UI中显示提示
    if (truncatedQuery !== query) {
      toast.warning("您的搜索查询过长，已自动截断以适应模型限制");
    }

    try {
      const { results: processedResults, error } = await performSearch(truncatedQuery);

      if (error) {
        console.error('搜索错误:', error);
      } else {
        // 更新搜索结果到 store
        // 将搜索结果转换为 academicStore 中的 SearchResult 类型
        const academicResults = processedResults.map((result: any) => ({
          title: result.title,
          url: result.url,
          snippet: result.snippet
        }));
        setResults(academicResults);

        // 添加搜索消息
        addMessage({
          role: 'user',
          content: `搜索: ${truncatedQuery}`
        });
      }
    } catch (error) {
      console.error('搜索错误:', error);
    } finally {
      setIsLoading(false);
      // 设置消息完成状态，触发滚动到底部
      setMessageComplete(true);
    }
  };

  // 添加 ref 用于滚动控制
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 添加状态来跟踪消息是否完成
  const [messageComplete, setMessageComplete] = useState(false);

  // 添加滚动到底部的函数
  const scrollToBottom = () => {
    // 使用更强制的滚动方式
    if (messagesEndRef.current) {
      // 先尝试使用 scrollIntoView
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });

      // 再使用 setTimeout 确保滚动生效
      setTimeout(() => {
        // 获取包含消息的容器
        const container = document.querySelector('.overflow-y-auto');
        if (container) {
          container.scrollTop = container.scrollHeight;
        }

        // 如果还有外层容器，也尝试滚动
        window.scrollTo(0, document.body.scrollHeight);
      }, 100);
    }
  };

  // 添加滚动到新的 assistant 消息位置的函数
  const scrollToNewAssistantMessage = (index: number) => {
    // 获取消息元素
    const element = document.getElementById(`message-${index}`);
    if (element) {
      // 查找是否有"AI正在回答..."提示
      const thinkingElement = element.parentElement?.querySelector('.thinking-animation')?.closest('.flex');

      if (thinkingElement && isLoading) {
        // 如果有思考动画元素且正在加载，滚动到思考动画元素
        thinkingElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else {
        // 否则滚动到消息元素
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // 添加高亮效果
        element.classList.add('bg-yellow-50', 'dark:bg-yellow-900/20');
        setTimeout(() => {
          element.classList.remove('bg-yellow-50', 'dark:bg-yellow-900/20');
        }, 2000);
      }
    } else {
      // 如果找不到元素，滚动到底部
      scrollToBottom();
    }
  };

  // 只在消息完成时滚动到底部，避免流式输出时的抖动
  useEffect(() => {
    if (messageComplete) {
      // 添加延迟，确保消息已经渲染
      setTimeout(() => {
        scrollToBottom();
        setMessageComplete(false); // 重置状态
      }, 300);
    }
  }, [messageComplete]);

  // 在消息数组更新后，也尝试滚动到底部
  useEffect(() => {
    // 只有当消息数组不为空，且最后一条消息是完整的时候才滚动
    if (messages.length > 0 && messages[messages.length - 1].content) {
      // 添加延迟，确保消息已经渲染
      setTimeout(() => {
        scrollToBottom();
      }, 300);
    }
  }, [messages.length]);

  // 处理时间线项点击
  const handleTimelineClick = (id: number) => {
    setActiveMessageId(id);
    const element = document.getElementById(`message-${id}`);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <NaviBar />

      {/* Floating ChatTimeline */}
      <div className={cn(
        "fixed left-32 top-48 bottom-0 z-40",
      )}>
        <ChatTimeline
          items={messages
            .filter(msg => msg.role === 'user')
            .map((msg, index) => ({
              id: index * 2,
              content: msg.content.slice(0, 50) + (msg.content.length > 50 ? '...' : ''),
              isActive: activeMessageId === index * 2,
              type: 'user' // 添加缺少的 type 属性
            }))}
          onItemClick={handleTimelineClick}
        />
      </div>

      {/* Main Content Container */}
      <div className="flex flex-1 justify-center">
        {/* Main Content */}
        <div className="w-[60%] min-w-[800px] relative">
          {/* Fixed Jumbotron */}
          <div className="sticky top-0 z-30 bg-background/95 backdrop-blur">
            <Jumbotron
              title="Thinking Academic"
              subtitle={safeDecodeURIComponent(prompt).slice(0, 50) + (prompt.length > 50 ? '...' : '')}
            />
          </div>

          {/* Scrollable Content */}
          <div className="overflow-y-auto">
            <div className="h-full overflow-y-auto pb-24 scroll-smooth">
                <div className="container mx-auto px-4 py-8">
                  <div className="max-w-4xl mx-auto space-y-6">
                    <div className="space-y-6">
                      {messages.map((message, index) => {

                        return (
                          <div key={index}>
                            <div
                              id={`message-${index}`}
                              className={cn(
                                "transition-opacity duration-300 ease-in-out",
                                index === messages.length - 1 && message.role === 'assistant' && !message.content
                                  ? "opacity-50"
                                  : "opacity-100"
                              )}
                            >
                              {/* 在助手消息前显示思考/回答状态提示 */}
                              {/* {message.role === 'assistant' && isLoading && isThinking && (
                                <div className="flex justify-end mb-2 animate-fadeIn">
                                  <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <div className="thinking-animation inline-flex">
                                      <div className="dot"></div>
                                      <div className="dot"></div>
                                      <div className="dot"></div>
                                    </div>
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                      思考中...
                                    </span>
                                  </div>
                                </div>
                              )} */}
                              {/* {message.role === 'assistant' && isLoading && isOrganizing && (
                                <div className="flex justify-end mb-2 animate-fadeIn">
                                  <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-gray-50 dark:bg-neutral-800 border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <div className="thinking-animation inline-flex">
                                      <div className="dot"></div>
                                      <div className="dot"></div>
                                      <div className="dot"></div>
                                    </div>
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                      思考已经结束，正在整理您的问题...
                                    </span>
                                  </div>
                                </div>
                              )} */}

                              <ChatMessage
                                key={index}
                                role={message.role}
                                content={message.content}
                                onTextSelect={handleTextSelect}
                              />
                              {/* 在每个消息后检查是否显示搜索结果 */}
                              {index === messages.length - 1 && showSearch && results?.length > 0 && (
                                <div className="mt-4">
                                  <SearchResultsGrid
                                    results={results}
                                    currentInput={inputValue}
                                    onSelect={(snippets, shouldDisableSearch) => {
                                      const newInput = inputValue
                                        ? `${inputValue}\n\n${snippets}`
                                        : snippets;
                                      setInputValue(newInput);

                                      if (shouldDisableSearch) {
                                        setShowSearch(false);
                                      }
                                    }}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Fixed Footer */}
      <div className="fixed mt-2 bottom-0 left-0 right-0 bg-background border-gray-200 dark:border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="max-w-4xl mx-auto">
            <ChatInput
              onSubmit={(message, modelInfo) => {
                // 添加更详细的日志
                // console.log('ChatInput onSubmit 被调用');
                // console.log('消息内容:', message);
                // console.log('接收到的模型信息:', modelInfo);

                // 确保 modelInfo 不为 undefined
                if (!modelInfo) {
                  console.warn('未接收到模型信息，尝试使用 URL 参数或全局选择的模型');

                  // 优先使用 URL 中的模型信息，其次使用全局选择的模型
                  if (urlModelInfo) {
                    console.log('使用 URL 中的模型信息:', urlModelInfo);
                    modelInfo = urlModelInfo;
                  } else if (baseUrlFromUrl && modelFromUrl) {
                    console.log('使用 URL 参数中的模型信息:', { baseUrl: baseUrlFromUrl, model: modelFromUrl });
                    modelInfo = {
                      baseUrl: baseUrlFromUrl,
                      model: modelFromUrl
                    };
                  } else if (selectedModel) {
                    console.log('使用全局选择的模型:', selectedModel);
                    modelInfo = {
                      baseUrl: selectedModel.baseUrl,
                      model: selectedModel.id
                    };
                  } else {
                    console.log('使用环境变量中的默认模型');
                    modelInfo = {
                      baseUrl: import.meta.env.VITE_LLM_HOST || "",
                      model: import.meta.env.VITE_LLM_MODEL || ""
                    };
                  }
                }

                const trimmedMessage = message.trim();
                if (trimmedMessage) {
                  // 截断消息内容，确保不超过token限制
                  // 注意：这里只是预检查，实际截断会在 handleSubmit 和 handleSearch 中进行
                  const estimatedTokens = estimateTokens(trimmedMessage);
                  if (estimatedTokens > 1000) {
                    console.warn(`消息估计token数量(${estimatedTokens})超过限制(1000)，将在处理时截断`);
                  }

                  if (selectedResults.size > 0) {
                    handleSubmit(trimmedMessage, false, modelInfo);
                    setShowSearch(false);
                  } else if (showSearch) {
                    handleSearch(trimmedMessage);
                  } else {
                    handleSubmit(trimmedMessage, false, modelInfo);
                  }
                }
              }}
              // 如果有URL中的模型信息，将其作为默认值传递给ChatInput
              defaultModelInfo={urlModelInfo}
              placeholder={showSearch ? "搜索相关资料..." : "继续提问...（Ctrl+Enter快捷提交）"}
              disabled={isLoading}
              isRequesting={isLoading}
              onStopRequest={() => {
                // 实现停止请求的逻辑
                console.log("用户请求停止生成");
                setIsLoading(false);
                // 如果有正在进行的请求，可以在这里中断它
                // 例如，可以设置一个状态标志，在流式处理循环中检查这个标志
              }}
              value={inputValue}
              onChange={setInputValue}
              onSearchModeChange={(isSearch) => {
                setShowSearch(isSearch);
                if (!isSearch) {
                  setResults([]);
                }
              }}
            />
          </div>
        </div>
      </div>

      <ToolBox />
    </div>
  );
}
