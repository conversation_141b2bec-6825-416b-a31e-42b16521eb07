import { useState, useEffect } from "react";
import { NaviBar } from "@/components/NaviBar";
import { ToolBox } from "@/components/ToolBox";
import { Jumbotron } from "@/components/Jumbotron";
import { useThoughtStore } from "@/store/thoughtsStore";
import { Search, RefreshCcw, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import API from "@/config/api";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { ThoughtCard } from "@/components/thoughts/ThoughtCard";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import ReactMarkdown from "react-markdown";
import { ThoughtDetailDialog } from "@/components/thoughts/ThoughtDetailDialog";
import { ChevronDown, ChevronUp } from "lucide-react";
import { AddThoughtDialog } from "@/components/thoughts/AddThoughtDialog";

export function ThoughtsPage() {
  const { thoughts, loading, fetchThoughts, updateThoughtPin, deleteThought } = useThoughtStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;
  // 修改 editingId 的类型从 number 变为 string
  const [expandedContent, setExpandedContent] = useState<number | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");
  // 添加控制对话框显示的状态
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedThought, setSelectedThought] = useState<any>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  useEffect(() => {
    fetchThoughts();
  }, [fetchThoughts]);

  const handleUpdate = async (id: string, content: string) => {
    try {
      console.log("更新思考:", id, content); // 添加日志
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      // 使用相对路径，确保通过 Vite 的代理配置处理
      const url = `/api/bookmark/${id}`;
      console.log("请求URL:", url); // 添加日志

      const requestBody = {
        desc: content,
      };
      console.log("请求体:", requestBody); // 添加日志

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody),
      });

      console.log("响应状态:", response.status); // 添加日志

      if (response.ok) {
        const responseData = await response.json();
        console.log("响应数据:", responseData); // 添加日志
        fetchThoughts();
        setEditingId(null);
      } else {
        const errorData = await response.json().catch(() => null);
        console.error("更新失败:", errorData); // 添加日志
      }
    } catch (error) {
      console.error("更新失败:", error);
    }
  };

  const filteredThoughts = thoughts.filter(
    (thought) =>
      (thought.desc?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       thought.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       thought.url?.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // 对过滤后的思考进行倒序排序，使最新的思考显示在前面
  // 修改排序逻辑，先按置顶状态排序，再按时间倒序排序
  const sortedThoughts = [...filteredThoughts].sort((a, b) => {
  // 首先按照置顶状态排序
  if (a.is_ticked && !b.is_ticked) return -1;
  if (!a.is_ticked && b.is_ticked) return 1;

  // 然后按照创建时间倒序排序
  if (a.created_at && b.created_at) {
  return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  }
  // 如果没有创建时间，则按ID排序
  return a.id.localeCompare(b.id);
  });

  // 处理置顶/取消置顶的回调函数
  const handlePinToggle = async (id: string, currentPinned: boolean) => {
  try {
  await updateThoughtPin(id, !currentPinned);
  } catch (error) {
  console.error("更新置顶状态失败:", error);
  }
  };

  // 处理删除的回调函数
  const handleDelete = async (id: string) => {
    try {
      if (confirm('确定要删除这个书签吗？')) {
        await deleteThought(id);
      }
    } catch (error) {
      console.error("删除书签失败:", error);
    }
  };

  const totalPages = Math.ceil(sortedThoughts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedThoughts = sortedThoughts.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="min-h-screen bg-background">
      <NaviBar />
      <div className="container max-w-screen-2xl mx-auto px-8 py-8">
        <Jumbotron title="点子" subtitle="捕捉灵感，记录思考" />

        <div className="flex justify-center mb-8">
          <div className="flex gap-4 w-full max-w-xl">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索内容或来源..."
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={() => fetchThoughts()}
              className={`${loading ? "animate-spin" : ""}`}
            >
              <RefreshCcw className="h-4 w-4" />
            </Button>
            {/* 添加新增思考按钮 */}
            <Button
              variant="default"
              size="icon"
              onClick={() => setDialogOpen(true)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 思考卡片网格 */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
              {paginatedThoughts.map((thought, index) => (
                <div
                  key={index}
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedThought(thought);
                    setDetailDialogOpen(true);
                  }}
                >
                  <ThoughtCard
                    content={thought.desc || ''}
                    source={thought.url || ''}
                    created_at={thought.created_at}
                    content_type={thought.content_type}
                    isPinned={thought.is_ticked}
                    onPin={() => handlePinToggle(thought.id, thought.is_ticked)}
                    onDelete={() => handleDelete(thought.id)} // 添加删除处理函数
                  />
                </div>
              ))}
            </div>

            {/* 分页控件 */}
            {filteredThoughts.length > 0 && (
              <Pagination className="justify-center w-full max-w-2xl mx-auto">
                <PaginationContent className="gap-4">
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() =>
                        currentPage > 1 && setCurrentPage((p) => p - 1)
                      }
                      className={
                        currentPage === 1
                          ? "pointer-events-none opacity-50"
                          : ""
                      }
                    />
                  </PaginationItem>

                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter(
                      (page) =>
                        page === 1 ||
                        page === totalPages ||
                        (page >= currentPage - 1 && page <= currentPage + 1)
                    )
                    .map((page, index, array) => (
                      <div key={`pagination-${page}`} className="flex gap-2">
                        {index > 0 && array[index - 1] !== page - 1 && (
                          <PaginationItem key={`ellipsis-${page}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}
                        <PaginationItem>
                          <PaginationLink
                            onClick={() => setCurrentPage(page)}
                            isActive={currentPage === page}
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      </div>
                    ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        currentPage < totalPages && setCurrentPage((p) => p + 1)
                      }
                      className={
                        currentPage === totalPages
                          ? "pointer-events-none opacity-50"
                          : ""
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}

            {/* 无数据提示 */}
            {filteredThoughts.length === 0 && (
              <div className="text-center text-muted-foreground py-12">
                {searchQuery ? "没有找到匹配的记录" : "暂无数据"}
              </div>
            )}
          </>
        )}
      </div>
      <ToolBox />

      {/* 添加思考对话框 */}
      <AddThoughtDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSuccess={fetchThoughts}
      />

      {/* 思考详情对话框 */}
      {selectedThought && (
        <ThoughtDetailDialog
          thought={selectedThought}
          open={detailDialogOpen}
          onOpenChange={setDetailDialogOpen}
          onUpdate={handleUpdate}
        />
      )}
    </div>
  );
}
