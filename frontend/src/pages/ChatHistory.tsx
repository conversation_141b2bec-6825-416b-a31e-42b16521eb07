import { useEffect, useState } from "react";
import { NaviBar } from "@/components/NaviBar";
import { ToolBox } from "@/components/ToolBox";
import { Jumbotron } from "@/components/Jumbotron";
import { Brain, Search, X } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { useSloganStore } from "@/store/sloganStore";
import API from "@/config/api";
import { cn } from "@/lib/utils";

import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { MarkdownComponents } from "@/components/markdown/MarkdownComponents";

import 'katex/dist/katex.min.css';


interface Message {
  id: string;
  session_id: string;
  role: "user" | "assistant";
  content: string;
  created_at: string;
}

interface Session {
  id: string;
  title: string;
  user_id: number;
  created_at: string;
}



export function ChatHistory() {
  // 删除这行
  // const { userId } = mockAuth;
  const [sessions, setSessions] = useState<Session[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeSessionId, setActiveSessionId] = useState<string>();
  const { slogan, fetchSlogan } = useSloganStore();

  const [searchQuery, setSearchQuery] = useState('');

  // 添加过滤逻辑
  const filteredSessions = sessions
    .filter(session =>
      session.title.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const displayedSessions = filteredSessions.slice(0, 10);
  const hasMoreSessions = filteredSessions.length > 10;

  useEffect(() => {
    fetchSlogan();
  }, [fetchSlogan]);

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        const token = localStorage.getItem("token") || sessionStorage.getItem("token");
        const response = await fetch(
          `${API.SESSIONS.LIST}?skip=0&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.ok) {
          throw new Error('获取会话列表失败');
        }

        const data = await response.json();
        setSessions(data.reverse());
        if (data.length > 0) {
          setActiveSessionId(data[0].id);
        }
      } catch (error) {
        console.error("获取会话列表失败:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, []);  // 保持空数组作为依赖

  useEffect(() => {
    const fetchMessages = async () => {
      if (!activeSessionId) return;

      try {
        const token =
          localStorage.getItem("token") || sessionStorage.getItem("token");
        const response = await fetch(
          // 使用 API 配置
          `${API.SESSIONS.MESSAGES(activeSessionId)}?skip=0&limit=100`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.ok) {
          throw new Error('获取消息失败');
        }

        const data = await response.json();
        setMessages(data);
      } catch (error) {
        console.error("获取消息失败:", error);
      }
    };

    fetchMessages();
  }, [activeSessionId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        加载中...
      </div>
    );
  }

  // 获取当前活动会话（可用于显示会话标题等）
  // const activeSession = sessions.find(
  //   (session) => session.id === activeSessionId
  // );

  const processMessageContent = (content: string) => {
    if (!content) {
      return { mainContent: "", thoughtContent: null };
    }

    try {
      const thinkMatch = content.match(/<think>(.*?)<\/think>/s);
      const thoughtContent = thinkMatch ? thinkMatch[1].trim() : null;
      // 只有在存在 think 标签时才移除
      let mainContent = thinkMatch
        ? content.replace(/<think>.*?<\/think>/s, "").trim()
        : content.trim();

      return { mainContent, thoughtContent };
    } catch (error) {
      console.error("处理消息内容时出错:", error);
      return { mainContent: content || "", thoughtContent: null };
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <NaviBar />
      <div className="container max-w-screen-2xl mx-auto px-8 py-8">
        <Jumbotron title="对话历史" subtitle={slogan} />

        <div className="flex gap-8 mt-8">
          <div className="flex-1 space-y-4 ml-20">
            {messages.map((message, index) => {
              console.log(message)
              const { mainContent, thoughtContent } = processMessageContent(
                message.content
              );
              return (
                <div
                  key={index}
                  className={`p-4 rounded-lg ${
                    message.role === "assistant"
                      ? "bg-indigo-50 dark:bg-indigo-900/20"
                      : "bg-gray-50 dark:bg-gray-800"
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {message.role === "assistant" ? "3Stooges" : "You"}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(message.created_at).toLocaleTimeString()}
                      </span>
                    </div>
                    {message.content && (
                      <Popover>
                        <PopoverTrigger asChild>
                          <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full">
                            <Brain className="h-4 w-4 text-purple-500" />
                          </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[640px]">
                          <div className="text-sm text-gray-700 dark:text-gray-300 prose prose-sm dark:prose-invert max-w-none">
                          <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                  components={MarkdownComponents}
                >
                  {thoughtContent}
          </ReactMarkdown>

                          </div>
                        </PopoverContent>
                      </Popover>
                    )}
                  </div>

                  <div className="prose dark:prose-invert max-w-none">
                  <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                  components={MarkdownComponents}
                >
                  {mainContent}
          </ReactMarkdown>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="w-64 border-s-2 border-gray-200 dark:border-neutral-700">
            <div className="p-4 border-b border-gray-200 dark:border-neutral-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="搜索对话..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 text-sm rounded-md border
                           border-input bg-transparent
                           focus:outline-none focus:ring-2
                           focus:ring-ring placeholder:text-muted-foreground"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
                  >
                    <X className="h-4 w-4 text-muted-foreground" />
                  </button>
                )}
              </div>
            </div>

            <nav className="ms-0.5 flex flex-col space-y-3 p-4">
              {displayedSessions.map((session) => (
                <a
                  key={session.id}
                  className={`py-1 ps-4 inline-flex items-center gap-2 border-s-2
                    ${
                      activeSessionId === session.id
                        ? "border-blue-500 text-blue-600 dark:text-blue-500 font-medium"
                        : "border-transparent text-gray-500 hover:text-blue-600 dark:text-neutral-500 dark:hover:text-blue-500"
                    } text-sm whitespace-nowrap focus:outline-none`}
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setActiveSessionId(session.id);
                  }}
                >
                  <span className="truncate">{session.title}</span>
                  <span className="text-xs text-gray-400">
                    {new Date(session.created_at).toLocaleDateString()}
                  </span>
                </a>
              ))}
              {hasMoreSessions && (
                <p className="text-xs text-muted-foreground text-right px-4">
                  还有 {filteredSessions.length - 10} 条对话...
                </p>
              )}
            </nav>
          </div>
        </div>
      </div>
      <ToolBox />
    </div>
  );
}
