import React, { useEffect, useState } from 'react'; // 添加 useState 导入
import { useLocation } from 'wouter';
import { Logo } from '@/components/Logo';
import { Footer } from '@/components/Footer';
import { NaviBar } from "@/components/NaviBar";
import { useSloganStore } from '@/store/sloganStore';
import { useSearchEngineStore } from '@/store/searchEngineStore';
import { useHistoryStore } from '@/store/historyStore';
import { ToolBox } from '@/components/ToolBox';
import { ChatInput } from '@/components/ChatInput';
import { useLLMStore } from '@/store/llmStore';

export function Home() {
  const [, setLocation] = useLocation();
  const { slogan, fetchSlogan } = useSloganStore();
  const { fetchEngines } = useSearchEngineStore();
  const { startNewConversation } = useHistoryStore();
  const { selectedModel, fetchProviders } = useLLMStore();

  useEffect(() => {
    fetchEngines().then(() => {
      console.log('可用的搜索引擎:', useSearchEngineStore.getState().engines);
    });
    fetchSlogan();
    fetchProviders(); // 加载模型信息
  }, [fetchSlogan, fetchProviders]);


  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <NaviBar />
      <div className="w-full max-w-3xl px-4 animate-fade-in">
        <div className="flex flex-col items-center mb-8">
          <Logo mb-4 />
          <p className="text-lg text-gray-400 dark:text-gray-400 mt-2 text-center">
            {slogan} 
          </p>
        </div>

        <ChatInput
          onSubmit={(message, modelInfo) => {
            const trimmedMessage = message.trim();
            if (trimmedMessage) {
              // setIsRequesting(true); // 设置请求状态为进行中
              const conversationId = startNewConversation();
              if (conversationId) {
                const state = useHistoryStore.getState();
                state.addMessage(conversationId, {
                  type: 'user',
                  content: trimmedMessage
                });
              }

              // 构建URL参数，包括模型信息
              let url = `/academic?prompt=${encodeURIComponent(trimmedMessage)}&initial=true`;

              // 如果有选择的模型，添加模型信息
              if (modelInfo) {
                url += `&model=${encodeURIComponent(modelInfo.model)}&baseUrl=${encodeURIComponent(modelInfo.baseUrl)}`;
              } else if (selectedModel) {
                // 如果没有直接选择模型，但有全局选择的模型，使用全局选择的模型
                url += `&model=${encodeURIComponent(selectedModel.id)}&baseUrl=${encodeURIComponent(selectedModel.baseUrl)}`;
              }

              setLocation(url);
              // 注意：由于页面跳转，这里的状态可能不会被重置，但在新页面会重新渲染组件
            }
          }}
          placeholder="输入你的问题...（Ctrl+Enter）"
          disabled={false}
          hideSearchToggle={true}  // 在首页显式设置为 true
          // isRequesting={isRequesting} // 传递请求状态
          // onStopRequest={handleStopRequest} // 传递停止请求回调
        />

        <Footer />
      </div>
      <ToolBox />
    </div>
  );
}
