import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { MarkdownComponents } from "@/components/markdown/MarkdownComponents";

interface ThinkPopoverProps {
  thinkContent: string | null;
}

export function ThinkPopover({ thinkContent }: ThinkPopoverProps) {
  if (!thinkContent) {
    return null;
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="p-1 ml-2 h-6 hover:bg-yellow-200 dark:hover:bg-yellow-900/50"
          title="查看思考内容"
        >
          <Brain className="h-3 w-3" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 max-h-[400px] overflow-y-auto">
        <div className="space-y-2">
          <h4 className="text-sm font-medium border-b pb-1">思考内容</h4>
          <div className="prose prose-sm dark:prose-invert max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={MarkdownComponents}
            >
              {thinkContent}
            </ReactMarkdown>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
