//图库中点击图片查看详细信息组件

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Diagram } from "@/types"
import { EditableField } from "../EditableField";
import { useState, useRef, useEffect } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Maximize2, Minimize2, X } from "lucide-react";

interface DiagramDetailProps {
  diagram: Diagram;
  children: React.ReactNode;
}



// 2. 移除 useState 和 isFullscreen 状态
export function DiagramDetail({ diagram, children }: DiagramDetailProps) {
  const [showFullImage, setShowFullImage] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      // 进入全屏模式
      containerRef.current?.requestFullscreen().catch(err => {
        console.error(`全屏模式错误: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      // 退出全屏模式
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!showFullImage) return;

      // ESC 键关闭图片查看器 (除了全屏模式下，因为ESC会退出全屏)
      if (e.key === 'Escape' && !isFullscreen) {
        setShowFullImage(false);
      }

      // F 键切换全屏
      if (e.key === 'f' || e.key === 'F') {
        toggleFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [showFullImage, isFullscreen, toggleFullscreen]);
  const handleUpdate = async (field: string, value: string) => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录，请先登录');
      }

      // 使用相对路径
      const response = await fetch(`/api/diagram/${diagram.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        },
        body: JSON.stringify({ [field]: value }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || '更新失败');
      }

      console.log('更新成功:', field, value);

    } catch (error) {
      console.error('Update error:', error);
      throw new Error(error instanceof Error ? error.message : '更新失败');
    }
  };

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          {children}
        </PopoverTrigger>
        <PopoverContent side="right" align="start" className="w-[800px] p-4 max-h-[80vh] overflow-y-auto">
          <div className="space-y-4">
            <div className="relative">
              <img
                src={diagram.image_url}
                alt={diagram.name}
                className="w-full rounded-lg object-contain cursor-zoom-in"
                onClick={() => setShowFullImage(true)}
              />
            </div>

            <div className="space-y-4">
              <EditableField
                label="名称"
                value={diagram.name}
                onUpdate={(value) => handleUpdate('name', value)}
              />

              <EditableField
                label="描述"
                value={diagram.description}
                isTextarea
                onUpdate={(value) => handleUpdate('description', value)}
              />

              {diagram.source && (
                <EditableField
                  label="来源"
                  value={diagram.source}
                  onUpdate={(value) => handleUpdate('source', value)}
                />
              )}

              {diagram.paper_title && (
                <div>
                  <h4 className="text-sm font-medium mb-1">论文标题</h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{diagram.paper_title}</p>
                </div>
              )}

              {/* {diagram.paper_authors && (
                <div>
                  <h4 className="text-sm font-medium mb-1">作者：{diagram.paper_authors}</h4>
                </div>
              )} */}
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <Dialog open={showFullImage} onOpenChange={setShowFullImage}>
        <DialogContent className="max-w-[98vw] max-h-[98vh] p-0 overflow-hidden">
          <DialogHeader className="absolute top-0 left-0 right-0 z-10 bg-background/80 backdrop-blur-sm flex justify-between items-center pr-4">
            <DialogTitle className="ml-4 mt-2 mb-2">详图</DialogTitle>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                className="h-8 w-8"
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowFullImage(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>
          <div
            ref={containerRef}
            className={`h-[98vh] w-full flex flex-col items-center justify-center ${isFullscreen ? 'bg-black' : ''}`}
          >
            <img
              ref={imageRef}
              src={diagram.image_url}
              alt={diagram.name}
              className={`max-w-full max-h-[90vh] object-contain transition-transform duration-200 ${isFullscreen ? 'max-h-screen' : ''}`}
              style={{ objectFit: 'contain' }}
            />

            {/* 快捷键提示 */}
            <div className="text-xs text-gray-500 mt-2 opacity-70">
              按 <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded">F</kbd> 切换全屏 |
              按 <kbd className="px-1 py-0.5 bg-gray-200 dark:bg-gray-700 rounded">ESC</kbd> 关闭
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
