import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Code2, Eye, PenLine, Copy, Download, Loader2 } from 'lucide-react';

import { useForm } from "react-hook-form";
import ReactMarkdown from 'react-markdown';
import { systemPrompts } from '@/lib/prompts';
import {MermaidDiagram} from '@lightenna/react-mermaid-diagram'

import { useFlowchartStore } from '@/store/flowchartStore';
import { useDialogStore } from '@/store/dialogStore';
import { useLLMStore } from '@/store/llmStore';

// import { useMermaid } from '@/hooks/useMermaid'

import { download } from '@/utils/download';
import styled from 'styled-components'
import {
  DropdownMenu,
  Dropdown<PERSON><PERSON>u<PERSON>ontent,
  Dropdown<PERSON>enu<PERSON>tem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// import { useAuth } from "@/utils/auth/AuthContext";
// import { generateThumbnail } from '@/utils/imageUtils';

interface AddFlowchartDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddFlowchartDialog({ open, onOpenChange }: AddFlowchartDialogProps) {
  const [code, setCode] = useState<string>('graph TD\nA-->B');

  const [activeTab, setActiveTab] = useState<string>('edit');
  const [modelResponse, setModelResponse] = useState<string>('');
  // 移除 selectedEndpoint 状态，直接使用 selectedModel
  const [diagramType, setDiagramType] = useState<'mermaid' | 'plantuml' | 'drawio'>('mermaid');
  const { pendingText, clearPendingText } = useFlowchartStore();
  const { isFlowchartOpen, setFlowchartOpen } = useDialogStore();
  const { providers, selectedModel } = useLLMStore();

  // 获取所有可用的模型列表
  const availableModels = providers.flatMap(provider =>
    provider.enabled ? provider.models.map(model => ({
      id: model.id,
      name: model.name,
      providerId: provider.id,
      providerName: provider.name
    })) : []
  );

  // 如果没有选中模型，默认选择第一个可用模型
  const [selectedModelId, setSelectedModelId] = useState<string>(selectedModel || (availableModels.length > 0 ? availableModels[0].id : ''));

  const [mermaidKey, setMermaidKey] = useState(0);
  // const { userId } = useAuth();

  // 获取选中模型的提供商信息
  const getSelectedModelInfo = () => {
    // console.log('getSelectedModelInfo 被调用');
    // console.log('当前选中的模型:', selectedModelId);
    // console.log('可用的提供商:', providers);
    // console.log('可用的模型:', availableModels);

    if (!selectedModelId) {
      // console.log('没有选中模型，使用默认配置');
      return {
        url: `${import.meta.env.VITE_LLM_HOST || ""}/v1/chat/completions`,
        model: `${import.meta.env.VITE_LLM_MODEL || ""}`,
        key: `${import.meta.env.VITE_LLM_API_KEY || ""}`
      };
    }

    // 查找包含选中模型的提供商
    const provider = providers.find(p =>
      p.models.some(m => m.id === selectedModelId)
    );

    // console.log('找到的提供商:', provider);

    if (provider) {
      // 确保 URL 路径末尾包含 v1/chat/completions
      const baseUrl = provider.baseUrl || `${import.meta.env.VITE_LLM_HOST || ""}`;
      const apiUrl = baseUrl.endsWith('/')
        ? `${baseUrl}v1/chat/completions`
        : `${baseUrl}/v1/chat/completions`;

      const modelInfo = {
        url: apiUrl,
        model: selectedModelId,
        key: provider.apiKey || `${import.meta.env.VITE_LLM_API_KEY || ""}`
      };

      // 添加日志输出
      // console.log('选中的模型信息:', {
      //   provider: provider.name,
      //   baseUrl: provider.baseUrl,
      //   apiUrl: apiUrl,
      //   model: selectedModelId
      // });

      return modelInfo;
    }

    // console.log('未找到匹配的提供商，使用默认配置');
    // 如果没有找到，返回默认值
    return {
      url: `${import.meta.env.VITE_LLM_HOST || ""}/v1/chat/completions`,
      model: `${import.meta.env.VITE_LLM_MODEL || ""}`,
      key: `${import.meta.env.VITE_LLM_API_KEY || ""}`
    };
  };

  // 更新 useEffect，当 selectedModel 变化时更新 selectedModelId
  useEffect(() => {
    if (selectedModel) {
      setSelectedModelId(selectedModel);
    }
  }, [selectedModel]);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<{ prompt: string }>();


  useEffect(() => {
    if (isFlowchartOpen && pendingText) {
      setValue('prompt', pendingText);
      clearPendingText();
    }
  }, [isFlowchartOpen, pendingText, setValue, clearPendingText]);

  // 修改 AI_CONFIG，移除 deepseek 选项
  const AI_CONFIG = {
    modelInfo: getSelectedModelInfo(),
    temperature: 0.6,
    max_tokens: 2000,
  } as const;

  const generateFlowchart = async (prompt: string): Promise<AsyncIterable<string>> => {
    // 每次生成时重新获取最新的模型信息
    const modelInfo = getSelectedModelInfo();
    // console.log('使用的模型信息:', modelInfo);

    const response = await fetch(modelInfo.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(modelInfo.key && { 'Authorization': `Bearer ${modelInfo.key}` })
      },
      body: JSON.stringify({
        model: modelInfo.model,
        messages: [
          {
            role: 'system',
            content: diagramType === 'mermaid'
              ? systemPrompts.mermaid.content
              : diagramType === 'plantuml'
                ? systemPrompts.plantuml.content
                : systemPrompts.drawio.content
          },
          {
            role: 'user',
            content: diagramType === 'mermaid'
              ? `${prompt}，请根据以下描述生成 Mermaid 流程图代码：`
              : diagramType === 'plantuml'
                ? `${prompt}，请根据以下描述生成 PlantUML 流程图代码：`
                : `${prompt}，请根据以下描述生成 Draw.io XML 格式的流程图代码：`
          }
        ],
        temperature: AI_CONFIG.temperature,
        max_tokens: AI_CONFIG.max_tokens,
        stream: true,
      }),
    });

    console.log('API 响应状态:', response.status);

    if (!response.body) {
      throw new Error('No response body');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    return {
      [Symbol.asyncIterator]() {
        return {
          async next() {
            const { done, value } = await reader.read();
            if (done) {
              return { done: true, value: null };
            }
            const chunk = decoder.decode(value);
            return { done: false, value: chunk };
          }
        };
      }
    };
  };

  const onSubmit = async (data: { prompt: string }) => {
    try {
      // 重置状态
      setCode('graph TD...');
      setModelResponse('');
      setActiveTab('edit');
      const stream = await generateFlowchart(data.prompt);
      let fullResponse = '';

      // 修改 onSubmit 函数中的解析部分
      for await (const chunk of stream) {
        try {
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5).trim();

              // 检查是否是结束标记
              if (data === '[DONE]') {
                continue;
              }

              try {
                const jsonData = JSON.parse(data);
                if (jsonData.choices[0].delta.content) {
                  const content = jsonData.choices[0].delta.content;
                  fullResponse += content;

                  if (diagramType === 'mermaid') {
                    // 检查是否包含 Mermaid 代码块，并确保添加 graph TD 头
                    const mermaidMatch = fullResponse.match(/```mermaid\n([\s\S]*?)```/);
                    if (mermaidMatch) {
                      const mermaidCode = mermaidMatch[1].trim();
                      setCode(mermaidCode.startsWith('graph TD') ? mermaidCode : `graph TD\n${mermaidCode}`);
                      const explanation = fullResponse.replace(/```mermaid\n[\s\S]*?```/, '');
                      setModelResponse(explanation.trim());
                    } else {
                      setModelResponse(fullResponse.trim());
                    }
                  } else if (diagramType === 'plantuml') {
                    // PlantUML 代码块处理
                    const plantumlMatch = fullResponse.match(/```plantuml\n([\s\S]*?)```/);
                    if (plantumlMatch) {
                      setCode(plantumlMatch[1].trim());
                      const explanation = fullResponse.replace(/```plantuml\n[\s\S]*?```/, '');
                      setModelResponse(explanation.trim());
                    } else {
                      setModelResponse(fullResponse.trim());
                    }
                  }else {
                    // Drawio 代码块处理
                    const drawioMatch = fullResponse.match(/<mxfile>[\s\S]*?<\/mxfile>/);
                    if (drawioMatch) {
                      setCode(drawioMatch[0]);
                      const explanation = fullResponse.replace(/<mxfile>[\s\S]*?<\/mxfile>/, '');
                      setModelResponse(explanation.trim());
                    } else {
                      setModelResponse(fullResponse.trim());
                    }
                  }
                }
              } catch (parseError) {
                console.error('JSON 解析失败:', data, parseError);
              }
            }
          }
        } catch (e) {
          console.error('处理数据块失败:', e);
        }
      }

      setActiveTab('preview');
    } catch (error) {
      console.error('生成流程图失败:', error);
      setModelResponse('生成失败，请重试');
    }
  };

  const handleDialogChange = (open: boolean) => {
    if (!open) {
      setCode('graph TD...');
      setModelResponse('');
      setActiveTab('edit');
    }
    onOpenChange(open);
  };

  // 修改 handleDownload 函数中的保存逻辑
  const handleDownload = async (format: 'svg' | 'png' | 'save') => {
    try {
      if (format === 'save') {
        const element = document.querySelector('.mermaid-diagram svg');
        if (!element) {
          console.log('未找到图表元素');
          return;
        }

        // 生成 PNG 文件
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const svgData = new XMLSerializer().serializeToString(element);
        const img = new Image();

        await new Promise((resolve, reject) => {
          img.onload = () => {
            // 获取 SVG 的实际尺寸
            const svgRect = element.getBoundingClientRect();
            const width = svgRect.width;
            const height = svgRect.height;

            // 设置画布尺寸
            const scale = 3;
            canvas.width = width * scale;
            canvas.height = height * scale;

            if (ctx) {
              // 设置白色背景
              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, canvas.width, canvas.height);

              // 缩放并绘制图像
              ctx.scale(scale, scale);
              ctx.drawImage(img, 0, 0, width, height);
            }
            resolve(null);
          };
          img.onerror = reject;

          // 创建 SVG blob URL
          const blob = new Blob([svgData], { type: 'image/svg+xml' });
          img.src = URL.createObjectURL(blob);
        });

        // 转换为 PNG 文件
        const blob = await new Promise<Blob>((resolve) => {
          canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0);
        });
        const file = new File([blob], 'diagram.png', { type: 'image/png' });

        // 生成缩略图
        const thumbnail = await generateThumbnail(file);

        const formData = new FormData();
        // 获取当前的 prompt 值
        const promptValue = register("prompt").value;

        // 添加必填字段
        formData.append('file', file);  // 文件对象
        formData.append('thumbnail', thumbnail);  // base64 字符串
        formData.append('name', promptValue || '未命名图表');
        formData.append('type', 'flowchart');
        formData.append('user_id', userId?.toString() || '');
        formData.append('description', code || '');  // 使用 AI 的解释作为描述
        formData.append('source', 'AI生成');
        formData.append('content_type', diagramType);  // 使用当前选择的图表类型
        formData.append('data', file);
        formData.append('file_size', file.size.toString());

        // 添加可选字段
        formData.append('paper_doi', '');
        formData.append('paper_title', '');
        formData.append('paper_authors', '');
        formData.append('page_number', '0');

        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/diagrams/`, {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();
        console.log(formData)
        console.log(result)

    if (!response.ok) {
      throw new Error(result.detail || '保存失败');
    }

        // alert('保存成功');
        return;
      }

      // 原有的下载逻辑保持不变
      const element = document.querySelector('.mermaid-diagram')
      if (!element) {
        console.log('未找到图表元素');
        return;
      }

      const timestamp = Date.now()

      if (format === 'svg') {
        const svgElement = element.querySelector('svg')
        if (!svgElement) return
        const svgData = new XMLSerializer().serializeToString(svgElement)
        const blob = new Blob([svgData], { type: 'image/svg+xml' })
        const url = URL.createObjectURL(blob)
        download(url, `mermaid-diagram-${timestamp}.svg`)
        URL.revokeObjectURL(url)
      } else if (format === 'png') {
        const svgElement = element.querySelector('svg')
        if (!svgElement) return

        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const img = new Image()
        img.crossOrigin = 'anonymous'

        const viewBox = svgElement.getAttribute('viewBox')?.split(' ').map(Number) || []
        const width = viewBox[2] || svgElement.clientWidth || svgElement.getBoundingClientRect().width
        const height = viewBox[3] || svgElement.clientHeight || svgElement.getBoundingClientRect().height

        const svgData = new XMLSerializer().serializeToString(svgElement)
        const svgBase64 = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svgData)))}`

        img.onload = () => {
          const scale = 3
          canvas.width = width * scale
          canvas.height = height * scale

          if (ctx) {
            ctx.scale(scale, scale)
            ctx.drawImage(img, 0, 0, width, height)
          }

          canvas.toBlob((blob) => {
            if (blob) {
              const pngUrl = URL.createObjectURL(blob)
              download(pngUrl, `mermaid-diagram-${timestamp}.png`)
              URL.revokeObjectURL(pngUrl)
            }
          }, 'image/png')
        }
        img.src = svgBase64
      }
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  // 在预览部分修改导出按钮
  return (
    <Dialog
      open={isFlowchartOpen}
      onOpenChange={setFlowchartOpen}
    >
      <DialogContent
        className="max-w-4xl"
        aria-describedby="diagram-dialog-description"
      >
        <DialogHeader>
          <DialogTitle>生成流程图</DialogTitle>
          <p id="diagram-dialog-description" className="sr-only">
            使用 AI 生成流程图，支持 Mermaid、PlantUML 和 Draw.io 格式
          </p>
          <div className="mt-2 flex justify-center">
            <div className="inline-flex rounded-lg border border-gray-200 p-1">
              <button
                type="button"
                onClick={() => setDiagramType('mermaid')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md ${
                  diagramType === 'mermaid'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-500 hover:text-blue-600'
                }`}
              >
                Mermaid
              </button>
              <button
                type="button"
                onClick={() => setDiagramType('plantuml')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md ${
                  diagramType === 'plantuml'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-500 hover:text-blue-600'
                }`}
              >
                PlantUML
              </button>
              <button
                type="button"
                onClick={() => setDiagramType('drawio')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md ${
                  diagramType === 'drawio'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-500 hover:text-blue-600'
                }`}
              >
                Draw.io
              </button>
            </div>
          </div>
        </DialogHeader>

        <div className="border-b border-gray-200 dark:border-neutral-700">
          <nav className="flex gap-x-1" aria-label="Tabs" role="tablist">
            <button
              type="button"
              onClick={() => setActiveTab('edit')}
              className={`py-4 px-1 inline-flex items-center gap-x-2 border-b-2 text-sm whitespace-nowrap ${
                activeTab === 'edit'
                  ? 'font-semibold border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-blue-600'
              }`}
              role="tab"
            >
              <PenLine className="h-4 w-4" />
              生成
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('preview')}
              className={`py-4 px-1 inline-flex items-center gap-x-2 border-b-2 text-sm whitespace-nowrap ${
                activeTab === 'preview'
                  ? 'font-semibold border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-blue-600'
              }`}
              role="tab"
            >
              <Eye className="h-4 w-4" />
              预览
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('code')}
              className={`py-4 px-1 inline-flex items-center gap-x-2 border-b-2 text-sm whitespace-nowrap ${
                activeTab === 'code'
                  ? 'font-semibold border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-blue-600'
              }`}
              role="tab"
            >
              <Code2 className="h-4 w-4" />
              代码
            </button>
          </nav>
        </div>

        <div className="mt-3">
          <div className={activeTab === 'edit' ? '' : 'hidden'}>
            <div className="grid grid-cols-2 gap-4">
              <Textarea
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="min-h-[200px] font-mono"
                placeholder="输入 Mermaid 流程图代码..."
              />
              <div
                className="prose max-w-none border rounded-lg p-4 overflow-y-auto"
                style={{
                  height: '200px',
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#888 #f1f1f1'
                }}
              >
                <ReactMarkdown>{modelResponse}</ReactMarkdown>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="mt-4">
              <div className="relative">
                <div className="flex gap-2">
                  <select
                    value={selectedModelId}
                    onChange={(e) => setSelectedModelId(e.target.value)}
                    className="rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm w-48"
                  >
                    {availableModels.length > 0 ? (
                      availableModels.map(model => (
                        <option key={model.id} value={model.id}>
                          {model.name} ({model.providerName})
                        </option>
                      ))
                    ) : (
                      <option value="">默认模型</option>
                    )}
                  </select>

                  <div className="flex-1 relative">
                    <Input
                      {...register("prompt", { required: true })}
                      placeholder="输入文字描述..."
                      className="pr-24"
                    />
                    <div className="absolute right-1 top-1 z-10">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        size="sm"
                      >
                        {isSubmitting && (
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        生成
                      </Button>
                    </div>
                  </div>
                </div>

                {errors.prompt && (
                  <span className="text-sm text-red-500 mt-1 block">请输入描述</span>
                )}
              </div>
            </form>

          </div>
          <div className={activeTab === 'preview' ? '' : 'hidden'}>
            <div className="relative border rounded-lg p-4 bg-white overflow-y-auto" style={{
              maxHeight: '800px',
              minHeight: '400px'
            }}>
              <div className="absolute top-2 right-2 z-10">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="p-1.5 h-auto"
                      title="导出图片"
                    >
                      <Download className="h-4 w-4 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onSelect={() => handleDownload('svg')}>
                      导出 SVG
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => handleDownload('png')}>
                      导出 PNG
                    </DropdownMenuItem>
                    <DropdownMenuItem onSelect={() => handleDownload('save')}>
                      保存到数据库
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="w-full h-full flex items-center justify-center">
                <div className="w-full">
                  {/* {code && code !== 'graph TD...' && (
                    <StyledMermaid className="mermaid mermaid-diagram" key={mermaidKey}>
                      {code}
                    </StyledMermaid>
                  )} */}
                  <MermaidDiagram className="h-full mermaid-diagram [&_text]:!font-sans [&_text]:!fill-current [&_text]:!font-normal">
                    {code}
                  </MermaidDiagram>
                </div>
              </div>
            </div>
          </div>
          <div className={activeTab === 'code' ? '' : 'hidden'}>
            <div className="relative">
              <button
                onClick={() => {
                  navigator.clipboard.writeText(code);
                }}
                className="absolute top-2 right-2 p-1.5 rounded-md hover:bg-gray-200 transition-colors"
                title="复制代码"
              >
                <Copy className="h-4 w-4 text-gray-500" />
              </button>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto whitespace-pre" style={{ maxHeight: '400px' }}>
                <code className="block">{code}</code>
              </pre>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

const StyledMermaid = styled.div`
  max-height: calc(80vh - 200px);
  min-height: 200px;
  text-align: center;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;

  svg {
    max-width: 100%;
    height: auto;
  }
`
