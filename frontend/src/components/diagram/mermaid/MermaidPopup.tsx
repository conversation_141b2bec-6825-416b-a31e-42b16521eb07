// TODO：废弃

// import { TopView } from '@renderer/components/TopView'
import { download } from '@/lib/utils'
// import { Button, Modal, Space, Tabs } from 'antd'
import { useEffect, useState } from 'react'
// import { useTranslation } from 'react-i18next'
import styled from 'styled-components'

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ShowParams {
  chart: string
}

interface Props extends ShowParams {
  resolve: (data: any) => void
}

const PopupContainer: React.FC<Props> = ({ resolve, chart }) => {
  const [open, setOpen] = useState(true)
  // const { t } = useTranslation()
  const mermaidId = `mermaid-popup-${Date.now()}`
  const [activeTab, setActiveTab] = useState('preview')
  const [scale, setScale] = useState(1)

  const onOk = () => {
    setOpen(false)
  }

  const onCancel = () => {
    setOpen(false)
  }

  const onClose = () => {
    resolve({})
  }

  const handleZoom = (delta: number) => {
    const newScale = Math.max(0.1, Math.min(3, scale + delta))
    setScale(newScale)

    const element = document.getElementById(mermaidId)
    if (!element) return

    const svg = element.querySelector('svg')
    if (!svg) return

    const container = svg.parentElement
    if (container) {
      container.style.overflow = 'auto'
      container.style.position = 'relative'
      svg.style.transformOrigin = 'top left'
      svg.style.transform = `scale(${newScale})`
    }
  }

  const handleDownload = async (format: 'svg' | 'png') => {
    try {
      const element = document.getElementById(mermaidId)
      if (!element) return

      const timestamp = Date.now()

      if (format === 'svg') {
        const svgElement = element.querySelector('svg')
        if (!svgElement) return
        const svgData = new XMLSerializer().serializeToString(svgElement)
        const blob = new Blob([svgData], { type: 'image/svg+xml' })
        const url = URL.createObjectURL(blob)
        download(url, `mermaid-diagram-${timestamp}.svg`)
        URL.revokeObjectURL(url)
      } else if (format === 'png') {
        const svgElement = element.querySelector('svg')
        if (!svgElement) return

        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const img = new Image()
        img.crossOrigin = 'anonymous'

        const viewBox = svgElement.getAttribute('viewBox')?.split(' ').map(Number) || []
        const width = viewBox[2] || svgElement.clientWidth || svgElement.getBoundingClientRect().width
        const height = viewBox[3] || svgElement.clientHeight || svgElement.getBoundingClientRect().height

        const svgData = new XMLSerializer().serializeToString(svgElement)
        const svgBase64 = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svgData)))}`

        img.onload = () => {
          const scale = 3
          canvas.width = width * scale
          canvas.height = height * scale

          if (ctx) {
            ctx.scale(scale, scale)
            ctx.drawImage(img, 0, 0, width, height)
          }

          canvas.toBlob((blob) => {
            if (blob) {
              const pngUrl = URL.createObjectURL(blob)
              download(pngUrl, `mermaid-diagram-${timestamp}.png`)
              URL.revokeObjectURL(pngUrl)
            }
          }, 'image/png')
        }
        img.src = svgBase64
      }
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(chart)
    window.message.success(('message.copy.success'))
  }

  useEffect(() => {
    window?.mermaid?.contentLoaded()
  }, [])

  // 修改返回的 JSX
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Mermaid 图表预览</DialogTitle>
        </DialogHeader>
  
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">预览</TabsTrigger>
            <TabsTrigger value="source">源代码</TabsTrigger>
          </TabsList>
  
          <TabsContent value="preview" className="mt-4">
            <div className="relative">
              <div className="absolute right-2 top-2 flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handleZoom(0.1)}>
                  放大
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleZoom(-0.1)}>
                  缩小
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleDownload('svg')}>
                  导出 SVG
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleDownload('png')}>
                  导出 PNG
                </Button>
              </div>
              <StyledMermaid id={mermaidId} className="mermaid mt-8">
                {chart}
              </StyledMermaid>
            </div>
          </TabsContent>
  
          <TabsContent value="source" className="mt-4">
            <div className="relative">
              <Button 
                variant="outline" 
                size="sm" 
                className="absolute right-2 top-2"
                onClick={handleCopy}
              >
                复制代码
              </Button>
              <pre className="mt-8 rounded-lg bg-muted p-4">
                {chart}
              </pre>
            </div>
          </TabsContent>
        </Tabs>
  
        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default class MermaidPopup {
  static topviewId = 0
  static hide() {
    // TopView.hide('MermaidPopup')
  }
  static show(props: ShowParams) {
    return new Promise<any>((resolve) => {
        'MermaidPopup'
    })
  }
}

const StyledMermaid = styled.div`
  max-height: calc(80vh - 200px);
  text-align: center;
  overflow-y: auto;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
`
