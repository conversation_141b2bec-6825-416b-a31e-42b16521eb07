// 可以舍弃

import { useEffect, useState } from 'react';
import {
  <PERSON>er,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { useThoughtStore } from '@/store/thoughtsStore';
import { RefreshCcw, Search } from "lucide-react";
import { Button } from "../ui/button";
import { Input } from '../ui/input';
import ReactMarkdown from 'react-markdown';

interface Thought {
  id: number;
  content: string;
  source: string;
  stars: number;
  createdAt: string;
}

interface ThoughtsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  thoughts: Thought[];
}

export function ThoughtsDialog({ open, onOpenChange }: ThoughtsDialogProps) {
  const { thoughts, loading, fetchThoughts } = useThoughtStore();
  const [searchQuery, setSearchQuery] = useState('');
  
  // 更新过滤逻辑，使用 desc、title 和 url 字段
  const filteredThoughts = thoughts.filter(thought => 
    (thought.desc?.toLowerCase().includes(searchQuery.toLowerCase()) || 
     thought.title?.toLowerCase().includes(searchQuery.toLowerCase()) || 
     thought.url?.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  useEffect(() => {
    fetchThoughts();
  }, [fetchThoughts]);

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="w-[700px] h-full">
        <DrawerHeader className="flex flex-col gap-4">
          <div className="flex flex-row justify-between items-center">
            <DrawerTitle>雁过留痕</DrawerTitle>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => fetchThoughts()}
              className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all ${loading ? 'animate-spin' : ''}`}
            >
              <RefreshCcw className="h-4 w-4" />
            </Button>
          </div>
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索内容或来源..."
              className="pl-8"
            />
          </div>
        </DrawerHeader>
        <div className="p-4 space-y-4 overflow-y-auto">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            Array.isArray(filteredThoughts) ? (
              filteredThoughts.length > 0 ? (
                // 更新渲染逻辑，使用新的字段名
                filteredThoughts.map((thought, index) => (
                  <div key={index} className="p-4 border rounded-lg space-y-2">
                    <div className="prose dark:prose-invert prose-sm max-w-none">
                      <ReactMarkdown>{thought.desc || ''}</ReactMarkdown>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      来源：
                      {thought.url ? (
                        <a 
                          href={thought.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {thought.title || thought.url}
                        </a>
                      ) : (
                        thought.title || '未知来源'
                      )}
                    </p>
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground">
                  {searchQuery ? '没有找到匹配的记录' : '暂无数据'}
                </div>
              )
            ) : (
              <div className="text-center text-muted-foreground">暂无数据</div>
            )
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
}
