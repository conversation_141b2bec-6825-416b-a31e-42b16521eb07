import ReactMarkdown from 'react-markdown';
import { Card, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"; // 导入CardFooter
import { Pin, Trash2, Gith<PERSON>, FileText, Presentation, MessageCircle, MessageSquare, Lightbulb } from "lucide-react"; // 导入更多图标
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge"; // 导入Badge组件

interface ThoughtCardProps {
  content: string;
  source?: string;
  created_at?: string;
  content_type?: string;
  onPin?: () => void;
  onDelete?: () => void;
  isPinned?: boolean;
}

export function ThoughtCard({
  content,
  source,
  created_at,
  content_type,
  onPin,
  onDelete,
  isPinned = false
}: ThoughtCardProps) {
  // 根据 content_type 设置不同的背景颜色
  const getBgColor = () => {
    switch (content_type?.toLowerCase()) {
      case 'reddit':
        return 'bg-orange-50 hover:bg-orange-100 dark:bg-orange-950/30 dark:hover:bg-orange-900/40';
      case 'weibo':
        return 'bg-red-50 hover:bg-red-100 dark:bg-red-950/30 dark:hover:bg-red-900/40';
      case 'wechat':
        return 'bg-green-50 hover:bg-green-100 dark:bg-green-950/30 dark:hover:bg-green-900/40';
      case 'idea':
        return 'bg-blue-50 hover:bg-blue-100 dark:bg-blue-950/30 dark:hover:bg-blue-900/40';
      case 'x':
        return 'bg-gray-50 hover:bg-gray-100 dark:bg-gray-800/30 dark:hover:bg-gray-700/40';
      case 'github':
        return 'bg-purple-50 hover:bg-purple-100 dark:bg-purple-950/30 dark:hover:bg-purple-900/40';
      case 'paper':
        return 'bg-yellow-50 hover:bg-yellow-100 dark:bg-yellow-950/30 dark:hover:bg-yellow-900/40';
      case 'web':
        return 'bg-indigo-50 hover:bg-indigo-100 dark:bg-indigo-950/30 dark:hover:bg-indigo-900/40';
      default:
        return 'bg-sky-50 hover:bg-sky-100 dark:bg-green-950/30 dark:hover:bg-green-900/40';
    }
  };

  // 根据 content_type 获取对应的图标
  const getTypeIcon = () => {
    switch (content_type?.toLowerCase()) {
      case 'reddit':
        return <MessageCircle className="h-3 w-3" />;
      case 'weibo':
        return <MessageSquare className="h-3 w-3 text-red-500" />;
      case 'wechat':
        return <MessageSquare className="h-3 w-3 text-green-500" />;
      case 'idea':
        return <Lightbulb className="h-3 w-3 text-blue-500" />;
      case 'x':
        return <MessageCircle className="h-3 w-3 text-gray-500" />;
      case 'github':
        return <Github className="h-3 w-3 text-purple-500" />;
      case 'paper':
        return <FileText className="h-3 w-3 text-yellow-500" />;
      case 'web':
        return <Presentation className="h-3 w-3 text-indigo-500" />;
      default:
        return <Lightbulb className="h-3 w-3 text-sky-500" />;
    }
  };

  // 获取内容类型的显示名称
  const getTypeName = () => {
    switch (content_type?.toLowerCase()) {
      case 'reddit':
        return 'Reddit';
      case 'weibo':
        return '微博';
      case 'wechat':
        return '微信';
      case 'idea':
        return '灵感';
      case 'x':
        return 'X';
      case 'github':
        return 'GitHub';
      case 'paper':
        return '论文';
      case 'web':
        return '网页';
      default:
        return '灵感';
    }
  };

  return (
    <Card className={`h-full transition-colors ${getBgColor()} relative group`}>
      {/* 添加右上角的按钮组 */}
      <div className="absolute top-2 right-2 flex gap-1 z-10">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 rounded-full text-muted-foreground hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity "
          onClick={(e) => {
            e.stopPropagation();
            onDelete && onDelete();
          }}
          title="删除"
        >
          <Trash2 className="h-1 w-1" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className={`h-6 w-6 rounded-full ${
            isPinned
              ? 'text-slate-500 group-hover:text-green-500' // 固定状态下显示琥珀色，悬停时变为绿色
              : 'text-muted-foreground hover:text-green-500 opacity-0 group-hover:opacity-100 transition-opacity'
          }`}
          onClick={(e) => {
            e.stopPropagation();
            onPin && onPin();
          }}
          title={isPinned ? "取消固定" : "固定"}
        >
          <Pin
            className={`h-1 w-1 transition-transform ${isPinned ? 'rotate-15' : ''}`}
            style={{
              transform: isPinned ? 'rotate(15deg)' : 'none',
            }}
          />
        </Button>
      </div>

      <CardContent className="px-4 flex flex-col h-full">
        {/* 添加类型标签 */}
        <div className="flex justify-between items-center mb-2">
          <Badge variant="outline" className="text-xs px-2 py-0 h-5 flex items-center gap-1">
            {getTypeIcon()}
            <span>{getTypeName()}</span>
          </Badge>
        </div>
        <div className="flex-1 overflow-hidden">
          <div className="text-sm line-clamp-5 prose prose-sm dark:prose-invert max-w-none">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
        </div>
      </CardContent>

      {/* 将底部内容移到CardFooter中 */}
      <CardFooter className="px-4 text-xs text-muted-foreground border-t border-border/40">
        <div className="flex justify-between items-center w-full">
          <div className="truncate max-w-[70%]">
            {source && (
              <a
                href={source.match(/^https?:\/\//) ? source : `https://${source}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
                onClick={(e) => e.stopPropagation()}
              >
                {source.replace(/^https?:\/\//, '').split('/')[0]}
              </a>
            )}
          </div>
          <div>{created_at ? created_at.split('T')[0] : ''}</div>
        </div>
      </CardFooter>
    </Card>
  );
}