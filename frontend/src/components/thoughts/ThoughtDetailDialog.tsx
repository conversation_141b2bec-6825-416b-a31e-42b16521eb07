import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Edit2, X, ChevronDown, ChevronUp } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import rehypeRaw from "rehype-raw";
import { cn } from "@/lib/utils";
// 导入 KaTeX 样式
import 'katex/dist/katex.min.css';
// 导入自定义数学公式样式
import '@/styles/math.css';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";

/**
 * 转换数学公式格式：
 * - 将 LaTeX 格式的 '\\[' 和 '\\]' 转换为 '$$$$'。
 * - 将 LaTeX 格式的 '\\(' 和 '\\)' 转换为 '$$'。
 * @param input 输入字符串
 * @returns string 转换后的字符串
 */
function convertMathFormula(input: string): string {
  if (!input) return input;

  let result = input;

  // 处理行间公式
  result = result.replace(/\\\[([\s\S]*?)\\\]/g, (_match, formula) => {
    // 特殊处理 cases 环境
    if (formula.includes('\\begin{cases}') && formula.includes('\\end{cases}')) {
      formula = formula.replace(/\\\\/g, '\\\\\\\\');
    }

    // 特殊处理矩阵环境
    const matrixEnvs = ['matrix', 'pmatrix', 'bmatrix', 'vmatrix', 'Vmatrix'];
    matrixEnvs.forEach(env => {
      const regex = new RegExp(`\\\\begin\\{${env}\\}([\\s\\S]*?)\\\\end\\{${env}\\}`, 'g');
      formula = formula.replace(regex, (matchStr: string) => {
        return matchStr.replace(/\\\\/g, '\\\\\\\\');
      });
    });

    // 特殊处理对齐环境
    const alignEnvs = ['align', 'align*', 'aligned', 'alignat', 'alignat*', 'equation', 'equation*'];
    alignEnvs.forEach(env => {
      const regex = new RegExp(`\\\\begin\\{${env}\\}([\\s\\S]*?)\\\\end\\{${env}\\}`, 'g');
      formula = formula.replace(regex, (matchStr: string) => {
        return matchStr.replace(/\\\\/g, '\\\\\\\\');
      });
    });

    // 确保公式在单独的行上，并且前后有足够的空行
    return `\n\n$$\n${formula}\n$$\n\n`;
  });

  // 处理行内公式
  result = result.replace(/\\\(([\s\S]*?)\\\)/g, '$$$1$$');

  return result;
}

interface ThoughtDetailDialogProps {
  thought: {
    id: string;
    desc?: string;
    url?: string;
    created_at?: string;
    content_type?: string;
    title?: string;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdate: (id: string, content: string) => Promise<void>;
}

export function ThoughtDetailDialog({
  thought,
  open,
  onOpenChange,
  onUpdate
}: ThoughtDetailDialogProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");
  const [expandedContent, setExpandedContent] = useState(false);

  // 根据 content_type 设置不同的背景颜色
  const getBgColor = () => {
    switch (thought.content_type?.toLowerCase()) {
      case 'reddit':
        return 'bg-orange-50 dark:bg-orange-950/30 border-orange-100 dark:border-orange-900';
      case 'weibo':
        return 'bg-red-50 dark:bg-red-950/30 border-red-100 dark:border-red-900';
      case 'idea':
        return 'bg-blue-50 dark:bg-blue-950/30 border-blue-100 dark:border-blue-900';
      case 'x':
        return 'bg-sky-50 dark:bg-sky-950/30 border-sky-100 dark:border-sky-900';
      default:
        return 'bg-green-50 dark:bg-green-950/30 border-green-100 dark:border-green-900';
    }
  };

  // 根据 content_type 设置不同的文本颜色
  const getTextColor = () => {
    switch (thought.content_type?.toLowerCase()) {
      case 'reddit':
        return 'text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300';
      case 'weibo':
        return 'text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300';
      case 'idea':
        return 'text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300';
      case 'x':
        return 'text-sky-600 dark:text-sky-400 hover:text-sky-700 dark:hover:text-sky-300';
      default:
        return 'text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300';
    }
  };

  const handleEdit = () => {
    setEditingId(thought.id);
    setEditContent(thought.desc || '');
  };

  const handleSave = async () => {
    await onUpdate(thought.id, editContent);
    setEditingId(null);
  };

  const handleCancel = () => {
    setEditingId(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={`${getBgColor()} max-w-[800px] max-h-[80vh] overflow-hidden flex flex-col ${
          (thought.desc || '').length < 150 ? "w-[500px]" : "w-[800px]"
        }`}
      >
        {/* <DialogHeader className="flex justify-between items-center"> */}
          {/* <DialogTitle className="text-lg font-medium">
            {thought.title || '思考详情'}

          </DialogTitle> */}

        {/* </DialogHeader> */}

        <div className="flex-1 overflow-y-auto p-4 overflow-x-hidden">
          <div className="prose dark:prose-invert max-w-none relative">


            {editingId === thought.id ? (
              <div className="space-y-4">
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className={`min-h-[200px] ${getBgColor()}`}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                  >
                    取消
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSave}
                  >
                    保存
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <div className={cn(
                  "prose dark:prose-invert max-w-none",
                  // 添加数学公式样式
                  (thought.desc || '').includes('$$') || (thought.desc || '').includes('\\[') ? "math-content" : ""
                )}>
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[
                      [rehypeKatex, {
                        throwOnError: false,
                        strict: false,
                        output: 'html',
                        trust: true,
                        displayMode: true,
                        fleqn: false,
                        leqno: false,
                        minRuleThickness: 0.05,
                        maxSize: 50,
                        maxExpand: 10000,
                        globalGroup: true,
                        macros: {
                          "\\R": "\\mathbb{R}",
                          "\\N": "\\mathbb{N}",
                          "\\Z": "\\mathbb{Z}",
                          "\\C": "\\mathbb{C}",
                          "\\Q": "\\mathbb{Q}",
                          "\\E": "\\mathbb{E}",
                          "\\F": "\\mathcal{F}",
                          "\\M": "\\mathcal{M}",
                          "\\L": "\\mathcal{L}",
                          "\\P": "\\mathcal{P}",
                          "\\S": "\\mathcal{S}",
                          "\\T": "\\mathcal{T}",
                          "\\cases": "\\begin{cases}#1\\end{cases}",
                          "\\matrix": "\\begin{matrix}#1\\end{matrix}",
                          "\\pmatrix": "\\begin{pmatrix}#1\\end{pmatrix}",
                          "\\bmatrix": "\\begin{bmatrix}#1\\end{bmatrix}",
                          "\\vmatrix": "\\begin{vmatrix}#1\\end{vmatrix}",
                          "\\Vmatrix": "\\begin{Vmatrix}#1\\end{Vmatrix}",
                          "\\align": "\\begin{align}#1\\end{align}",
                          "\\align*": "\\begin{align*}#1\\end{align*}",
                          "\\aligned": "\\begin{aligned}#1\\end{aligned}",
                          "\\equation": "\\begin{equation}#1\\end{equation}",
                          "\\equation*": "\\begin{equation*}#1\\end{equation*}"
                        }
                      }],
                      rehypeRaw
                    ]}
                    components={{
                      pre: ({ node, children, ...props }) => (
                        <div className="relative my-4">
                          <pre
                            className="overflow-x-auto p-4 rounded-lg bg-neutral-900 dark:bg-neutral-800"
                            {...props}
                          >
                            {children}
                          </pre>
                        </div>
                      ),
                      code: ({ node, className, children, ...props }: any) => {
                        const match = /language-(\w+)/.exec(className || '');
                        const inline = !match && (props.inline || false);

                        if (inline) {
                          return (
                            <code
                              className="px-1.5 py-0.5 rounded-md bg-neutral-100 dark:bg-neutral-800 font-mono text-sm"
                              {...props}
                            >
                              {children}
                            </code>
                          );
                        }

                        return (
                          <code
                            className={cn(
                              "text-sm text-neutral-50 bg-transparent",
                              match && `language-${match[1]}`
                            )}
                            {...props}
                          >
                            {children}
                          </code>
                        );
                      },
                      // 改进段落渲染，增加段落间距
                      p: ({ children }) => <p className="mb-4 last:mb-0 leading-relaxed">{children}</p>,
                      // 改进标题渲染
                      h1: ({ children }) => <h1 className="text-2xl font-bold mt-6 mb-4 pb-1 border-b border-gray-200 dark:border-gray-700">{children}</h1>,
                      h2: ({ children }) => <h2 className="text-xl font-bold mt-5 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">{children}</h2>,
                      h3: ({ children }) => <h3 className="text-lg font-bold mt-4 mb-3">{children}</h3>,
                      h4: ({ children }) => <h4 className="text-base font-semibold mt-3 mb-2">{children}</h4>,
                      h5: ({ children }) => <h5 className="text-sm font-semibold mt-3 mb-2">{children}</h5>,
                      h6: ({ children }) => <h6 className="text-sm font-medium mt-3 mb-2">{children}</h6>,
                      // 改进列表渲染
                      ul: ({ children }) => <ul className="list-disc pl-6 mb-4">{children}</ul>,
                      ol: ({ children }) => <ol className="list-decimal pl-6 mb-4">{children}</ol>,
                      li: ({ children }) => <li className="mb-1">{children}</li>,
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline underline-offset-4"
                        >
                          {children}
                        </a>
                      ),
                      // 改进表格渲染
                      table: ({ children }) => (
                        <div className="overflow-x-auto my-4">
                          <table className="border-collapse border border-gray-300 dark:border-gray-700 w-full">
                            {children}
                          </table>
                        </div>
                      ),
                      thead: ({ children }) => (
                        <thead className="bg-gray-100 dark:bg-gray-800">
                          {children}
                        </thead>
                      ),
                      tbody: ({ children }) => (
                        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                          {children}
                        </tbody>
                      ),
                      tr: ({ children }) => (
                        <tr className="hover:bg-gray-50 dark:hover:bg-gray-900/50">
                          {children}
                        </tr>
                      ),
                      th: ({ children }) => (
                        <th className="border border-gray-300 dark:border-gray-700 px-4 py-2 text-left font-medium">
                          {children}
                        </th>
                      ),
                      td: ({ children }) => (
                        <td className="border border-gray-300 dark:border-gray-700 px-4 py-2">
                          {children}
                        </td>
                      ),
                    }}
                  >
                    {expandedContent
                      ? convertMathFormula(thought.desc || '')
                      : (thought.desc || '').length > 300
                      ? convertMathFormula((thought.desc || '').slice(0, 300) + "...")
                      : convertMathFormula(thought.desc || '')}
                  </ReactMarkdown>
                </div>
                {(thought.desc || '').length > 300 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`mt-2 w-full flex items-center justify-center gap-1 ${getTextColor()}`}
                    onClick={() => setExpandedContent(!expandedContent)}
                  >
                    {expandedContent ? (
                      <>
                        收起 <ChevronUp className="h-4 w-4" />
                      </>
                    ) : (
                      <>
                        展开 <ChevronDown className="h-4 w-4" />
                      </>
                    )}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>

        <div className={`flex items-center justify-between p-4 border-t ${getBgColor()} backdrop-blur-sm`}>
          <p className="text-sm text-muted-foreground">
            来源：
            {thought.url && thought.url.match(/^https?:\/\/[^\s]+$/) ? (
              <a
                href={thought.url}
                target="_blank"
                rel="noopener noreferrer"
                className={`${getTextColor()} hover:underline transition-colors`}
              >
                {thought.url.length > 30
                  ? thought.url.slice(0, 30) + "..."
                  : thought.url}
              </a>
            ) : (
              thought.url || '未知来源'
            )}
          </p>
          <p className="text-xs text-muted-foreground">
            {thought.created_at
              ? thought.created_at.split("T")[0]
              : "暂无日期"}
          </p>
          <Button
                variant="ghost"
                size="icon"
                className={getTextColor()}
                onClick={handleEdit}
              >
                <Edit2 className="h-4 w-4" />
              </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}