import { useState, useEffect } from "react";
import {
  RefreshCw,
  Paperclip,
  Send,
  Search,
  ChevronDown,
  LibraryBig,
  Lightbulb,
  Loader2
} from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

import { useSearchEngineStore } from "@/store/searchEngineStore";
import { useThoughtStore } from "@/store/thoughtsStore";
import { useDigestStore } from "@/store/digestStore";
import { useDocumentStore } from "@/store/documentStore";
import { CustomModel, useModelConfigStore } from "@/store/modelConfigStore";
import { checkLLMHealth } from "@/utils/healthCheck";
import { useLLMStore } from '@/store/llmStore';  // Add this import
import { useAgentStore } from '@/store/agentStore';  // 添加 AgentStore
import { AgentPopover } from './chat/AgentPopover';  // 导入 AgentPopover 组件

// 在组件的props中添加defaultModelInfo
interface ChatInputProps {
  onSubmit: (message: string, modelInfo?: { baseUrl: string; model: string }) => void;
  placeholder?: string;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onSearchModeChange?: (isSearch: boolean) => void; // 新增
  onSearchEngineChange?: (engine: string) => void; // 添加搜索引擎切换回调
  hideSearchToggle?: boolean; // 添加新的属性
  isRequesting?: boolean; // 添加请求状态属性
  onStopRequest?: () => void; // 添加停止请求回调
  defaultModelInfo?: {
    model: string;
    baseUrl: string;
  } | null;
}

export function ChatInput({
  onSubmit,
  placeholder = "输入消息...",
  disabled = false,
  isRequesting = false,
  onStopRequest,
  value,
  onChange,
  onSearchModeChange,
  hideSearchToggle = false,
  defaultModelInfo = null
}: ChatInputProps) {
  const [input, setInput] = useState(value || "");
  // 移除本地的isRequesting状态，使用从props传入的状态
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const {selectedEngine, setSelectedEngine } = useSearchEngineStore();
  const { customModels } = useModelConfigStore();
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  // 获取当前 agent
  const { currentAgent, setCurrentAgent } = useAgentStore();

  const defaultModel: CustomModel = {
    id: "default",
    type: "language",
    name: "Deep Think",
    description: "系统默认模型",
    baseModel: import.meta.env.VITE_LLM_MODEL || "",
    baseService: import.meta.env.VITE_LLM_HOST || "",
    temperature: 0.6,
    maxTokens: 4096,
    agentId: "999",
    status: "ready",
    createdAt: new Date().toISOString(),
  };

  const allModels = [defaultModel, ...customModels];

  const [isDocsOpen, setIsDocsOpen] = useState(false);
  const { documents, isLoading, fetchDocuments } = useDocumentStore();

  // 对加入对话的文档进行过滤
  const [docFilter, setDocFilter] = useState("");
  const filteredDocuments = documents.filter(
    (doc) => {
      if (!doc) return false;

      const titleMatch = doc.title ?
        doc.title.toLowerCase().includes(docFilter.toLowerCase()) :
        false;

      const contentMatch = doc.markdown_url ?
        doc.markdown_url.toLowerCase().includes(docFilter.toLowerCase()) :
        false;

      return titleMatch || contentMatch;
    }
  );
  const [healthStatus, setHealthStatus] = useState<{
    status: "ok" | "error";
    latency: number;
  } | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  // 添加健康检查函数
  const checkHealth = async (baseUrl: string) => {
    if (isChecking) return;
    setIsChecking(true);
    try {
      const result = await checkLLMHealth({ serviceId: "chat", baseUrl });
      setHealthStatus(result);
    } catch (error) {
      console.error("健康检查失败:", error);
    } finally {
      setIsChecking(false);
    }
  };

  // 从 LLM Store 获取提供商和模型信息
  const { providers, selectedProvider, fetchProviders } = useLLMStore();

  // 在组件初始化时检查搜索引擎状态
  useEffect(() => {
    const { selectedEngine } = useSearchEngineStore.getState();
    // 如果已经选择了搜索引擎，则自动开启搜索模式
    if (selectedEngine && selectedEngine !== 'default') {
      setIsSearchMode(true);
      onSearchModeChange?.(true);
    }
  }, []);

  // 在组件初始化时，如果有默认的模型信息，则设置为当前选择的模型
  useEffect(() => {
    if (defaultModelInfo && defaultModelInfo.model && defaultModelInfo.baseUrl) {
      // console.log('ChatInput 接收到默认模型信息:', defaultModelInfo);

      // 查找是否有匹配的提供商和模型
      const provider = providers.find(p =>
        p.models.some(m => m.id === defaultModelInfo.model)
      );

      if (provider) {
        // console.log('找到匹配的提供商:', provider.name);
        // 设置选中的模型
        setSelectedModel(defaultModelInfo.model);

        // 更新全局状态
        useLLMStore.getState().setSelectedProvider(provider);
        useLLMStore.getState().setSelectedModel(defaultModelInfo.model);
      } else {
        console.log('未找到匹配的提供商，使用自定义模型');
        // 如果没有找到匹配的提供商，直接设置自定义模型
        useLLMStore.setState({
          selectedModel: {
            id: defaultModelInfo.model,
            providerName: "Custom Provider",
            baseUrl: defaultModelInfo.baseUrl
          }
        });
      }
    }
  }, [defaultModelInfo, providers]);

  // 在组件挂载时获取模型列表
  useEffect(() => {
    fetchProviders();
  }, []);

  // 获取所有已启用的提供商的模型列表
  const availableModels = providers
    .filter(provider => provider.enabled)
    .flatMap(provider => provider.models.map(model => ({
      ...model,
      providerName: provider.name,
      providerColor: provider.color,
      baseUrl: provider.baseUrl
    })));

  // 初始化时从 store 获取选中的模型
  useEffect(() => {
    const { selectedModel: storeSelectedModel } = useLLMStore.getState();
    if (storeSelectedModel) {
      setSelectedModel(storeSelectedModel.id);
    }
  }, [selectedProvider]);

  // 当用户选择模型时，更新 store 中的选中模型
  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);

    // 找到对应的提供商
    const provider = providers.find(p =>
      p.models.some(m => m.id === modelId)
    );

    if (provider) {
      useLLMStore.getState().setSelectedProvider(provider);
      // 添加这一行，确保同时更新全局状态中的选中模型
      useLLMStore.getState().setSelectedModel(modelId);
    }
  };
  // 添加定期检查
  useEffect(() => {
    // 获取当前选中模型的信息
    const modelInfo = getSelectedModelInfo();
    const baseUrl = modelInfo?.baseUrl || import.meta.env.VITE_LLM_HOST || "";

    if (baseUrl) {
      checkHealth(baseUrl);
      const interval = setInterval(() => {
        // 重新获取最新的模型信息进行检查
        const currentModelInfo = getSelectedModelInfo();
        const currentBaseUrl = currentModelInfo?.baseUrl || import.meta.env.VITE_LLM_HOST || "";
        checkHealth(currentBaseUrl);
      }, 30000); // 每30秒检查一次

      return () => clearInterval(interval);
    }
  }, [selectedModel]); // 添加 selectedModel 作为依赖项

  useEffect(() => {
    if (isDocsOpen) {
      console.log('文档选择器已打开，正在获取文档列表...');
      fetchDocuments().then(() => {
        console.log('文档获取成功，共获取到', documents.length, '个文档');
      }).catch(error => {
        console.error('文档获取失败:', error);
      });
    }
  }, [isDocsOpen]);

  const handleDocSelect = (markdown: string | null) => {
    if (!markdown) return;

    const prefix =
      "请阅读我提交的补充材料，放入到上下文中，继续我们的对话。\n\n";

    const sections = markdown.split(/(?=^# )/m);
    // 获取前三个段落（如果存在的话）
    const content = sections
      .slice(0, 4) // 取4个是因为split可能会在开头产生一个空字符串
      .filter((section) => section.trim()) // 过滤掉空段落
      .slice(0, 2) // 只3个段落（摘要）
      .join("\n\n")
      .trim();
    const newInput = prefix + content;

    setInput(newInput);
    onChange?.(newInput);
    setIsDocsOpen(false);
  };

  useEffect(() => {
    if (value !== undefined) {
      setInput(value);
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    onChange?.(e.target.value);
  };

  // 添加键盘事件处理函数，支持 Shift+Enter 和 Ctrl+Enter 提交
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 检测 Shift+Enter 或 Ctrl+Enter
    if (e.key === 'Enter' && (e.ctrlKey)) {
      e.preventDefault(); // 阻止默认行为（换行）

      // 如果输入框不为空且未禁用且当前没有请求正在进行，则提交
      if (input.trim() && !disabled && !isRequesting) {
        const modelInfo = getSelectedModelInfo();
        if (!modelInfo) {
          console.error('未找到选中的模型信息');
          return;
        }

        // 获取当前选择的 agent 信息
        // const { currentAgent, agents } = useAgentStore.getState();
        // console.log('键盘提交对话，当前 Agent:', currentAgent, agents[currentAgent]?.name);

        // 将 agent 信息添加到消息中
        let message = input.trim();

        // 如果需要在消息中添加 agent 标记，可以在这里添加
        // 例如：message = `[Agent: ${currentAgent}] ${message}`;

        onSubmit(message, modelInfo);
        setInput("");
      }
    }
  };

  // 获取当前选中模型的信息
  const getSelectedModelInfo = () => {
    // console.group('获取模型信息');
    // console.log('当前选中模型:', selectedModel);

    if (!selectedModel || selectedModel === "Deep Think") {
      const defaultInfo = {
        baseUrl: import.meta.env.VITE_LLM_HOST || "",
        model: import.meta.env.VITE_LLM_MODEL || "",
      };
      // console.log('使用默认模型配置:', defaultInfo);
      // console.groupEnd();
      return defaultInfo;
    }

    // 从启用的提供商中查找选中的模型
    const provider = providers.find(p =>
      p.enabled && p.models.some(m => m.id === selectedModel)
    );

    // console.log('找到的提供商:', provider);

    if (!provider) {
      console.warn('未找到对应的提供商配置');
      console.groupEnd();
      return null;
    }

    const modelInfo = {
      baseUrl: provider.baseUrl || "",
      model: selectedModel,
    };

    // console.log('返回模型配置:', modelInfo);
    console.groupEnd();
    return modelInfo;
  };

  // 修改提交函数
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !disabled && !isRequesting) {
      const modelInfo = getSelectedModelInfo();
      if (!modelInfo) {
        console.error('未找到选中的模型信息');
        return;
      }

      // 获取当前选择的 agent 信息
      // const { currentAgent, agents } = useAgentStore.getState();
      // console.log('提交对话，当前 Agent:', currentAgent, agents[currentAgent]?.name);

      // 记录当前搜索模式状态
      // console.log('提交对话，当前搜索模式:', isSearchMode ? '开启' : '关闭');

      // 将 agent 信息添加到消息中
      let message = input.trim();

      // 如果需要在消息中添加 agent 标记，可以在这里添加
      // 例如：message = `[Agent: ${currentAgent}] ${message}`;

      onSubmit(message, modelInfo);
      setInput("");
    }
  };

  const handleStopRequest = () => {
    if (onStopRequest) {
      onStopRequest();
    }
  };

  const [isThoughtsOpen, setIsThoughtsOpen] = useState(false);
  const [thoughtsFilter, setThoughtsFilter] = useState("");

  const { thoughts } = useThoughtStore();
  const { digests } = useDigestStore();

  // 添加 useEffect 来在 Popover 打开时获取数据
  useEffect(() => {
    if (isThoughtsOpen) {
      useThoughtStore.getState().fetchThoughts();
    }
  }, [isThoughtsOpen]);

  // 合并 thoughts 和 digests
  const allItems = [
    ...thoughts.map((t) => ({
      id: t.id,
      title: (t.desc || '').slice(0, 20) + ((t.desc || '').length > 20 ? "..." : ""),
      content: t.desc,
      type: "thought" as const,
    })),
    ...digests.map((d) => ({
      id: d.id,
      title: d.title || "摘要",
      content: d.content,
      type: "digest" as const,
    })),
  ];

  const filteredThoughts = allItems.filter((item) =>
    item.title.toLowerCase().includes(thoughtsFilter.toLowerCase())
  );

  const handleThoughtSelect = (content: string | null, type: "thought" | "digest") => {
    if (!content) return;

    const prefix =
      type === "thought"
        ? "让我们继续讨论这个话题，请添加以下内容到上下文中：\n\n"
        : "请参考以下内容，继续我们的对话：\n\n";

    const newInput = prefix + content;
    setInput(newInput);
    onChange?.(newInput);
    setIsThoughtsOpen(false);
  };

  return (
    <form onSubmit={handleSubmit} className="relative">
      <div className="absolute top-2 right-2 flex items-center gap-2 z-10">
        {/* 显示当前选择的 Agent */}
        <div className="flex items-center gap-1 bg-gray-100/70 dark:bg-gray-800/50 px-2 py-0.5 rounded-full text-xs border border-gray-200/50 dark:border-gray-700/50">
          <span className="text-gray-500 dark:text-gray-400">Agent:</span>
          <span className="font-medium">
            {(() => {
              const { currentAgent, agents } = useAgentStore.getState();
              return agents[currentAgent]?.name || currentAgent;
            })()}
          </span>
        </div>

        {healthStatus && (
          <div className="flex items-center gap-2">
            <span
              className={`inline-block w-2 h-2 rounded-full ${healthStatus.status === "ok" ? "bg-green-500" : "bg-red-500"
                }`}
            />
          </div>
        )}
      </div>
      <textarea
        value={input}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        className="p-4 pb-12 block w-full border border-gray-200 rounded-lg text-sm
                  focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50
                  disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700
                  dark:text-neutral-400 dark:placeholder-neutral-500
                  dark:focus:ring-neutral-600 resize-none min-h-[80px]"
        placeholder={placeholder || "输入消息，按 Shift+Enter 或 Ctrl+Enter 发送"}
        disabled={disabled}
      />

      <div className="absolute bottom-px inset-x-px p-2 rounded-b-lg bg-white dark:bg-neutral-900">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            {!hideSearchToggle && (
              <div className="relative inline-flex items-center">
                <input
                  type="checkbox"
                  id="search-mode-toggle"
                  checked={isSearchMode}
                  onChange={() => {
                    const newMode = !isSearchMode;
                    setIsSearchMode(newMode);
                    onSearchModeChange?.(newMode);
                    if (newMode) {
                      setIsDropdownOpen(true);
                    }
                  }}
                  className="sr-only peer"
                />
                <label
                  htmlFor="search-mode-toggle"
                  className="relative w-12 h-6 p-px bg-gray-100 border-transparent rounded-full cursor-pointer
                         transition-colors ease-in-out duration-200 peer-focus:ring-blue-600
                         peer-checked:bg-blue-600 dark:bg-neutral-800 dark:peer-checked:bg-blue-500
                         before:inline-block before:size-5 before:bg-white peer-checked:before:bg-blue-200
                         before:translate-x-0 peer-checked:before:translate-x-full before:rounded-full
                         before:shadow before:transform before:ring-0 before:transition
                         before:ease-in-out before:duration-200 dark:before:bg-neutral-400
                         dark:peer-checked:before:bg-blue-200"
                >
                  <span className="sr-only">切换搜索模式</span>
                </label>

                {isSearchMode && (
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="ml-2 py-1.5 px-2.5 inline-flex items-center gap-x-1.5 text-sm font-medium
                             rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm
                             hover:bg-gray-500 dark:bg-neutral-800 dark:border-neutral-700
                             dark:text-white dark:hover:bg-neutral-700"
                    >
                      <Search className="size-3.5" />
                      {/* 修改这里，显示引擎名称并首字母大写 */}
                      {(() => {
                        const engineName = useSearchEngineStore.getState().engines.find(engine => engine.id === selectedEngine)?.name || selectedEngine;
                        return engineName.charAt(0).toUpperCase() + engineName.slice(1);
                      })()}
                      <ChevronDown
                        className={`size-3.5 transition-transform ${isDropdownOpen ? "rotate-180" : ""
                          }`}
                      />
                    </button>

                    {isDropdownOpen && (
                      <div
                        className="absolute bottom-full left-0 mb-2 w-48 bg-white shadow-lg rounded-lg py-1
                                  dark:bg-neutral-800 dark:border dark:border-neutral-700"
                        style={
                          { "--placement": "top-left" } as React.CSSProperties
                        }
                      >
                        {/* 从 store 中获取搜索引擎列表，而不是使用硬编码的列表 */}
                        {useSearchEngineStore.getState().engines.map(
                          (engine) => (
                            <button
                              key={engine.id}
                              type="button"
                              onClick={() => {
                                setSelectedEngine(engine.id);
                                setIsDropdownOpen(false);
                              }}
                              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100
                                   dark:text-neutral-300 dark:hover:bg-neutral-700"
                            >
                              {engine.name.charAt(0).toUpperCase() + engine.name.slice(1)}
                            </button>
                          )
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
            {/* Agent Popover */}
            <AgentPopover
              onAgentSelect={(agentId) => {
                console.log('选择 Agent:', agentId);
                setCurrentAgent(agentId);
              }}
            />

            <Popover open={isDocsOpen} onOpenChange={setIsDocsOpen}>
              <PopoverTrigger asChild>
                <button
                  type="button"
                  className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                            text-gray-500 hover:bg-gray-100 focus:z-10 focus:outline-none
                            focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                            dark:focus:bg-neutral-700"
                >
                  <LibraryBig className="size-4 text-purple-500" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0">
                {/* Header */}
                <div className="border-b px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-t-lg">
                  <h4 className="text-sm font-medium text-center">添加论文到对话</h4>
                </div>

                {/* Content */}
                <div className="p-4 space-y-4">
                  <div className="flex flex-col gap-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        value={docFilter}
                        onChange={(e) => setDocFilter(e.target.value)}
                        placeholder="搜索文档..."
                        className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                     focus:outline-none focus:ring-1 focus:ring-blue-500
                     dark:bg-neutral-800 dark:border-neutral-700
                     dark:text-neutral-200 dark:placeholder-neutral-400"
                      />
                    </div>
                  </div>
                  {isLoading ? (
                    <div className="flex justify-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin" />
                    </div>
                  ) : (
                    <div className="max-h-[250px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                      {filteredDocuments.map((doc) => (
                        <button
                          key={doc.id}
                          onClick={() => handleDocSelect(doc.description)}
                          className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                                   dark:hover:bg-neutral-800 flex items-center justify-between"
                        >
                          <span className="line-clamp-1">{doc.title}</span>
                          {/* <span className="text-xs text-gray-500">{doc.content_type}</span> */}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Footer */}
                <div className="border-t px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-b-lg">
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    在知识库中预处理相关论文
                  </p>
                </div>
              </PopoverContent>
            </Popover>

            {/* 新增的 Thoughts Popover */}
            <Popover open={isThoughtsOpen} onOpenChange={setIsThoughtsOpen}>
              <PopoverTrigger asChild>
                <button
                  type="button"
                  className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                        text-gray-500 hover:bg-gray-100 focus:z-10 focus:outline-none
                        focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                        dark:focus:bg-neutral-700"
                >
                  <Lightbulb className="size-4 text-yellow-500" />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-100">
                <div className="space-y-4">
                  <div className="flex flex-col gap-2">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium">添加一点想法...</div>
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          useThoughtStore.getState().fetchThoughts(); // 修正为正确的刷新方法
                        }}
                        className="p-1 hover:bg-gray-100 rounded-full dark:hover:bg-neutral-700
                                     transition-colors duration-200"
                      >
                        <RefreshCw className="h-4 w-4 text-gray-500 dark:text-neutral-400" />
                      </button>
                    </div>
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        value={thoughtsFilter}
                        onChange={(e) => setThoughtsFilter(e.target.value)}
                        placeholder="搜索想法..."
                        className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                             focus:outline-none focus:ring-1 focus:ring-blue-500
                             dark:bg-neutral-800 dark:border-neutral-700
                             dark:text-neutral-200 dark:placeholder-neutral-400"
                      />
                    </div>
                  </div>
                  <div className="max-h-[300px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                    {filteredThoughts.map((thought) => (
                      <button
                        key={thought.id}
                        onClick={() =>
                          handleThoughtSelect(thought.content, thought.type)
                        }
                        className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                              dark:hover:bg-neutral-800 flex items-center justify-between gap-2"
                      >
                        <span className="line-clamp-1">{thought.title}</span>
                        <span className="text-xs text-gray-500">
                          {thought.type === "thought" ? "想法" : "摘要"}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex items-center gap-x-1">
            <div className="relative inline-flex">
              <DropdownMenu
                open={isModelDropdownOpen}
                onOpenChange={setIsModelDropdownOpen}
              >
                <DropdownMenuTrigger asChild>
                  <button
                    type="button"
                    className="py-1.5 px-2.5 inline-flex items-center gap-x-1.5 text-sm font-medium
               rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm
               hover:bg-gray-500 focus:outline-none focus:bg-gray-50
               disabled:opacity-50 disabled:pointer-events-none
               dark:bg-neutral-800 dark:border-neutral-700 dark:text-white
               dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="stroke-[2]"
                    >
                      <path
                        d="M19 9C19 12.866 15.866 17 12 17C8.13398 17 4.99997 12.866 4.99997 9C4.99997 5.13401 8.13398 3 12 3C15.866 3 19 5.13401 19 9Z"
                        className={`origin-center transition-all duration-100 ${selectedModel
                            ? `${allModels.find(m => m.name === selectedModel)?.status === "ready"
                              ? "fill-green-100 dark:fill-green-400/40"
                              : allModels.find(m => m.name === selectedModel)?.status === "training"
                                ? "fill-yellow-100 dark:fill-yellow-400/40"
                                : "fill-red-100 dark:fill-red-400/40"
                            } scale-100 opacity-100`
                            : "scale-0 opacity-0"
                          }`}
                      />
                      <path
                        d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z"
                        fill="currentColor"
                      />
                      <path d="M9 16.0001H15" stroke="currentColor" />
                      <path
                        d="M12 16V12"
                        stroke="currentColor"
                        strokeLinecap="square"
                      />
                      <g
                        className={`transition-all duration-100 ease-in-out ${selectedModel ? "opacity-100" : "opacity-0"
                          }`}
                      >
                        <path
                          d="M20 7L19 8"
                          stroke="currentColor"
                          strokeLinecap="round"
                          className="translate-x-[3px] -translate-y-[3px]"
                        />
                        <path
                          d="M20 9L19 8"
                          stroke="currentColor"
                          strokeLinecap="round"
                          className="translate-x-[3px] translate-y-[3px]"
                        />
                        <path
                          d="M4 7L5 8"
                          stroke="currentColor"
                          strokeLinecap="round"
                          className="-translate-x-[3px] -translate-y-[3px]"
                        />
                        <path
                          d="M4 9L5 8"
                          stroke="currentColor"
                          strokeLinecap="round"
                          className="-translate-x-[3px] translate-y-[3px]"
                        />
                      </g>
                    </svg>
                    {selectedModel ?
                      (() => {
                        const model = availableModels.find(m => m.id === selectedModel);
                        return model ? `${model.id}（${model.providerName}）` : "Deep Think";
                      })()
                      : "Deep Think"}
                    <ChevronDown
                      className={`size-3.5 transition-transform ${isModelDropdownOpen ? "rotate-180" : ""
                        }`}
                    />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="min-w-48">
                  {/* 系统默认模型 */}
                  <DropdownMenuItem
                    key="default"
                    onClick={() => setSelectedModel("Deep Think")}
                  >
                    <span className="truncate text-green-600 dark:text-green-400">
                      Deep Think (默认)
                    </span>
                  </DropdownMenuItem>

                  {/* 分隔线 */}
                  {availableModels.length > 0 && (
                    <div className="h-px my-1 bg-gray-200 dark:bg-neutral-700" />
                  )}

                  {/* 按提供商分组显示模型列表 */}
                  {(() => {
                    // 按提供商名称对模型进行分组
                    const modelsByProvider: Record<string, typeof availableModels> = {};

                    // 将模型按提供商分组
                    availableModels.forEach(model => {
                      if (!modelsByProvider[model.providerName]) {
                        modelsByProvider[model.providerName] = [];
                      }
                      modelsByProvider[model.providerName].push(model);
                    });

                    // 渲染分组后的模型列表
                    return Object.entries(modelsByProvider).map(([providerName, models], index, array) => (
                      <div key={providerName}>
                        {/* 提供商名称作为分组标题 */}
                        <div className="px-2 py-1.5 text-xs font-semibold text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-neutral-800">
                          {providerName}
                        </div>

                        {/* 该提供商下的模型列表 */}
                        {models.map(model => (
                          <DropdownMenuItem
                            key={`${model.providerName}-${model.id}`}
                            onClick={() => {
                              handleModelSelect(model.id);
                            }}
                          >
                            <div className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${model.providerColor || 'bg-blue-500'}`} />
                              <span className="truncate text-sm">
                                {model.id}
                              </span>
                            </div>
                          </DropdownMenuItem>
                        ))}

                        {/* 在每个提供商组之后添加分隔线，最后一个除外 */}
                        {index < array.length - 1 && (
                          <div className="h-px my-1 bg-gray-200 dark:bg-neutral-700" />
                        )}
                      </div>
                    ));
                  })()}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <button
              type="button" // 改为 button 类型，避免自动提交表单
              onClick={(e) => {
                e.preventDefault(); // 阻止表单提交
                if (isRequesting) {
                  handleStopRequest();
                } else {
                  handleSubmit(e);
                }
              }}
              disabled={(!input.trim() || disabled) && !isRequesting} // 修改禁用条件，允许在请求中点击停止
              className={`inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                            text-white ${isRequesting ? 'bg-red-600 hover:bg-red-500' : 'bg-blue-600 hover:bg-blue-500'}
                            focus:z-10 focus:outline-none focus:bg-blue-500 disabled:opacity-50
                            disabled:hover:bg-blue-600 relative overflow-hidden`}
            >
              {isRequesting ? (
                <>
                  <Loader2 className="size-4 animate-spin relative z-10" />
                  <span className="absolute inset-0 bg-red-700 animate-pulse opacity-30"></span>
                </>
              ) : input.trim() && !disabled ? (
                <>
                  <Send className="size-3.5 relative z-10" />
                  <span className="absolute inset-0 bg-blue-700 animate-pulse opacity-0 group-hover:opacity-30 transition-opacity"></span>
                </>
              ) : (
                <Send className="size-3.5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}

