import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  ImageIcon,
  Globe,
  File,
  Loader2,
  X,
  Image,
} from "lucide-react"
import { ImageUploader } from "./ImageUploader"
import { useState, useRef, useEffect } from "react"
import { toast } from "sonner"
import API from "@/config/api"
import { useLLMStore } from '@/store/llmStore'
import FireCrawlApp from '@mendable/firecrawl-js'
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"


interface Document {
  title: string;
  description: string;
  type: string;
  content_type: string;
  file_path: string;
  markdown_url: string;
  file_size: number;
  id: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

// 添加书签接口
interface BookmarkItem {
  id: string;
  title: string;
  url: string;
  desc?: string;
  user_id: string;
  created_at: string;
}

interface PostInputProps {
  onPostSuccess?: () => void; // 添加刷新父组件的回调函数
  initialContent?: string; // 添加初始内容属性
}

export function PostInput({ onPostSuccess, initialContent = "" }: PostInputProps) {
  const [content, setContent] = useState(initialContent)
  const [image, setImage] = useState<File | null>(null)
  const [imageName, setImageName] = useState<string>("")
  const [showImageUploader, setShowImageUploader] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [showDocuments, setShowDocuments] = useState(false)
  const [documents, setDocuments] = useState<Document[]>([])
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([])
  // const [mentionText, setMentionText] = useState("")
  const [cursorPosition, setCursorPosition] = useState(0)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 添加书签相关状态
  const [bookmarks, setBookmarks] = useState<BookmarkItem[]>([])
  const [filteredBookmarks, setFilteredBookmarks] = useState<BookmarkItem[]>([])
  const [showBookmarks, setShowBookmarks] = useState(false)
  // const [bookmarkText, setBookmarkText] = useState("")

  // 添加网页链接相关状态
  const [isLoadingWebContent, setIsLoadingWebContent] = useState(false)
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null)
  const [selectedScraperService, setSelectedScraperService] = useState<string>("firecrawl") // 默认使用 FireCrawl

  // 翻译功能相关
  const [enableTranslation, setEnableTranslation] = useState(false)
  const [translationModelId, setTranslationModelId] = useState<string | null>(null)
  const [isTranslating, setIsTranslating] = useState(false)

  // 获取用户选择的模型
  const { selectedModel, providers } = useLLMStore();

  const maxLength = 2000-200;

  // 获取文档列表
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const token = localStorage.getItem('token')
        // 使用相对路径，确保通过 Vite 的代理配置处理
        const response = await fetch('/api/documents', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        if (response.ok) {
          const data = await response.json()
          setDocuments(data)
        }
      } catch (error) {
        console.error('Error fetching documents:', error)
      }
    }

    fetchDocuments()
  }, [])

  // 获取书签列表
  useEffect(() => {
    const fetchBookmarks = async () => {
      try {
        const token = localStorage.getItem('token')
        // 使用相对路径，确保通过 Vite 的代理配置处理
        const response = await fetch('/api/bookmark', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        if (response.ok) {
          const data = await response.json()
          setBookmarks(data)
        }
      } catch (error) {
        console.error('Error fetching bookmarks:', error)
      }
    }

    fetchBookmarks()
  }, [])

  // 选择书签
  const selectBookmark = (bookmark: BookmarkItem) => {
    const textBeforeCursor = content.substring(0, cursorPosition)
    const slashIndex = textBeforeCursor.lastIndexOf('%')

    if (slashIndex !== -1) {
      const bookmarkReference = `[${bookmark.desc}](${bookmark.url}) `
      const newContent =
        content.substring(0, slashIndex) +
        bookmarkReference +
        content.substring(cursorPosition)

      setContent(newContent)
      setShowBookmarks(false)

      setTimeout(() => {
        if (inputRef.current) {
          const newPosition = slashIndex + bookmarkReference.length
          inputRef.current.focus()
          inputRef.current.setSelectionRange(newPosition, newPosition)
        }
      }, 0)
    }
  }

  // 监听输入变化，检测 @ 符号和 %符号
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value
    setContent(newContent)

    const cursorPos = e.target.selectionStart || 0
    setCursorPosition(cursorPos)

    const textBeforeCursor = newContent.substring(0, cursorPos)
    const atIndex = textBeforeCursor.lastIndexOf('@')
    const slashIndex = textBeforeCursor.lastIndexOf('%')

    if (atIndex !== -1 && (atIndex === 0 || textBeforeCursor[atIndex - 1] === ' ') &&
        (slashIndex === -1 || atIndex > slashIndex)) {
      // 提取 @ 后面的文本用于过滤
      const filterText = textBeforeCursor.substring(atIndex + 1)
      // setMentionText(filterText)

      // 过滤文档列表
      const filtered = documents.filter(doc =>
        doc.title.toLowerCase().includes(filterText.toLowerCase())
      )
      setFilteredDocuments(filtered)
      setShowDocuments(true)
      setShowBookmarks(false) // 确保书签列表关闭
    }
    // 检查是否输入了 %
    else if (slashIndex !== -1 && (slashIndex === 0 || textBeforeCursor[slashIndex - 1] === ' ')) {
      // 提取 % 后面的文本用于过滤
      const filterText = textBeforeCursor.substring(slashIndex + 1) // 只需要+1，因为只有一个字符
      // setBookmarkText(filterText)

      // 过滤书签列表，添加空值检查
      const filtered = bookmarks.filter(bookmark =>
        bookmark && bookmark.title && bookmark.title.toLowerCase().includes(filterText.toLowerCase())
      )
      setFilteredBookmarks(filtered)
      setShowBookmarks(true)
      setShowDocuments(false) // 确保文档列表关闭
    } else {
      setShowDocuments(false)
      setShowBookmarks(false)
    }
  }

  // 选择文档
  const selectDocument = (doc: Document) => {
    // 找到当前 @ 的位置
    const textBeforeCursor = content.substring(0, cursorPosition)
    const atIndex = textBeforeCursor.lastIndexOf('@')

    if (atIndex !== -1) {
      // 替换 @ 后面的文本为选中的文档的Markdown格式
      const docReference = `\n## ${doc.title}\n${doc.description}\n![](${doc.file_path})\n`
      const newContent =
        content.substring(0, atIndex) +
        docReference +
        content.substring(cursorPosition)

      setContent(newContent)
      setShowDocuments(false)

      // 设置光标位置到文档引用后
      setTimeout(() => {
        if (inputRef.current) {
          const newPosition = atIndex + docReference.length
          inputRef.current.focus()
          inputRef.current.setSelectionRange(newPosition, newPosition)
        }
      }, 0)
    }
  }

  // 处理图片选择
  const handleImageSelect = (file: File | null) => {
    if (file) {
      setImage(file);
      setImageName(file.name);

      // 创建预览
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      // 清除图片
      setImage(null);
      setImageName("");
      setImagePreview(null);
    }

    // 关闭上传器
    setShowImageUploader(false);
  }

  // 翻译内容
  const translateContent = async () => {
    if (!content.trim() || !translationModelId || !enableTranslation) {
      return content;
    }

    setIsTranslating(true);
    const toastId = toast.loading("正在翻译内容...");

    try {
      // 获取选中模型的提供商
      const provider = providers.find(p =>
        p.models.some(m => m.id === translationModelId)
      );

      if (!provider) {
        throw new Error('无法找到选中模型的提供商信息');
      }

      // 使用模型提供商的 credential（API密钥）
      const apiKey = provider.apiKey;

      if (!apiKey) {
        throw new Error('模型未配置API密钥');
      }

      // 获取模型的基础URL
      const modelBaseUrl = provider.baseUrl || API.BASE_URL;

      // 构建请求体
      const requestBody = {
        model: translationModelId,
        messages: [
          {
            role: "user",
            content: `请将以下内容翻译成英文，保持原文的格式和结构：\n\n${content}`
          }
        ],
        temperature: 0.3,
        max_tokens: 2000, // 限制输出长度
      };

      // 发送请求到API
      const response = await fetch(`${modelBaseUrl}chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`翻译失败: ${response.status}`);
      }

      const data = await response.json();
      const translatedContent = data.choices[0].message.content;

      toast.dismiss(toastId);
      toast.success("内容已翻译成英文");

      return translatedContent;
    } catch (error) {
      console.error('翻译失败:', error);
      toast.dismiss(toastId);
      toast.error("翻译失败，将使用原始内容");
      return content;
    } finally {
      setIsTranslating(false);
    }
  };

  const handleSubmit = async () => {
    if (!content.trim()) {
      toast.error("文字不能为空")
      return
    }

    // 检查内容长度
    let truncatedContent = content;

    // 前端UI限制
    if (content.length > maxLength) {
      truncatedContent = content.slice(0, maxLength);
      toast.warning(`内容超出${maxLength}字符限制，已自动截断`);
    }

    // 数据库字段长度限制 (VARCHAR(2000))
    const DB_MAX_LENGTH = 2000;
    if (truncatedContent.length > DB_MAX_LENGTH) {
      truncatedContent = truncatedContent.slice(0, DB_MAX_LENGTH);
      toast.warning(`内容超出数据库限制(${DB_MAX_LENGTH}字符)，已自动截断`);
      console.warn(`内容长度(${content.length})超过数据库限制(${DB_MAX_LENGTH})，已截断`);
    }

    // 如果启用了翻译，先翻译内容
    if (enableTranslation && translationModelId) {
      truncatedContent = await translateContent();
    }

    const token = localStorage.getItem('token') // 从 localStorage 获取 token

    const formData = new FormData()
    formData.append("content", truncatedContent) // 使用截断后的内容
    if (image) {
      formData.append("images", image)
      // 清除图片预览
      setImagePreview(null)
    }

    try {
      // 使用相对路径，确保通过 Vite 的代理配置处理
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}` // 使用获取的 token
        },
        body: formData
      })
      if (response.ok) {
        toast.success("发送成功") // 使用 toast 替代 alert
        setContent("")
        setImage(null)
        setImageName("") // 清除文件名
        setImagePreview(null) // 清除图片预览

        // 调用父组件的刷新函数
        if (onPostSuccess) {
          onPostSuccess()
        }
      } else {
        // 获取更详细的错误信息
        try {
          const errorData = await response.json();
          console.error('发送失败，服务器返回:', errorData);

          // 检查是否是数据库字段长度错误
          if (errorData.detail && errorData.detail.includes('StringDataRightTruncation')) {
            toast.error("发送失败: 内容长度超出数据库限制");
          } else {
            toast.error(`发送失败: ${errorData.message || errorData.detail || '服务器错误'}`);
          }
        } catch (parseError) {
          console.error('发送失败，状态码:', response.status, response.statusText);
          toast.error(`发送失败 (${response.status}): ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error('Error sending post:', error);

      // 提供更详细的错误信息
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        toast.error("发送失败：无法连接到服务器，请检查网络连接或服务器状态");
        console.error('可能的原因：服务器未运行、网络问题或CORS错误');
      } else if (error instanceof Error) {
        toast.error(`发送失败：${error.message}`);
      } else {
        toast.error("发送失败，请检查网络连接");
      }
    }
  }

  const handleImageClick = () => {
    setShowImageUploader(prev => !prev);
  }

  // 处理网页链接点击
  const handleWebLinkClick = () => {
    // 初始化选中的模型ID为当前选中的模型（如果有）
    if (selectedModel) {
      setSelectedModelId(selectedModel.id);
    }
  }

  // 处理网页内容提交
  const handleWebContentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const input = document.getElementById('web-url-input') as HTMLInputElement;
    if (input && input.value) {
      fetchWebContent(input.value, selectedModelId, selectedScraperService);

      // 关闭 Popover 对话框
      const popoverTrigger = document.querySelector('[data-slot="popover-trigger"]') as HTMLButtonElement;
      if (popoverTrigger) {
        popoverTrigger.click();
      }
    } else {
      toast.error("请输入有效的网址");
    }
  }

  // 验证URL是否合法
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  // 清理微博内容，移除无用元素
  const cleanWeiboContent = (content: string): string => {
    if (!content) return '';

    // 保留原始内容，只移除一些明显的无用元素
    let cleanedContent = content;

    // 移除图片链接，但保留其他链接
    cleanedContent = cleanedContent.replace(/!\[\]\([^\)]+\)/g, '');

    // 移除多余的空行
    cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');

    return cleanedContent;
  }

  // 处理网页抓取响应结果
  const processFireCrawlResult = async (
    scrapeResult: any,
    url: string,
    toastId: string | number,
    modelId: string | null = null
  ) => {

    // 检查响应格式
    if (!scrapeResult || !scrapeResult.success) {
      throw new Error(`抓取失败: ${JSON.stringify(scrapeResult)}`);
    }

    // 获取标题和内容
    // 根据响应结构，markdown 可能在顶层或 data 对象内
    let title = url;
    let markdown = '';

    // 尝试从不同位置获取 markdown 内容
    if (scrapeResult.markdown) {
      markdown = scrapeResult.markdown || '';
      // 尝试获取标题
      title = scrapeResult.metadata?.title || url;
    } else if (scrapeResult.data?.markdown) {
      markdown = scrapeResult.data.markdown || '';
      // 尝试获取标题
      title = scrapeResult.data.metadata?.title || url;
    } else {
      throw new Error('无法找到内容');
    }

    // 处理微博内容，过滤无用元素
    if (url.includes('weibo.com')) {
      // 移除微博页面常见的无用内容
      markdown = cleanWeiboContent(markdown);
    }

    // 如果用户选择了模型，使用模型进行总结
    if (modelId) {
      toast.dismiss(toastId);
      const summaryToastId = toast.loading("正在使用AI总结内容...");

      try {
        // 获取选中模型的提供商
        const provider = providers.find(p =>
          p.models.some(m => m.id === modelId)
        );

        if (!provider) {
          throw new Error('无法找到选中模型的提供商信息');
        }

        // 使用模型提供商的 credential（API密钥）
        const apiKey = provider.apiKey;

        if (!apiKey) {
          throw new Error('模型未配置API密钥');
        }

        // 获取模型的基础URL
        const modelBaseUrl = provider.baseUrl || API.BASE_URL;

        // 构建请求体
        const requestBody = {
          model: modelId,
          messages: [
            {
              role: "user",
              content: url.includes('weibo.com')
                ? `作为专业摘要师，请对以下微博内容创建一个简洁而全面的摘要：
                  * 请以"##"标签开头创建一个引人注目的标题
                  * 撰写详细、透彻、深入且结构清晰的摘要，同时保持语言简洁
                  * 包含主要观点和核心信息，去除冗余语言，专注于关键方面
                  * 严格基于提供的文本，不要添加外部信息
                  * 摘要格式应为段落形式，便于理解
                  * 总字数控制在800字以内
                  以下是需要摘要的内容：\n\n${markdown.length > 10000 ? markdown.substring(0, 10000) + "...(内容过长已截断)" : markdown}`
                : `作为专业摘要师，请对以下内容创建一个简洁而全面的摘要：
                  * 请以"##"标签开头创建一个引人注目的标题
                  * 撰写详细、透彻、深入且结构清晰的摘要，同时保持语言简洁
                  * 包含主要观点和核心信息，去除冗余语言，专注于关键方面
                  * 严格基于提供的文本，不要添加外部信息
                  * 摘要格式应为段落形式，便于理解
                  * 总字数控制在800字以内

                  以下是需要摘要的内容：\n\n${markdown.length > 10000 ? markdown.substring(0, 10000) + "...(内容过长已截断)" : markdown}`
            }
          ],
          temperature: 0.6,
          max_tokens: 2000, // 限制输出长度
        };

        // 发送请求到API

        const summaryResponse = await fetch(`${modelBaseUrl}chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify(requestBody)
        });

        if (!summaryResponse.ok) {
          throw new Error(`总结失败: ${summaryResponse.status}`);
        }

        const summaryData = await summaryResponse.json();

        const summary = summaryData.choices[0].message.content;

        // 更新微博内容
        // 检查 summary 是否已经包含标题和来源
        let formattedContent = summary;

        // 如果 summary 不包含标题，添加标题
        if (!summary.includes('##')) {
          formattedContent = `## ${title}\n\n${formattedContent}`;
        }

        // 如果 summary 不包含来源，添加来源
        if (!summary.includes('> ')) {
          // 格式化URL，提取域名作为来源
          const sourceUrl = new URL(url);
          const sourceDomain = sourceUrl.hostname.replace('www.', '');
          formattedContent = `${formattedContent}\n\n> 来源: [${sourceDomain}](${url})`;
        }

        setContent(formattedContent);
        toast.dismiss(summaryToastId);
        toast.success("网页内容已总结并添加到微博");
      } catch (error) {
        console.error('总结失败:', error);
        toast.dismiss(summaryToastId);
        toast.error("AI总结失败，已添加原始内容");

        // 如果总结失败，使用原始内容
        // 格式化URL，提取域名作为来源
        const sourceUrl = new URL(url);
        const sourceDomain = sourceUrl.hostname.replace('www.', '');
        const formattedContent = `## ${title}\n\n${markdown}\n\n> 来源: [${sourceDomain}](${url})`;
        setContent(formattedContent);
      }
    } else {
      // 如果没有选择模型，直接使用抓取的内容
      // 格式化URL，提取域名作为来源
      const sourceUrl = new URL(url);
      const sourceDomain = sourceUrl.hostname.replace('www.', '');
      const formattedContent = `## ${title}\n\n${markdown}\n\n> 来源: [${sourceDomain}](${url})`;
      setContent(formattedContent);
      toast.dismiss(toastId);
      toast.success("网页内容已添加到微博");
    }
  };

  // 抓取网页内容
  const fetchWebContent = async (url: string, modelId: string | null = null, scraperService: string = "firecrawl") => {
    // 验证URL
    if (!isValidUrl(url)) {
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
        if (!isValidUrl(url)) {
          toast.error("请输入有效的网址");
          return;
        }
      } else {
        toast.error("请输入有效的网址");
        return;
      }
    }

    // 显示加载中的提示
    const toastId = toast.loading("正在抓取网页内容...");
    setIsLoadingWebContent(true);

    try {
      let scrapeResult;

      if (scraperService === 'jina') {
        // 使用 Jina Reader 抓取网页内容
        try {
          const jinaUrl = `https://r.jina.ai/${url}`;

          const jinaResponse = await fetch(jinaUrl, {
            headers: {
              'Authorization': `Bearer jina_7a8f45255f9341cebefc877866111655YMgQtBlVbq0h6hQOU-XJGDPkbIeg`
            }
          });

          if (!jinaResponse.ok) {
            throw new Error(`Jina Reader 请求失败: ${jinaResponse.status}`);
          }

          // Jina 返回的是纯文本 Markdown
          const markdown = await jinaResponse.text();

          // 构造一个类似 FireCrawl 的结果对象
          scrapeResult = {
            success: true,
            markdown: markdown,
            metadata: {
              title: url
            }
          };
        } catch (jinaError: any) {
          console.error('Jina Reader 调用失败:', jinaError);
          throw new Error(`Jina Reader 抓取失败: ${jinaError.message || '未知错误'}`);
        }
      } else {
        // 使用 FireCrawl 抓取网页内容
        try {
          const firecrawlApp = new FireCrawlApp({apiKey: "fc-31400474c7174a49aa31eb508be34a98"});

          scrapeResult = await firecrawlApp.scrapeUrl(url, {
            formats: ["markdown"],
          });
        } catch (libraryError) {
          // 如果 FireCrawl 库失败，尝试使用直接 API 调用
          try {
            const apiResponse = await fetch('https://api.firecrawl.dev/v1/scrape', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer fc-31400474c7174a49aa31eb508be34a98`
              },
              body: JSON.stringify({
                url: url,
                formats: ["markdown"]
              })
            });

            if (!apiResponse.ok) {
              throw new Error(`FireCrawl API 请求失败: ${apiResponse.status}`);
            }

            scrapeResult = await apiResponse.json();
          } catch (apiError: any) {
            console.error('直接 API 调用也失败:', apiError);
            throw new Error(`无法抓取网页内容: ${apiError.message || '未知错误'}`);
          }
        }
      }

      // 处理抓取结果
      if (scrapeResult) {
        await processFireCrawlResult(scrapeResult, url, toastId, modelId);
      } else {
        throw new Error('未能获取抓取结果');
      }
    } catch (error) {
      console.error('抓取网页内容失败:', error);
      toast.dismiss(toastId);

      // 显示更详细的错误信息
      if (error instanceof Error) {
        toast.error(`抓取失败: ${error.message}`);
      } else {
        toast.error("抓取网页内容失败，请检查网址是否正确");
      }
    } finally {
      setIsLoadingWebContent(false);
    }
  }

  return (
    <Card className="overflow-visible">
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="relative">
            <Textarea
              ref={inputRef}
              placeholder="有什么新鲜事想分享给大家? 使用@引用文档，使用%引用书签"
              className="bg-gray-50 border-gray-200 text-sm py-3 min-h-[50px] h-[100px] resize-none overflow-y-auto"
              value={content}
              onChange={handleContentChange}
              maxLength={2000} // 添加最大长度限制
            />
            <div className="text-xs text-gray-500 text-right mt-1">
              <span
                className={`
                  ${content.length > maxLength ? 'text-red-500 font-medium' :
                    content.length > maxLength * 0.8 ? 'text-yellow-500' :
                    'text-green-500'}
                `}
              >
                {content.length}/{maxLength}
              </span>
            </div>

            {showDocuments && (
              <div className="absolute z-10 mt-1 w-full max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-lg">
                {filteredDocuments.length > 0 ? (
                  filteredDocuments.map(doc => (
                    <div
                      key={doc.id}
                      className="p-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => selectDocument(doc)}
                    >
                      <div className="font-medium">{doc.title}</div>
                      <div className="text-xs text-gray-500 truncate">{doc.description}</div>
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-500">没有找到匹配的文档</div>
                )}
              </div>
            )}

            {/* 添加书签列表 */}
            {showBookmarks && (
              <div className="absolute z-10 mt-1 w-full max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-lg">
                {filteredBookmarks.length > 0 ? (
                  filteredBookmarks.map(bookmark => (
                    bookmark && bookmark.id ? (
                      <div
                        key={bookmark.id}
                        className="p-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => selectBookmark(bookmark)}
                      >
                        <div className="font-medium">{bookmark.title || '无标题'}</div>
                        <div className="text-xs text-gray-500 truncate">{bookmark.url || '无链接'}</div>
                      </div>
                    ) : null
                  ))
                ) : (
                  <div className="p-2 text-gray-500">没有找到匹配的书签</div>
                )}
              </div>
            )}
          </div>

          <div className="flex justify-between items-center">
            <div className="flex gap-6">
              <Popover open={showImageUploader} onOpenChange={setShowImageUploader}>
                <PopoverTrigger asChild>
                  <div className="flex flex-col items-start gap-1">
                    <Button variant="ghost" size="sm" className="text-gray-500" onClick={handleImageClick}>
                      <ImageIcon className="h-5 w-5 mr-1" />
                      图片
                    </Button>
                    {imageName && (
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 ml-2">{imageName}</span>
                        {imagePreview && (
                          <img
                            src={imagePreview}
                            alt="Preview"
                            className="ml-2 h-6 w-6 object-cover rounded"
                          />
                        )}
                      </div>
                    )}
                  </div>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-4" align="start">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">上传图片</h3>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 rounded-full"
                        onClick={() => setShowImageUploader(false)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <ImageUploader
                      onImageSelect={handleImageSelect}
                      initialImage={image}
                      initialPreview={imagePreview}
                    />
                  </div>
                </PopoverContent>
              </Popover>
              <Button variant="ghost" size="sm" className="text-gray-500">
                <File className="h-5 w-5 mr-1" />
                文档
              </Button>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-500"
                    onClick={handleWebLinkClick}
                    disabled={isLoadingWebContent}
                  >
                    {isLoadingWebContent ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-1 animate-spin" />
                        处理中...
                      </>
                    ) : (
                      <>
                        <Globe className="h-5 w-5 mr-1" />
                        网页
                      </>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96 p-4" align="start">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">输入网页链接</h3>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 rounded-full"
                        onClick={() => {
                          const popoverTrigger = document.querySelector('[data-slot="popover-trigger"]') as HTMLButtonElement;
                          if (popoverTrigger) {
                            popoverTrigger.click();
                          }
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <form onSubmit={handleWebContentSubmit}>
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="web-url-input" className="block text-xs font-medium mb-1">网页地址</label>
                          <Input
                            id="web-url-input"
                            placeholder="https://example.com"
                            className="w-full"
                          />
                          <div className="text-xs text-gray-500 mt-1">
                            输入网址后按回车或点击确定按钮
                          </div>
                        </div>

                        <div>
                          <label htmlFor="scraper-service-select" className="block text-xs font-medium mb-1">网页抓取服务</label>
                          <select
                            id="scraper-service-select"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            value={selectedScraperService}
                            onChange={(e) => {
                              setSelectedScraperService(e.target.value);
                            }}
                          >
                            <option value="firecrawl">FireCrawl</option>
                            <option value="jina">Jina Reader</option>
                          </select>
                          <div className="text-xs text-gray-500 mt-1">
                            选择用于抓取网页内容的服务
                          </div>
                        </div>

                        {providers && providers.length > 0 && (
                          <div>
                            <label htmlFor="model-select" className="block text-xs font-medium mb-1">选择模型（用于总结内容）</label>
                            <select
                              id="model-select"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              value={selectedModelId || ''}
                              onChange={(e) => {
                                setSelectedModelId(e.target.value || null);
                              }}
                            >
                              <option value="">选择模型</option>
                              {providers.map((provider) => (
                                <optgroup key={provider.id} label={provider.name}>
                                  {provider.models.map((model) => (
                                    <option key={model.id} value={model.id}>
                                      {model.name}
                                    </option>
                                  ))}
                                </optgroup>
                              ))}
                            </select>
                            <div className="text-xs text-gray-500 mt-1">
                              选择模型后将使用AI总结网页内容
                            </div>
                          </div>
                        )}

                        <div className="flex justify-end gap-2 pt-2">
                          <Button type="submit" className="bg-blue-500 hover:bg-blue-600 text-white">
                            确定
                          </Button>
                        </div>
                      </div>
                    </form>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="translation"
                  checked={enableTranslation}
                  onCheckedChange={(checked) => setEnableTranslation(checked as boolean)}
                />
                <Label htmlFor="translation" className="text-sm">翻译</Label>

                {enableTranslation && (
                  <select
                    className="ml-2 px-2 py-1 text-xs border border-gray-300 rounded-md"
                    value={translationModelId || ''}
                    onChange={(e) => setTranslationModelId(e.target.value || null)}
                  >
                    <option value="">选择模型</option>
                    {providers.map((provider) => (
                      <optgroup key={provider.id} label={provider.name}>
                        {provider.models.map((model) => (
                          <option key={model.id} value={model.id}>
                            {model.name}
                          </option>
                        ))}
                      </optgroup>
                    ))}
                  </select>
                )}
              </div>
              <Button
                className="bg-orange-500 hover:bg-orange-600"
                onClick={handleSubmit}
                disabled={isTranslating}
              >
                {isTranslating ? "翻译中..." : "发送"}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}