import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import {
  User,
  Hash,
  RefreshCw,
} from "lucide-react"

export function LeftSidebar() {
  return (
    <aside className="hidden md:block md:col-span-2 lg:col-span-2">
      <div className="sticky pb-2 space-y-4">
        <Card>
          {/* <CardHeader className="pb-2">
            <h2 className="text-xl font-bold">首页</h2>
          </CardHeader> */}
          <CardContent className="pt-0">
            <nav className="grid gap-2">
              <Button variant="ghost" className="justify-start gap-2">
                <Hash className="h-4 w-4" />
                全部关注
              </Button>
              <Button variant="ghost" className="justify-start gap-2">
                <RefreshCw className="h-4 w-4" />
                最新微博
              </Button>
              <Button variant="ghost" className="justify-start gap-2">
                <User className="h-4 w-4" />
                特别关注
              </Button>
              <Button variant="ghost" className="justify-start gap-2">
                <User className="h-4 w-4" />
                好友圈
              </Button>
            </nav>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">自定义分组</h3>
              <Badge variant="outline" className="text-xs">
                管理
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="pl-2 text-sm text-muted-foreground">
              <ul className="space-y-2">
                {[
                  "智能制造",
                  "数字孪生",
                  "工业大模型",
                  "工业智能",
                  "工业物联网",
                  "智能工厂",
                  "预测性维护",
                  "工业机器人",
                  "数据分析与优化",
                  "供应链管理",
                  "生产自动化",
                ].map((item, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <span className="h-1 w-1 rounded-full bg-muted-foreground"></span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </aside>
  )
}