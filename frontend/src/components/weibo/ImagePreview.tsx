import { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, ZoomIn, ZoomOut, RotateCw, RotateCcw, Maximize, Minimize } from 'lucide-react';
import { convertUrlForDocker } from '@/utils/url-utils';

interface ImagePreviewProps {
  src: string;
  alt?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ImagePreview({ src, alt, open, onOpenChange }: ImagePreviewProps) {
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // 重置图片状态
  const resetImage = () => {
    setScale(1);
    setRotation(0);
    setPosition({ x: 0, y: 0 });
  };

  // 当对话框关闭时重置图片状态
  useEffect(() => {
    if (!open) {
      resetImage();
    }
  }, [open]);

  // 处理缩放
  const handleZoom = (zoomIn: boolean) => {
    setScale(prevScale => {
      // 放大时增加更大的步长，以便更快地放大
      const step = zoomIn ? (prevScale >= 2 ? 0.5 : 0.25) : 0.25;
      const newScale = zoomIn ? prevScale + step : prevScale - step;
      return Math.max(0.25, Math.min(10, newScale)); // 增加最大缩放比例到 10x
    });
  };

  // 处理旋转
  const handleRotate = (clockwise: boolean) => {
    setRotation(prevRotation => {
      const newRotation = clockwise ? prevRotation + 90 : prevRotation - 90;
      return newRotation % 360;
    });
  };

  // 处理鼠标滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const zoomIn = e.deltaY < 0;
    handleZoom(zoomIn);
  };

  // 处理拖动开始
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button !== 0) return; // 只处理左键点击
    setIsDragging(true);
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  // 处理拖动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;
    setPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  // 处理拖动结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen().catch(err => {
        console.error(`全屏模式错误: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      switch (e.key) {
        case 'Escape':
          if (!isFullscreen) onOpenChange(false);
          break;
        case '+':
        case '=':
          handleZoom(true);
          break;
        case '-':
          handleZoom(false);
          break;
        case 'r':
          handleRotate(true);
          break;
        case 'R':
          handleRotate(false);
          break;
        case 'f':
        case 'F':
          toggleFullscreen();
          break;
        case '0':
          resetImage();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [open, isFullscreen, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-[98vw] max-h-[98vh] p-0 overflow-hidden"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="absolute top-0 left-0 right-0 z-10 bg-background/80 backdrop-blur-sm flex justify-between items-center px-4 py-2">
          <DialogTitle className="text-sm">图片预览</DialogTitle>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleZoom(true)}
              className="h-8 w-8"
              title="放大 (+)"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleZoom(false)}
              className="h-8 w-8"
              title="缩小 (-)"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRotate(true)}
              className="h-8 w-8"
              title="顺时针旋转 (R)"
            >
              <RotateCw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleRotate(false)}
              className="h-8 w-8"
              title="逆时针旋转 (Shift+R)"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={resetImage}
              className="h-8 w-8"
              title="重置 (0)"
            >
              <Minimize className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleFullscreen}
              className="h-8 w-8"
              title="全屏 (F)"
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8"
              title="关闭 (ESC)"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div
          ref={containerRef}
          className="h-[98vh] w-full flex items-center justify-center bg-black/90 cursor-grab"
          onWheel={handleWheel}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          <img
            ref={imageRef}
            src={convertUrlForDocker(src)}
            alt={alt || '图片'}
            className="max-w-full max-h-[90vh] object-contain transition-transform duration-200"
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale}) rotate(${rotation}deg)`,
              transformOrigin: 'center center',
            }}
            draggable={false}
          />
        </div>

        {/* 快捷键提示 */}
        <div className="absolute bottom-4 left-0 right-0 text-center">
          <div className="text-xs text-gray-500 bg-background/80 backdrop-blur-sm py-1 px-2 rounded-md inline-block">
            <span className="mr-2">鼠标滚轮: 缩放</span>
            <span className="mr-2">拖拽: 移动</span>
            <span className="mr-2">F: 全屏</span>
            <span className="mr-2">R: 旋转</span>
            <span>0: 重置</span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
