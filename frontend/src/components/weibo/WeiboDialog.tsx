import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { PostInput } from "@/components/weibo/PostInput";

interface WeiboDialogProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  mainContent?: string; // 添加主要内容属性
}

export function WeiboDialog({ isOpen, setIsOpen, mainContent = "" }: WeiboDialogProps) {
  // 处理发布成功的回调
  const handlePostSuccess = () => {
    // 可以在这里添加额外的逻辑，例如显示通知或刷新列表
    // 关闭对话框
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent
        className="p-0 max-w-6xl w-[95vw] overflow-hidden"
        style={{ height: '80vh', maxHeight: '800px' }} // 使用视口高度的百分比，但设置最大高度
        onInteractOutside={(e) => e.preventDefault()}>
        <div className="flex flex-col h-full">
          {/* 标题栏 */}
          <DialogHeader className="p-3 border-b bg-gray-50 flex flex-row items-center justify-between">
            <DialogTitle className="text-lg font-bold">发布微博</DialogTitle>
            <DialogClose className="p-1 rounded-full hover:bg-gray-200">
              <X className="h-4 w-4" />
            </DialogClose>
          </DialogHeader>

          {/* 内容区域 - 固定高度，添加滚动条 */}
          <div className="flex-1 p-4 overflow-hidden">
            {/* 添加自定义样式到 PostInput 组件的容器，添加滚动条 */}
            <div className="h-full overflow-y-auto pr-2">
              <PostInput
                onPostSuccess={handlePostSuccess}
                initialContent={mainContent} // 传递主要内容作为初始内容
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
