import { ExternalLink, FileText, X } from 'lucide-react';
import { useSearchStore } from '@/store/searchStore';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Button } from "../ui/button";
import { useState } from 'react';
import { Loader2 } from "lucide-react";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
}

interface SearchResultsGridProps {
  results: SearchResult[];
  onSelect: (snippets: string, shouldDisableSearch: boolean) => void;
  currentInput?: string;
}

export function SearchResultsGrid({ results, onSelect, currentInput = '' }: SearchResultsGridProps) {
  const { selectedResults, toggleSelected, clearSelected } = useSearchStore();

  const [isMarkdownOpen, setIsMarkdownOpen] = useState(false);
  const [markdownContent, setMarkdownContent] = useState('');
  const [loadingMarkdown, setLoadingMarkdown] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  const [currentTitle, setCurrentTitle] = useState('');
  
  const handleMarkdownConvert = async (url: string, title: string) => {
    setLoadingMarkdown(true);
    setIsMarkdownOpen(true);
    setCurrentUrl(url);
    setCurrentTitle(title);
    
    try {
      const response = await fetch(`https://r.jina.ai/${url}`, {
        // headers: {
        //   'x-engine': 'readerlm-v2'
        // }
      });
      
      if (response.ok) {
        const markdown = await response.text();
        setMarkdownContent(markdown);
      } else {
        setMarkdownContent('转换失败，请稍后重试');
      }
    } catch (error) {
      setMarkdownContent('转换失败，请稍后重试');
    } finally {
      setLoadingMarkdown(false);
    }
  };

  const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    e.stopPropagation();
    toggleSelected(index);  // 切换选中状态
    
    // 只获取当前选中项的 snippet
    const currentSnippet = results[index].snippet;
    
    // 追加新的 snippet 到当前输入
    const newInput = currentInput 
      ? `${currentSnippet}` 
      : currentSnippet;
    
    onSelect(newInput, false); // 不关闭搜索模式
  };

  const handleLinkClick = (e: React.MouseEvent, url: string) => {
    e.stopPropagation(); // 阻止事件冒泡
    window.open(url, '_blank');
  };

  return (
    <>
      <div className="space-y-4">
        {selectedResults.size > 0 && (
          <div className="flex items-center justify-between px-2">
            <span className="text-sm text-gray-500">
              已选择 {selectedResults.size} 项
            </span>
            <button
              onClick={clearSelected}
              className="text-sm text-red-500 hover:text-red-600 flex items-center gap-1"
            >
              <X className="h-4 w-4" />
              清除选择
            </button>
          </div>
        )}
        
        <div className="grid grid-cols-3 gap-4">
          {results.slice(0, 9).map((result, index) => (
            <div key={index} className="space-y-2">
              <div
                className={`p-4 rounded-lg border relative
                        ${selectedResults.has(index) 
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                          : 'border-gray-200 hover:border-blue-500 bg-white dark:bg-neutral-800 dark:border-neutral-700'}
                        cursor-pointer transition-all hover:shadow-md`}
              >
                {selectedResults.has(index) && (
                  <div className="absolute top-2 right-2 flex items-center justify-center size-5 rounded-full bg-blue-500 text-white text-xs">
                    {Array.from(selectedResults).indexOf(index) + 1}
                  </div>
                )}
                
                <div className="flex items-start justify-between gap-2">
                  <div className="flex items-start gap-2 flex-1">
                    <input
                      type="checkbox"
                      checked={selectedResults.has(index)}
                      onChange={(e) => handleCheckboxClick(e, index)}
                      className="mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded 
                              focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 
                              focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <h3 className="text-sm font-medium line-clamp-2 text-gray-900 dark:text-neutral-200">
                      {result.title}
                    </h3>
                  </div>
                  <ExternalLink 
                    className="h-4 w-4 flex-shrink-0 text-gray-400 hover:text-blue-500" 
                    onClick={(e) => handleLinkClick(e, result.url)}
                  />
                  <FileText
                    className="h-4 w-4 flex-shrink-0 text-gray-400 hover:text-blue-500 cursor-pointer"
                    onClick={() => handleMarkdownConvert(result.url, result.title)}
                  />
                </div>
                <p className="mt-2 text-xs text-gray-500 dark:text-neutral-400 line-clamp-3">
                  {result.snippet}
                </p>
              </div>
            </div> 
          ))}
        </div>
      </div>
      <Dialog open={isMarkdownOpen} onOpenChange={setIsMarkdownOpen}>
        <DialogContent className="max-w-3xl h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              <div className="flex flex-col gap-1">
                <div>Markdown 预览</div>
                <div className="text-sm font-normal text-gray-500">{currentTitle}</div>
              </div>
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto flex-1 h-[calc(80vh-8rem)] pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
            {loadingMarkdown ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="prose dark:prose-invert max-w-none">
                <div className="mb-4">
                  <a href={currentUrl} target="_blank" rel="noopener noreferrer" 
                     className="text-sm text-blue-500 hover:text-blue-600">
                    {currentUrl}
                  </a>
                </div>
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                  className="text-sm"
                >
                  {markdownContent}
                </ReactMarkdown>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}