import { useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { AlertCircle, Plus } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import { SourceList } from '@/components/search/SourceList';
import { Logo } from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { NaviBar } from "@/components/NaviBar";
import { AddThoughtDialog } from '../thoughts/AddThoughtDialog';
import { cn } from "@/lib/utils";
import { useHistoryStore } from '@/store/historyStore';
import { SearchHistory } from './SearchHistory';

interface SearchResultsProps {
  query: string;
  results: any;
  isLoading: boolean;
  error?: string | Error | undefined;
}

export function SearchResults({ query, results, isLoading, error }: SearchResultsProps) {
  const { conversations, currentConversationId } = useHistoryStore();
  const currentConversation = conversations.find(conv => conv.id === currentConversationId);
  const userMessages = currentConversation?.messages.filter(msg => msg.type === 'user') || [];

  const contentRef = useRef<HTMLDivElement>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const stripHtml = (html: string) => {
    if (!html) return '';
    return html
      .replace(/<[^>]+>/g, '\n')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\n\s*\n/g, '\n')
      .replace(/^\s+|\s+$/g, '');
  };

  if (error) {
    return (
      <Alert variant="destructive" className="animate-in fade-in-50">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {(typeof error === 'string' ? error : error?.message) || 'An error occurred while searching. Please try again.'}
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4 animate-in fade-in-50">
        <div className="flex justify-center mb-8">
          <Logo animate className="w-12 h-12" />
        </div>
        <Card className="p-6">
          <Skeleton className="h-4 w-3/4 mb-4" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-2/3" />
        </Card>
        <div className="space-y-2">
          <Skeleton className="h-[100px] w-full" />
          <Skeleton className="h-[100px] w-full" />
        </div>
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="relative min-h-screen">
      <NaviBar />
      <div className="w-[90%] mx-auto p-4">
        <div className="flex gap-4 justify-between">
          {/* 左侧主要内容区域 */}
          <div className="w-[70%]">
            <div ref={contentRef} className="space-y-6 animate-in fade-in-50">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="w-full bg-white border border-gray-200 rounded-xl shadow-lg dark:bg-neutral-800 dark:border-neutral-700 mb-6"
              >
                <SearchHistory query={query} />
              </motion.div>

              <div className="relative">
                <Card className="overflow-hidden shadow-md">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.4 }}
                    className="py-4 px-8"
                  >
                    <div
                      className={cn(
                        "prose prose-slate max-w-none",
                        "dark:prose-invert",
                        "prose-headings:font-bold prose-headings:mb-4",
                        "prose-h2:text-2xl prose-h2:mt-8 prose-h2:border-b prose-h2:pb-2 prose-h2:border-border",
                        "prose-h3:text-xl prose-h3:mt-6",
                        "prose-p:text-base prose-p:leading-7 prose-p:my-4",
                        "prose-ul:my-6 prose-ul:list-disc prose-ul:pl-6",
                        "prose-li:my-2 prose-li:marker:text-muted-foreground",
                        "prose-strong:font-semibold",
                        "prose-a:text-primary prose-a:no-underline hover:prose-a:text-primary/80",
                      )}
                      dangerouslySetInnerHTML={{ 
                        __html: results.summary
                      }}
                    />
                  </motion.div>
                  <Button
                    onClick={() => setIsDialogOpen(true)}
                    className="absolute bottom-3 right-3 rounded-full w-7 h-7 p-0"
                    variant="outline"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </Card>
                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm rounded-lg">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                      className="text-primary"
                    >
                      <Logo className="h-12 w-12 animate-spin" />
                    </motion.div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧 SourceList */}
          {results.sources && results.sources.length > 0 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="w-[20%] sticky top-4 self-start"
            >
              <SourceList sources={results.sources} />
            </motion.div>
          )}
        </div>

        <AddThoughtDialog 
          open={isDialogOpen} 
          onOpenChange={setIsDialogOpen}
          initialContent={stripHtml(results.summary)}
          initialSource="lcot"
        />
      </div>
    </div>
  );
}
