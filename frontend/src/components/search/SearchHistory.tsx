import { Button } from "@/components/ui/button";
import { Clock } from "lucide-react";
import { useHistoryStore } from "@/store/historyStore";

interface SearchHistoryProps {
  query: string;
}

export function SearchHistory({ query }: { query: string }) {
  const { conversations, currentConversationId, toggleDialog } = useHistoryStore();
  const currentConversation = conversations.find(conv => conv.id === currentConversationId);
  const userMessages = currentConversation?.messages.filter(msg => msg.type === 'user') || [];

  return (
    <div className="relative w-full bg-white dark:bg-neutral-800 rounded-xl p-4">
      <Button
        variant="ghost"
        size="icon"
        onClick={() => toggleDialog()}
        className="absolute top-2 right-2"
      >
        <Clock className="h-4 w-4" />
      </Button>
      <div className="flex">
        <div className="shrink-0">
          <svg className="size-5 text-blue-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
          </svg>
        </div>
        <div className="ms-4 space-y-2">
          <p className="text-lg font-medium text-gray-900 dark:text-neutral-200">
            "{query}"
          </p>
          {userMessages.slice(1).map((msg, index) => (
            <p key={index} className="text-sm text-gray-500 dark:text-neutral-400">
              → {msg.content}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
}