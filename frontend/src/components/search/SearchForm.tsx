import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { useSearchEngineStore } from '@/store/searchEngineStore';

interface SearchFormProps {
  onSearch: (query: string, engine: string) => void;
  initialQuery?: string;
  className?: string;
}

export function SearchForm({ onSearch, initialQuery = '', className = '' }: SearchFormProps) {
  const [query, setQuery] = useState(initialQuery);
  const { engines, selectedEngine, setSelectedEngine } = useSearchEngineStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      onSearch(query.trim(), selectedEngine);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={`w-full ${className}`}>
      <div className="flex">
        <select
          value={selectedEngine}
          onChange={(e) => setSelectedEngine(e.target.value)}
          className="flex-shrink-0 z-10 inline-flex items-center py-2.5 px-4 text-sm 
                    font-medium text-center text-gray-900 bg-gray-100 
                    border border-gray-300 rounded-s-lg 
                    hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 
                    dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-700 
                    dark:text-white dark:border-gray-600"
        >
          {engines.map((engine) => (
            <option key={engine.name} value={engine.name.toLowerCase()}>
              {engine.name}
            </option>
          ))}
        </select>

        <div className="relative w-full">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="三个臭皮匠来帮你研究学术..."
            className="block p-2.5 w-full z-20 text-sm text-gray-900 
                      bg-gray-50 rounded-e-lg border-s-gray-50 border-s-2 
                      border border-gray-300 focus:ring-blue-500 
                      focus:border-blue-500 dark:bg-gray-700 
                      dark:border-s-gray-700 dark:border-gray-600 
                      dark:placeholder-gray-400 dark:text-white 
                      dark:focus:border-blue-500"
            autoFocus
          />
          <button
            type="submit"
            disabled={!query.trim()}
            className="absolute top-0 end-0 p-2.5 text-sm font-medium h-full 
                      text-white bg-blue-700 rounded-e-lg border border-blue-700 
                      hover:bg-blue-800 focus:ring-4 focus:outline-none 
                      focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 
                      dark:focus:ring-blue-800 disabled:opacity-50 
                      disabled:hover:bg-blue-700"
          >
            <Search className="w-4 h-4" />
            <span className="sr-only">搜索</span>
          </button>
        </div>
      </div>
    </form>
  );
}