// TODO: 废弃

import { cn } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";  // 合并 import
import { X } from "lucide-react";
import { SearchInput } from '@/components/search/SearchInput';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search as SearchIcon, Globe, BookOpen } from "lucide-react";  // 添加图标导入
import API from '@/config/api';

interface SearchSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  className?: string;
}

// 定义搜索结果类型
interface SearchResult {
  title: string;
  link: string;
  snippet: string;
}

export function SearchSidePanel({ isOpen, onClose, children, className }: SearchSidePanelProps) {
  const [searchEngine, setSearchEngine] = useState<'google' | 'bing' | 'serper'>('google');

  const panelRef = useRef<HTMLDivElement>(null);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // 点击外部关闭面板
  // 修改点击外部关闭面板的处理函数
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击事件是否来自 Select 组件
      const isSelectClick = (event.target as Element)?.closest('[role="combobox"]');
      if (isSelectClick) return;  // 如果是 Select 相关的点击，直接返回

      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 修改处理搜索函数
  const handleSearch = async (query: string) => {
    setIsSearching(true);
    try {
      let response;

      if (searchEngine === 'bing') {
        // Bing 使用 GET 请求
        response = await fetch(`${API.SEARCH.BING}?q=${encodeURIComponent(query)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });
      } else {
        // Google Scholar 和 Serper 使用 POST 请求
        response = await fetch(API.SEARCH.BY_ENGINE(searchEngine), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            q: query,
            hl: 'zh-cn'
          })
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      let results: SearchResult[] = [];

      // 根据不同搜索引擎处理结果
      if (searchEngine === 'bing') {
        if (data.webPages?.value) {
          results = data.webPages.value.map((result: any) => ({
            title: result.name,
            link: result.url,
            snippet: result.snippet
          }));
        }
      } else {
        if (data.organic_results) {
          results = data.organic_results.map((result: any) => ({
            title: result.title,
            link: result.link,
            snippet: result.snippet
          }));
        }
      }

      setSearchResults(results);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]); // 清空搜索结果
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <>
      {/* 遮罩层 */}
      <div
        className={cn(
          "fixed inset-0 bg-black/30 backdrop-blur-sm z-50",
          "transition-opacity duration-300",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />

      {/* Drawer Panel */}
      <div
        ref={panelRef}
        className={cn(
          "fixed top-0 right-0 z-50",
          "w-[480px] h-screen",  // 修改宽度从 w-80 到 w-[480px]
          "flex flex-col",
          "bg-background",
          "border-l border-gray-200 dark:border-gray-800",
          "shadow-lg",
          "transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "translate-x-full",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center gap-4 p-4 border-b">
          <div className="text-lg font-semibold">搜索结果</div>
          <div className="flex-1">
            <Select
              defaultValue="google"
              onValueChange={(value) => setSearchEngine(value as typeof searchEngine)}
            >
              <SelectTrigger className="w-[120px]">
                {searchEngine === 'google' && <BookOpen className="h-4 w-4 mr-2" />}
                {searchEngine === 'bing' && <SearchIcon className="h-4 w-4 mr-2" />}
                {searchEngine === 'serper' && <Globe className="h-4 w-4 mr-2" />}
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="google">
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2" />
                    <span>Scholar</span>
                  </div>
                </SelectItem>
                <SelectItem value="bing">
                  <div className="flex items-center">
                    <SearchIcon className="h-4 w-4 mr-2" />
                    <span>Bing</span>
                  </div>
                </SelectItem>
                <SelectItem value="serper">
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    <span>Serper</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <SearchInput
                  onSearch={handleSearch}
                  isLoading={isSearching}
                  autoFocus
                  hideIcon  // 添加这个属性来隐藏重复的搜索图标
                  placeholder={`搜索...`}
                />
              </div>
            </div>

            {/* 搜索结果列表 */}
            <div className="space-y-4">
              {searchResults.map((result, index) => (
                <div key={index} className="p-4 rounded-lg border border-gray-200 dark:border-gray-800">
                  <a
                    href={result.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline font-medium"
                  >
                    {result.title}
                  </a>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {result.snippet}
                  </p>
                </div>
              ))}
            </div>

            {children}
          </div>
        </div>
      </div>
    </>
  );
}
