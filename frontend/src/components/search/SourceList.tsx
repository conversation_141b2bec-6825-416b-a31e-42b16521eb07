import { Card } from '@/components/ui/card';
import { ExternalLink, Link2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { useSearchEngineStore } from '@/store/searchEngineStore';

interface Source {
  title: string;
  url: string;
  snippet: string;
}

interface SourceListProps {
  sources?: any[];
}

const getHostname = (url: string) => {
  try {
    if (!url) return '';
    // 确保 URL 包含协议
    const urlWithProtocol = url.startsWith('http') ? url : `https://${url}`;
    return new URL(urlWithProtocol).hostname.replace('www.', '');
  } catch (error) {
    // 使用正则表达式提取域名
    const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
    return match ? match[1] : url;
  }
};

export function SourceList({ sources = [] }: SourceListProps) {
  const { selectedEngine } = useSearchEngineStore();

  return (
    <div className="space-y-4 animate-in fade-in-50">
      <div className="flex items-center gap-2 mb-2">
        <Link2 className="h-4 w-4 text-muted-foreground" />
        <h2 className="text-base font-semibold text-foreground/90">
          资料来源 ({selectedEngine.toUpperCase()})
        </h2>
      </div>

      <ScrollArea className="h-[calc(100vh-8rem)] w-full rounded-md">
        <motion.div 
          className="flex flex-col space-y-3 pr-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, staggerChildren: 0.1 }}
        >
          {sources.map((source, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card 
                className="group overflow-hidden transition-all hover:shadow-md cursor-pointer bg-card/50 hover:bg-card"
                onClick={() => window.open(source.url, '_blank')}
              >
                <div className="p-4 hover:bg-muted/30">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-sm text-foreground line-clamp-1 mb-1">
                        {source.title.replace(/\*\*/g, '')}
                      </h3>

                      {source.snippet && (
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                          {source.snippet.replace(/\*\*/g, '')}
                        </p>
                      )}

                      <div className="flex items-center gap-2 text-xs text-muted-foreground/70">
                        <span className="truncate max-w-[200px]">
                          {getHostname(source.url)}
                        </span>
                      </div>
                    </div>

                    <ExternalLink className="h-4 w-4 flex-shrink-0 text-muted-foreground 
                      opacity-50 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
        <ScrollBar orientation="vertical" />
      </ScrollArea>
    </div>
  );
}