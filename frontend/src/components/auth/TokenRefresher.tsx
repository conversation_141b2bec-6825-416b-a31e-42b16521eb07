import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/authStore';

// 定义 token 刷新的间隔时间（毫秒）
// 设置为 40 分钟，因为 Supabase 默认的 token 有效期是 1 小时
const REFRESH_INTERVAL = 40 * 60 * 1000; // 40 分钟
// 刷新失败后的重试间隔（毫秒）- 默认为 1 分钟
const RETRY_INTERVAL = 60 * 1000;
// 最大重试次数
const MAX_RETRIES = 3;

/**
 * TokenRefresher 组件
 *
 * 这个组件负责在后台自动刷新认证 token，以延长用户的登录 session 时间。
 * 它不渲染任何 UI 元素，只在组件挂载时设置一个定时器，定期刷新 token。
 * 增强了错误处理和重试机制。
 */
export function TokenRefresher() {
  const { token, refreshToken, refreshAuth } = useAuthStore();
  const [retryCount, setRetryCount] = useState(0);

  // 刷新 token 的函数
  const refreshTokenHandler = async () => {
    if (retryCount >= MAX_RETRIES) {
      console.error(`已达到最大重试次数 (${MAX_RETRIES})，停止尝试刷新 token`);
      return;
    }

    try {
      console.log('自动刷新 token...');
      await refreshAuth();
      console.log('token 刷新成功');
      // 重置重试计数
      setRetryCount(0);
    } catch (error) {
      console.error('自动刷新 token 失败:', error);
      // 增加重试计数
      setRetryCount(prev => prev + 1);

      // 如果还没达到最大重试次数，设置重试定时器
      if (retryCount < MAX_RETRIES - 1) {
        console.log(`将在 ${RETRY_INTERVAL / 1000} 秒后重试刷新 token (尝试 ${retryCount + 1}/${MAX_RETRIES})`);
        setTimeout(refreshTokenHandler, RETRY_INTERVAL);
      }
    }
  };

  useEffect(() => {
    // 只有当用户已登录（有 token 和 refreshToken）时才设置定时器
    if (!token || !refreshToken) {
      console.log('未检测到有效的认证信息，不设置 token 刷新定时器');
      return;
    }

    console.log(`设置 token 自动刷新定时器，间隔: ${REFRESH_INTERVAL / 1000 / 60} 分钟`);

    // 设置定时器，定期刷新 token
    const intervalId = setInterval(refreshTokenHandler, REFRESH_INTERVAL);

    // 组件卸载时清除定时器
    return () => {
      console.log('清除 token 自动刷新定时器');
      clearInterval(intervalId);
    };
  }, [token, refreshToken, refreshAuth, retryCount]);

  // 这个组件不渲染任何内容
  return null;
}
