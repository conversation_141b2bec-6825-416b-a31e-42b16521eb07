import React, { ComponentType, useState } from "react";
import { Check, Copy } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";

/**
 * Custom components for rendering Markdown content with enhanced styling and functionality.
 * This can be used with ReactMarkdown's `components` prop.
 *
 * Example usage:
 * ```tsx
 * import { MarkdownComponents } from "@/components/markdown/MarkdownComponents";
 *
 * <ReactMarkdown components={MarkdownComponents}>
 *   {markdownContent}
 * </ReactMarkdown>
 * ```
 */
export const MarkdownComponents: Record<string, ComponentType<any>> = {
  code({ className, children, ...props }) {
    const match = /language-(\w+)/.exec(className || "");
    const language = match ? match[1] : "";
    const [copied, setCopied] = useState(false);

    const copyToClipboard = async (text: string) => {
      try {
        await navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Failed to copy text: ", err);
      }
    };

    return match ? (
      <div className="relative">
        <div className="absolute top-0 right-0 flex items-center gap-2 px-2 py-1 text-xs text-muted-foreground bg-muted-foreground/10 rounded-bl">
          {language}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 hover:bg-muted-foreground/20"
                  onClick={() => copyToClipboard(String(children))}
                >
                  {copied ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{copied ? "Copied!" : "Copy code"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <SyntaxHighlighter
          style={oneDark}
          language={language}
          PreTag="div"
          className="mt-2 rounded-md"
          wrapLines={true}
          wrapLongLines={true}
          customStyle={{
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflowWrap: 'anywhere',
            maxWidth: '100%',
            overflow: 'auto'
          }}
          {...props}
        >
          {String(children).replace(/\n$/, "")}
        </SyntaxHighlighter>
      </div>
    ) : (
      <code
        className="bg-muted-foreground/20 rounded px-1 py-[2px] text-sm break-words whitespace-pre-wrap"
        style={{ wordBreak: 'break-word', overflowWrap: 'anywhere' }}
        {...props}
      >
        {children}
      </code>
    );
  },
  p({ children, ...props }) {
    return (
      <p className="mb-2 last:mb-0 break-words" style={{ wordBreak: 'break-word', overflowWrap: 'anywhere' }} {...props}>
        {children}
      </p>
    );
  },
  ul({ children, ...props }) {
    return (
      <ul className="list-disc pl-6 mb-2 space-y-1" {...props}>
        {children}
      </ul>
    );
  },
  ol({ children, ...props }) {
    return (
      <ol className="list-decimal pl-6 mb-2 space-y-1" {...props}>
        {children}
      </ol>
    );
  },
  li({ children, ...props }) {
    return (
      <li className="mb-1 last:mb-0 marker:text-foreground/70" {...props}>
        {children}
      </li>
    );
  },
  blockquote({ children, ...props }) {
    return (
      <blockquote
        className="border-l-4 border-muted-foreground/40 pl-4 italic my-2"
        {...props}
      >
        {children}
      </blockquote>
    );
  },
  h1({ children, ...props }) {
    return (
      <h1 className="text-2xl font-bold my-6 pb-2 border-b mb-8 border-muted-foreground/30" {...props}>
        {children}
      </h1>
    );
  },
  h2({ children, ...props }) {
    return (
      <h2 className="text-xl font-bold mt-9 mb-8" {...props}>
        {children}
      </h2>
    );
  },
  h3({ children, ...props }) {
    return (
      <h3 className="text-lg font-bold mt-7 mb-6" {...props}>
        {children}
      </h3>
    );
  },
  a({ children, href, ...props }) {
    return (
      <a
        href={href}
        className="text-blue-500 underline hover:text-blue-600 break-words"
        style={{ wordBreak: 'break-word', overflowWrap: 'anywhere' }}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    );
  },
  strong({ children, ...props }) {
    return (
      <strong className="font-bold" {...props}>
        {children}
      </strong>
    );
  },
  em({ children, ...props }) {
    return (
      <em className="italic" {...props}>
        {children}
      </em>
    );
  },
  table({ children, ...props }) {
    return (
      <div className="my-4 overflow-x-auto">
        <table
          className="min-w-full divide-y divide-muted-foreground/20"
          {...props}
        >
          {children}
        </table>
      </div>
    );
  },
  thead({ children, ...props }) {
    return (
      <thead className="bg-muted-foreground/5" {...props}>
        {children}
      </thead>
    );
  },
  tbody({ children, ...props }) {
    return (
      <tbody
        className="divide-y divide-muted-foreground/20 bg-muted-foreground/0"
        {...props}
      >
        {children}
      </tbody>
    );
  },
  tr({ children, ...props }) {
    return (
      <tr className="transition-colors hover:bg-muted-foreground/5" {...props}>
        {children}
      </tr>
    );
  },
  th({ children, ...props }) {
    return (
      <th className="px-4 py-3 text-left text-sm font-semibold" {...props}>
        {children}
      </th>
    );
  },
  td({ children, ...props }) {
    return (
      <td className="px-4 py-2 text-sm whitespace-normal break-words" style={{ maxWidth: '300px', wordBreak: 'break-word' }} {...props}>
        {children}
      </td>
    );
  },
};

// 添加一个辅助函数，用于将 LaTeX 公式转换为 KaTeX 可以渲染的格式
export const convertMathFormula = (content: string): string => {
  if (!content) return '';

  // 替换行内公式 \( ... \) 为 $ ... $
  let result = content.replace(/\\\(/g, '$').replace(/\\\)/g, '$');

  // 替换块级公式 \[ ... \] 为 $$ ... $$
  result = result.replace(/\\\[/g, '$$').replace(/\\\]/g, '$$');

  return result;
};
