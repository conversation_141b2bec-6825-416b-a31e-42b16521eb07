import { useLocation } from 'wouter';
import { ThemeToggle } from "./ThemeToggle";
import { Images, Bird, History, Lightbulb, LibraryBig, Settings, Home, LogOut, GraduationCap } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"


import { useHistoryStore } from "@/store/historyStore";
import { HistoryDialog } from "./chat/HistoryDialog";
import { useState, useEffect } from "react";
import { User } from "lucide-react";


export function NaviBar() {
  // 使用真实认证数据
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<{id: string, email: string, username: string} | null>(null);

  useEffect(() => {
    // 从 localStorage 获取用户信息
    const token = localStorage.getItem('token');
    const userJson = localStorage.getItem('user');

    if (token && userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  const logout = async () => {
    try {
      // 清除本地存储的认证信息
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // 更新状态
      setIsAuthenticated(false);
      setUser(null);

      // 跳转到登录页面并刷新
      setLocation('/');
      window.location.reload();
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  const [, setLocation] = useLocation();
  const { toggleDialog } = useHistoryStore();

  return (
    <>
      <TooltipProvider>
        <div className="fixed left-8 top-1/2 -translate-y-1/2
                      bg-background/80 backdrop-blur-sm
                      border rounded-lg shadow-lg
                      p-2 flex flex-col gap-2 z-999">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => {
                  setLocation('/');
                  // 强制刷新页面内容
                  window.location.reload();
                }}
              >
                <Home className="h-[1.2rem] w-[1.2rem]" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">首页</TooltipContent>
          </Tooltip>
          {/* <hr /> */}
          <hr />
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setLocation('/academic')}
              >
                <GraduationCap className="h-[1.2rem] w-[1.2rem] text-blue-600" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">学术助手</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setLocation('/weibo')}  // 直接使用 toggleDialog，不需要箭头函数包装
              >
                <Bird className="h-[1.2rem] w-[1.2rem] text-green-700" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">博客</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setLocation('/chat-history')}  // 直接使用 toggleDialog，不需要箭头函数包装
              >
                <History className="h-[1.2rem] w-[1.2rem] text-sky-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">历史对话</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => {
                  setLocation('/thoughts');
                  // 强制刷新页面内容
                  window.location.reload();
                }}
              >
                <Lightbulb className="h-[1.2rem] w-[1.2rem] text-yellow-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">随想</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setLocation('/diagrams')}
              >
                <Images className="h-[1.2rem] w-[1.2rem] text-slate-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">我的图库</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => setLocation('/documents')}
              >
                <LibraryBig className="h-[1.2rem] w-[1.2rem] text-purple-500" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">论文（Top 100）</TooltipContent>
          </Tooltip>


          <hr />
          <Tooltip>

            <TooltipTrigger asChild>
              <div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <User className="h-[1.2rem] w-[1.2rem]" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side="right"
                    align="start"
                    sideOffset={16}
                    alignOffset={-8}
                    className="w-56"
                  >
                    <div className="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                      <p className="text-sm text-gray-500 dark:text-neutral-400">登录账号</p>
                      <p className="text-sm mt-1 font-medium text-green-600 dark:text-neutral-300">
                        {user?.email || user?.username || '未登录'}
                      </p>
                    </div>


                    <DropdownMenuItem
                      className="flex items-center gap-x-3.5 px-3 py-2 cursor-pointer"
                      onClick={() => setLocation('/settings')}
                    >
                      <Settings className="h-4 w-4" />
                      <span>设置</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive flex items-center gap-x-3.5 px-3 py-2 cursor-pointer"
                      onClick={async () => {
                        try {
                          await logout();
                        } catch (error) {
                          console.error('登出失败:', error);
                        }
                      }}
                    >
                      <LogOut className="h-4 w-4" />
                      <span>登出</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TooltipTrigger>
            {/* <TooltipContent side="right">用户信息</TooltipContent> */}
          </Tooltip>
        </div>
      </TooltipProvider>
      <HistoryDialog />
    </>
  );
}