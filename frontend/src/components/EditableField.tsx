import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface EditableFieldProps {
  label: string;
  value: string;
  isTextarea?: boolean;
  onUpdate: (value: string) => Promise<void>;
}

export function EditableField({ label, value, isTextarea, onUpdate }: EditableFieldProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await onUpdate(editValue);
      setIsEditing(false);
    } catch (error) {
      console.error('Update failed:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div>
      <h4 className="text-sm font-medium mb-1">{label}</h4>
      {isEditing ? (
        <div className="space-y-2">
          {isTextarea ? (
            <Textarea
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="min-h-[100px]"
            />
          ) : (
            <Input
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
            />
          )}
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={handleUpdate}
              disabled={isUpdating}
            >
              {isUpdating ? '更新中...' : '更新'}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsEditing(false)}
              disabled={isUpdating}
            >
              取消
            </Button>
          </div>
        </div>
      ) : (
        <p
          className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 p-2 rounded"
          onClick={() => setIsEditing(true)}
        >
          {value}
        </p>
      )}
    </div>
  );
}