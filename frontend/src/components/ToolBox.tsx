import { useState } from 'react';
import { 
  Wand,
  ImagePlus,
  FilePlus,
  Plus,
  History,
  PocketKnife,
  PackagePlus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AddThoughtDialog } from './thoughts/AddThoughtDialog';
import { AddDiagramDialog } from './diagram/AddDiagramDialog';
import { AddDocumentDialog } from './document/AddDocumentDialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// import confetti from 'canvas-confetti';
import { GitFork } from 'lucide-react';  // 添加新图标导入
import { AddFlowchartDialog } from './diagram/AddFlowchartDialog';  // 导入新组件
import { useFlowchartStore } from '@/store/flowchartStore';
import { useDialogStore } from '@/store/dialogStore';

export function ToolBox() {
  const [isOpen, setIsOpen] = useState(false);
  const [isAddThoughtOpen, setIsAddThoughtOpen] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showAddDocumentDialog, setShowAddDocumentDialog] = useState(false);
  const [showFlowchartDialog, setShowFlowchartDialog] = useState(false);

  const { clearPendingText } = useFlowchartStore();
  const { setFlowchartOpen } = useDialogStore();

  const tools = [
    {
      icon: <Wand className="w-5 h-5" />,
      label: "开心一下",
      onClick: () => {console.log("happy time!")},
    },
    {
      icon: <PackagePlus className="w-5 h-5" />,
      label: "记录灵感",
      onClick: () => setIsAddThoughtOpen(true),
    },
    {
      icon: <ImagePlus className="w-5 h-5" />,
      label: "收藏图片",
      onClick: () => setShowAddDialog(true),
    },
    {
      icon: <FilePlus className="w-5 h-5" />,
      label: "收藏论文",
      onClick: () => setShowAddDocumentDialog(true),
    },
    {
      icon: <GitFork className="w-5 h-5" />,
      label: "生成流程图",
      onClick: () => {
        clearPendingText(); // 先清空 store
        setFlowchartOpen(true); // 再打开对话框
      },
    },
  ];

  return (
    <TooltipProvider>
      <div className="fixed end-6 bottom-6 group z-50">
        <div className={`flex flex-col items-center mb-4 space-y-2 ${isOpen ? 'block' : 'hidden'}`}>
          {tools.map((tool, index) => (
            <Tooltip key={index}>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={tool.onClick}
                  className="w-8 h-8 rounded-full bg-white dark:bg-gray-700"
                >
                  {tool.icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>{tool.label}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
        <Button
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className="w-8 h-8 rounded-full bg-purple-300 hover:bg-blue-700"
        >
          <PocketKnife className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-45' : ''}`} />
        </Button>
        <AddThoughtDialog 
          open={isAddThoughtOpen} 
          onOpenChange={setIsAddThoughtOpen}
        />
        <AddDiagramDialog 
          open={showAddDialog} 
          onOpenChange={setShowAddDialog}
        />
        <AddDocumentDialog 
          open={showAddDocumentDialog} 
          onOpenChange={setShowAddDocumentDialog}
        />
        <AddFlowchartDialog 
          open={showFlowchartDialog} 
          onOpenChange={setShowFlowchartDialog}
        />
      </div>
    </TooltipProvider>
  );
}