import { cn } from "@/lib/utils";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { MarkdownComponents } from "@/components/markdown/MarkdownComponents";
import { useState, useEffect } from "react";
import {
  Co<PERSON>,
  Check,
  Brain,
  <PERSON>,
  Cpu,
  Loader2
} from 'lucide-react';
import 'katex/dist/katex.min.css';
import '../pages/thinking-animation.css';

import { useLLMStore } from "@/store/llmStore";
import { useSelectedTextStore } from '@/store/selectedTextStore';
import { ThinkingProcessModal } from "@/components/ThinkingProcessModal";
import { WeiboDialog } from "@/components/weibo/WeiboDialog";
import { ThinkPopover } from "@/components/ThinkPopover";

// 为 Mermaid 添加类型声明
declare global {
  interface Window {
    mermaid: {
      initialize: (config: {
        startOnLoad: boolean;
        theme?: string;
        securityLevel?: string;
        logLevel?: number;
        flowchart?: object;
        sequence?: object;
        gantt?: object;
      }) => void;
      render: (id: string, code: string, callback: (svg: string) => void) => void;
    };
  }
}

interface ChatMessageProps {
  role: 'user' | 'assistant';
  content: string;
  onTextSelect?: (text: string) => void;
}

// 定义响应状态类型
type ResponseStatus = 'preparing' | 'thinking' | 'thought-complete' | 'processing' | 'complete' | 'idle';

export function ChatMessage({ role, content, onTextSelect }: ChatMessageProps) {
  const { addSelectedText } = useSelectedTextStore();
  const { providers, fetchProviders, selectedModel } = useLLMStore();

  // 状态管理
  const [isCopied, setIsCopied] = useState(false);
  const [isThinkPopoverOpen, setIsThinkPopoverOpen] = useState(false);
  const [isWeiboDialogOpen, setIsWeiboDialogOpen] = useState(false);

  // 思考完成后的状态
  const [isThinkingComplete, setIsThinkingComplete] = useState(false);
  // 整理完成状态（问答结束）
  const [isProcessingComplete, setIsProcessingComplete] = useState(false);
  // 响应状态
  const [responseStatus, setResponseStatus] = useState<ResponseStatus>('idle');

  // 获取模型列表
  useEffect(() => {
    if ((isThinkPopoverOpen || isWeiboDialogOpen) && providers.length === 0) {
      fetchProviders();
    }
  }, [isThinkPopoverOpen, isWeiboDialogOpen, providers.length, fetchProviders]);

  // 检测思考完成状态和整理完成状态
  useEffect(() => {
    if (role === 'assistant') {
      if (!content) {
        // 没有内容时，设置为准备状态
        setResponseStatus('preparing');
        setIsThinkingComplete(false);
        setIsProcessingComplete(false);
      } else {
        // 有内容时，检查是否有思考内容
        const { thinkContent } = extractThinkContent(content);
        const hasThinkContent = !!thinkContent;

        // 检查是否包含思考标签的开始部分
        const hasThinkingStarted = content.includes('<think>');

        // 检查是否包含思考标签的结束部分
        const hasThinkingEnded = content.includes('</think>');

        // 检查内容是否完整（是否包含完整的回答）
        const contentLength = content.length;

        // 更新思考完成状态
        setIsThinkingComplete(hasThinkingEnded);

        // 检查是否有明确的结束标记
        const isLongEnough = contentLength > 1000;
        const hasCompleteSentences = content.trim().endsWith('.') || content.trim().endsWith('。') || content.trim().endsWith('!') || content.trim().endsWith('！') || content.trim().endsWith('?') || content.trim().endsWith('？');

        // 判断是否有思考功能
        const hasThinkFeature = hasThinkingStarted || hasThinkingEnded;

        // 更新整理完成状态
        // 对于有思考功能的模型：只有当思考结束且满足所有完成条件时，才认为整理完成
        // 对于没有思考功能的模型：只需要满足内容长度和句子完整性条件
        const isProcessingComplete = hasThinkFeature
          ? (hasThinkingEnded && isLongEnough && hasCompleteSentences)
          : (isLongEnough && hasCompleteSentences);

        setIsProcessingComplete(isProcessingComplete);

        // 更新响应状态
        if (hasThinkFeature) {
          // 有思考功能的模型
          if (hasThinkingStarted && !hasThinkingEnded) {
            // 思考已开始但未结束 - 正在思考
            setResponseStatus('thinking');
          } else if (hasThinkingEnded && !isProcessingComplete) {
            // 思考已结束但整理未完成 - 正在整理
            setResponseStatus('processing');
          } else if (isProcessingComplete) {
            // 整理已完成 - 完成回答
            setResponseStatus('complete');
          }
        } else {
          // 没有思考功能的模型
          if (contentLength < 100) {
            // 内容很少 - 正在准备
            setResponseStatus('preparing');
          } else if (!isProcessingComplete) {
            // 内容较多但未完成 - 正在回答
            setResponseStatus('processing');
          } else {
            // 回答完成
            setResponseStatus('complete');
          }
        }

        // 调试信息
        // console.log('状态更新:', {
        //   content: contentLength > 50 ? content.substring(0, 50) + '...' : content,
        //   contentLength,
        //   hasThinkFeature,
        //   hasThinkingStarted,
        //   hasThinkingEnded,
        //   isLongEnough,
        //   hasCompleteSentences,
        //   isProcessingComplete,
        //   currentStatus: responseStatus,
        //   newStatus: responseStatus
        // });
      }
    }
  }, [role, content, responseStatus]);


  const handleTextSelection = () => {
    const selection = window.getSelection();
    const text = selection?.toString().trim();
    if (text) {
      onTextSelect?.(text);  // 调用父组件传入的处理函数
      addSelectedText(text); // 将选中的文本添加到 store
    }
  };

  // 提取 think 标签内容
  const extractThinkContent = (content: string): { mainContent: string, thinkContent: string | null } => {
    // 使用正则表达式匹配 <think> 标签及其内容
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const matches = content.match(thinkRegex);

    // 提取 think 内容（如果有）
    let thinkContent: string | null = null;
    if (matches && matches.length > 0) {
      // 提取第一个 <think> 标签中的内容
      thinkContent = matches[0].replace(/<think>|<\/think>/g, '').trim();
    }

    // 移除所有 <think> 标签及其内容
    const mainContent = content.replace(thinkRegex, '');

    return { mainContent, thinkContent };
  };





  // 获取当前状态对应的提示信息
  const getStatusMessage = () => {
    // 检查是否有思考功能
    const hasThinkFeature = content && (content.includes('<think>') || content.includes('</think>'));

    switch (responseStatus) {
      case 'preparing':
        return '正在准备中...';
      case 'thinking':
        return '正在思考中...';
      case 'thought-complete':
        return '思考完成，正在处理...';
      case 'processing':
        // 根据是否有思考功能显示不同的消息
        return hasThinkFeature
          ? '思考结束，正在整理回答...'
          : '正在生成回答...';
      case 'complete':
        return '回答完成';
      default:
        return '';
    }
  };

  // 获取状态对应的颜色样式
  const getStatusColorClass = () => {
    switch (responseStatus) {
      case 'preparing':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-300';
      case 'thinking':
        return 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-700 text-yellow-600 dark:text-yellow-300';
      case 'thought-complete':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-300';
      case 'processing':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-600 dark:text-green-300';
      case 'complete':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-300';
      default:
        return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={cn(
      "flex w-full gap-4 p-4 rounded-lg",
      role === 'assistant' ? "bg-muted/50" : ""
    )}>
      <div className="flex-1 space-y-2">
        <div
          className="prose prose-neutral dark:prose-invert max-w-none"
          onMouseUp={handleTextSelection}
        >

          {(() => {
            // 提取 think 内容和主要内容
            const { mainContent } = extractThinkContent(content);

            return (
              <>
                {/* 助手回复的状态指示器 */}
                {role === 'assistant' && (
                  <div className="mb-4">
                    {/* 顶部行：思考功能提示和模型名称 */}
                    <div className="flex justify-between items-center mb-2">
                      {/* 左侧：思考功能提示 - 只在内容包含思考标签时显示 */}
                      <div className="flex items-center">
                        {/* 思考功能提示 */}
                        {(content && content.includes('<think>')) ? (
                          <div className="flex items-center">
                            <div className="flex items-center px-3 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 text-xs text-yellow-700 dark:text-yellow-300 font-medium">
                              <Brain className="h-3 w-3 mr-1" />
                              <span>思考模式</span>
                            </div>
                            {/* 添加思考内容弹出框 */}
                            <ThinkPopover thinkContent={extractThinkContent(content).thinkContent} />
                          </div>
                        ) : (
                          <div className="flex items-center px-3 py-1 rounded-full bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400">
                            <span>标准模式</span>
                          </div>
                        )}
                      </div>

                      {/* 右侧：当前选择的模型 */}
                      {selectedModel && (
                        <div className="flex items-center px-3 py-1 rounded-full bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-400">
                          <Cpu className="h-3 w-3 mr-1" />
                          <span className="font-medium">{selectedModel.providerName} - {selectedModel.id}</span>
                        </div>
                      )}
                    </div>

                    {/* 当前状态提示信息 - 只在非完成状态时显示 */}
                    {responseStatus !== 'idle' && responseStatus !== 'complete' && (
                      <div className="flex justify-center mr-4">
                        <div className={cn(
                          "flex items-center px-4 py-1.5 rounded-full border text-xs leading-tight whitespace-nowrap min-w-[350px] max-w-[90%]",
                          getStatusColorClass()
                        )}>
                          {/* 使用更明显的加载动画 */}
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span className="truncate font-medium">{getStatusMessage()}</span>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* 主要内容 */}
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                  components={MarkdownComponents}
                >
                  {mainContent}
                </ReactMarkdown>
              </>
            );
          })()}

          {role === 'assistant' && (
                <div className="flex gap-1 mt-2 justify-end">
                  <button
                    onClick={() => {
                      const { mainContent } = extractThinkContent(content);
                      navigator.clipboard.writeText(mainContent);
                      setIsCopied(true);
                      setTimeout(() => setIsCopied(false), 2000);
                    }}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors relative"
                    title="复制内容"
                  >
                    {isCopied ? (
                      <>
                        <Check className="h-4 w-4 text-green-500" />
                        <span className="absolute -top-8 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-70">
                          copied
                        </span>
                      </>
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </button>

                  {/* 思考过程按钮和弹窗 */}
                  <ThinkingProcessModal
                    thinkContent={extractThinkContent(content).thinkContent}
                    mainContent={extractThinkContent(content).mainContent}
                    isOpen={isThinkPopoverOpen}
                    setIsOpen={setIsThinkPopoverOpen}
                  />

                  {/* 微博按钮 */}
                  <button
                    onClick={() => setIsWeiboDialogOpen(true)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    title="发布到微博"
                  >
                    <Bird className="h-4 w-4" />
                  </button>

                  {/* 微博对话框 */}
                  <WeiboDialog
                    isOpen={isWeiboDialogOpen}
                    setIsOpen={setIsWeiboDialogOpen}
                    mainContent={extractThinkContent(content).mainContent}
                  />
                </div>
              )}
        </div>
      </div>

    </div>
  );
}