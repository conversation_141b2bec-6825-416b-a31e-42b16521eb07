'use client';
import { useEffect, useRef, useState, useCallback } from 'react';
import * as pdfjs from 'pdfjs-dist';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { useSelectedTextStore } from '@/store/selectedTextStore';
import { Copy } from 'lucide-react';
import { toast } from 'sonner';
import '@/styles/pdf-text-layer.css';
import { convertSupabaseUrl, convertUrlForDocker } from '@/utils/url-utils';

// 定义接口，用于类型检查
interface TextItem {
  str?: string;
  dir?: string;
  transform?: number[];
  width?: number;
  height?: number;
  fontName?: string;
  fontSize?: number;
  [key: string]: any;
}

interface TextContent {
  items: any[];
  styles?: Record<string, any>;
  [key: string]: any;
}

// Set up the worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString();

interface PDFViewerProps {
  url: string;
  scale?: number;
  className?: string;
}

export function PDFViewer({ url, scale = 1.5, className = '' }: PDFViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [pdf, setPdf] = useState<PDFDocumentProxy | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [numPages, setNumPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const renderTaskRef = useRef<{ promise: Promise<void>; cancel: () => void } | null>(null);
  const [selectedText, setSelectedText] = useState<string>('');
  const [showCopyButton, setShowCopyButton] = useState(false);
  const [copyButtonPosition, setCopyButtonPosition] = useState({ x: 0, y: 0 });
  const { addSelectedText } = useSelectedTextStore();
  const [continuousMode, setContinuousMode] = useState(true);

  // 处理文本选择 - 改进版
  const handleTextSelection = useCallback(() => {
    // 使用 setTimeout 确保选择已完成
    setTimeout(() => {
      const selection = window.getSelection();
      const text = selection?.toString().trim();

      if (text) {
        setSelectedText(text);
        setShowCopyButton(true);

        // 获取选中文本的位置，用于显示复制按钮
        const range = selection?.getRangeAt(0);
        if (range) {
          const rect = range.getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect();

          if (containerRect) {
            // 计算复制按钮位置，确保在视口内
            const x = Math.min(
              rect.right - containerRect.left,
              containerRect.width - 50 // 确保按钮不会超出右边界
            );
            const y = Math.min(
              rect.bottom - containerRect.top,
              containerRect.height - 40 // 确保按钮不会超出底部边界
            );

            setCopyButtonPosition({ x, y });
          }
        }
      } else {
        // 不要立即隐藏复制按钮，让用户有时间点击
        // 点击外部区域时会自动隐藏
      }
    }, 10); // 短暂延迟，确保选择已完成
  }, []);

  // 复制选中的文本
  const copySelectedText = useCallback(() => {
    if (selectedText) {
      navigator.clipboard.writeText(selectedText)
        .then(() => {
          toast.success('文本已复制到剪贴板');
          addSelectedText(selectedText); // 将选中的文本添加到 store
        })
        .catch(err => {
          console.error('复制失败:', err);
          toast.error('复制失败');
        });

      setShowCopyButton(false);
    }
  }, [selectedText, addSelectedText]);

  // 渲染文本层 - 使用改进的方法
  const renderTextLayer = useCallback((textContent: TextContent, container: HTMLElement, viewport: any, pageNum: number) => {
    // 清除容器中的所有文本元素
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }

    // 设置文本层容器样式
    container.style.left = '0';
    container.style.top = '0';
    container.style.right = '0';
    container.style.bottom = '0';
    container.style.overflow = 'hidden';
    container.style.position = 'absolute';
    container.style.opacity = '0';  // 初始设为不可见
    container.style.color = 'transparent';
    container.style.userSelect = 'text';
    container.style.pointerEvents = 'auto';
    container.style.zIndex = '1';

    // 创建文本层
    const textLayerFrag = document.createDocumentFragment();
    const textDivs: HTMLElement[] = [];
    const textItems = textContent.items;

    // 文本层已经通过 viewport 进行了缩放，不需要额外的缩放因子

    // 为每个文本项创建一个 span 元素
    for (let i = 0; i < textItems.length; i++) {
      const item = textItems[i];

      // 跳过非文本项或没有文本内容的项
      if ('type' in item || !('str' in item) || !item.str || !item.transform) continue;

      const textItem = item as TextItem;

      // 计算文本位置和变换
      const tx = pdfjs.Util.transform(
        viewport.transform,
        textItem.transform
      );

      // 计算文本样式
      let angle = Math.atan2(tx[1], tx[0]);
      if (angle) {
        angle *= (180 / Math.PI);
      }

      // 创建文本元素
      const textDiv = document.createElement('span');
      textDiv.textContent = textItem.str || '';
      textDiv.setAttribute('data-page', pageNum.toString());
      textDiv.dataset.angle = angle ? angle.toString() : '0';

      // 设置样式
      const fontHeight = Math.sqrt(tx[2] * tx[2] + tx[3] * tx[3]);
      const style: any = {
        position: 'absolute',
        left: `${tx[4]}px`,
        top: `${tx[5]}px`,
        fontSize: `${fontHeight}px`,
        fontFamily: 'sans-serif',
        whiteSpace: 'pre',
        transformOrigin: '0% 0%',
        userSelect: 'text',
        pointerEvents: 'auto',
        cursor: 'text'
      };

      // 如果有旋转角度，添加旋转变换
      if (angle) {
        style.transform = `rotate(${angle}deg)`;
      }

      // 应用样式
      Object.assign(textDiv.style, style);

      textLayerFrag.appendChild(textDiv);
      textDivs.push(textDiv);
    }

    // 将所有文本元素一次性添加到容器中，提高性能
    container.appendChild(textLayerFrag);

    // 添加文本选择事件
    container.addEventListener('mouseup', handleTextSelection);

    // 延迟显示文本层，确保所有元素都已正确渲染
    setTimeout(() => {
      container.style.opacity = '1';
    }, 100);

    return textDivs;
  }, [handleTextSelection]);

  // 渲染单个页面
  const renderSinglePage = useCallback(async (pageNum: number) => {
    if (!pdf || !containerRef.current) return null;

    try {
      // 获取当前页
      const page = await pdf.getPage(pageNum);
      const viewport = page.getViewport({ scale });

      // 创建页面容器
      const pageContainer = document.createElement('div');
      pageContainer.className = 'pdf-page relative';
      pageContainer.setAttribute('data-page', pageNum.toString());

      // 创建 canvas 元素
      const canvas = document.createElement('canvas');
      canvas.setAttribute('data-page', pageNum.toString());
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      canvas.style.width = '100%';
      canvas.style.height = 'auto';

      // 创建文本层
      const textLayerDiv = document.createElement('div');
      textLayerDiv.className = 'textLayer';
      textLayerDiv.setAttribute('data-page', pageNum.toString());

      // 添加元素到页面容器
      pageContainer.appendChild(canvas);
      pageContainer.appendChild(textLayerDiv);

      // 如果是连续模式，添加页码标签
      if (continuousMode) {
        const pageLabel = document.createElement('div');
        pageLabel.className = 'absolute top-2 right-2 bg-white/80 px-2 py-1 rounded text-xs';
        pageLabel.textContent = `${pageNum} / ${numPages}`;
        pageContainer.appendChild(pageLabel);
      }

      // 渲染页面到 canvas
      const canvasContext = canvas.getContext('2d');
      if (!canvasContext) return null;

      const renderContext = { canvasContext, viewport };
      const renderTask = page.render(renderContext);

      await renderTask.promise;

      // 获取文本内容并渲染到文本层
      const textContent = await page.getTextContent();
      renderTextLayer(textContent, textLayerDiv, viewport, pageNum);

      return pageContainer;
    } catch (err) {
      if (err instanceof Error && err.name === 'RenderingCancelledException') {
        console.log('渲染已取消');
      } else {
        console.error('渲染 PDF 页面失败:', err);
        setError('渲染 PDF 页面失败');
      }
      return null;
    }
  }, [pdf, scale, numPages, continuousMode, renderTextLayer]);

  // 加载 PDF 文档
  useEffect(() => {
    if (!url) return;

    setLoading(true);
    setError(null);

    const loadPDF = async () => {
      try {
        // 取消之前的渲染任务
        if (renderTaskRef.current) {
          renderTaskRef.current.cancel();
          renderTaskRef.current = null;
        }

        // 转换 URL 使其在浏览器中可访问
        // 先应用 Docker URL 转换，再应用 Supabase URL 转换
        const dockerConvertedUrl = convertUrlForDocker(url);
        const convertedUrl = convertSupabaseUrl(dockerConvertedUrl);
        console.log('PDF URL:', url);
        console.log('转换后的 URL:', convertedUrl);

        // 加载 PDF 文档
        const loadingTask = pdfjs.getDocument(convertedUrl);
        const pdfDocument = await loadingTask.promise;

        setPdf(pdfDocument);
        setNumPages(pdfDocument.numPages);
        setCurrentPage(1);
      } catch (err) {
        console.error('加载 PDF 失败:', err);
        setError('PDF 加载失败，请检查文件路径是否正确');
      } finally {
        setLoading(false);
      }
    };

    loadPDF();

    // 清理函数
    return () => {
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
        renderTaskRef.current = null;
      }
    };
  }, [url]);

  // 渲染 PDF 页面
  useEffect(() => {
    if (!pdf || !containerRef.current) return;

    // 取消之前的渲染任务
    if (renderTaskRef.current) {
      renderTaskRef.current.cancel();
      renderTaskRef.current = null;
    }

    // 清除容器中的所有元素
    while (containerRef.current.firstChild) {
      containerRef.current.removeChild(containerRef.current.firstChild);
    }

    const renderPages = async () => {
      if (continuousMode) {
        // 连续模式 - 渲染所有页面
        const wrapper = document.createElement('div');
        wrapper.className = 'pdf-pages-wrapper flex flex-col gap-4';
        containerRef.current?.appendChild(wrapper);

        for (let pageNum = 1; pageNum <= numPages; pageNum++) {
          const pageContainer = await renderSinglePage(pageNum);
          if (pageContainer) {
            wrapper.appendChild(pageContainer);
          }
        }
      } else {
        // 单页模式 - 只渲染当前页
        const pageContainer = await renderSinglePage(currentPage);
        if (pageContainer) {
          containerRef.current?.appendChild(pageContainer);
        }
      }
    };

    renderPages();
  }, [pdf, currentPage, scale, numPages, continuousMode, renderSinglePage]);

  // 页面导航函数
  const goToNextPage = () => {
    if (currentPage < numPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // 切换连续模式
  const toggleContinuousMode = () => {
    setContinuousMode(!continuousMode);
  };

  // 点击文档其他区域时关闭复制按钮
  useEffect(() => {
    const handleClickOutside = () => {
      if (showCopyButton) {
        const selection = window.getSelection();
        if (!selection || selection.toString().trim() === '') {
          setShowCopyButton(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showCopyButton]);

  return (
    <div className={`pdf-viewer ${className}`}>
      {loading && (
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">正在加载 PDF 文件...</div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">{error}</div>
        </div>
      )}

      {!loading && !error && (
        <>
          <div className="pdf-controls flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <button
                onClick={goToPrevPage}
                disabled={currentPage <= 1 || continuousMode}
                className="px-3 py-1 bg-gray-200 rounded disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={goToNextPage}
                disabled={currentPage >= numPages || continuousMode}
                className="px-3 py-1 bg-gray-200 rounded disabled:opacity-50"
              >
                下一页
              </button>
            </div>

            <div className={continuousMode ? "hidden" : ""}>
              第 {currentPage} 页 / 共 {numPages} 页
            </div>

            <button
              onClick={toggleContinuousMode}
              className={`px-3 py-1 rounded ${continuousMode ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              title={continuousMode ? "切换到单页模式" : "切换到连续滚动模式"}
            >
              {continuousMode ? "单页模式" : "连续模式"}
            </button>
          </div>

          <div className="relative h-full">
            <div
              ref={containerRef}
              className={`pdf-container border border-gray-300 rounded h-full overflow-auto`}
              style={{
                height: continuousMode ? 'auto' : '100%',
                maxHeight: 'calc(100vh - 12rem)'
              }}
            ></div>

            {/* 复制按钮 - 改进样式 */}
            {showCopyButton && (
              <button
                className="absolute bg-blue-500 text-white shadow-md rounded-md p-2 z-50 flex items-center gap-1 text-xs transition-opacity hover:bg-blue-600"
                style={{
                  top: `${copyButtonPosition.y + 10}px`,
                  left: `${copyButtonPosition.x - 40}px`,
                  opacity: 0.9,
                }}
                onClick={copySelectedText}
              >
                <Copy className="h-3 w-3" />
                复制
              </button>
            )}
          </div>
        </>
      )}
    </div>
  );
}
