"use client"

import { useState } from "react"

interface SimpleFileUploaderProps {
  onFileChange?: (file: File | null) => void;
  acceptedFileTypes?: string;
  maxSizeText?: string;
  className?: string;
}

export function SimpleFileUploader({
  onFileChange,
  acceptedFileTypes = ".pdf,.doc,.docx,.txt,.md",
  maxSizeText = "支持 PDF, Word, TXT, Markdown 等格式",
  className = ""
}: SimpleFileUploaderProps) {
  const [file, setFile] = useState<File | null>(null)
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null
    setFile(selectedFile)
    if (onFileChange) {
      onFileChange(selectedFile)
    }
  }

  // 处理拖放功能
  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      setFile(droppedFile);
      if (onFileChange) {
        onFileChange(droppedFile);
      }
    }
  };
  
  return (
    <div className={`w-full ${className}`}>
      <label
        htmlFor="simple-dropzone-file"
        className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          <UploadIcon className="w-8 h-8 text-gray-400 mb-2" />
          <p className="mb-1 text-sm text-gray-500">
            <span className="font-semibold">点击上传</span> 或拖拽文件到此处
          </p>
          <p className="text-xs text-gray-500">{maxSizeText}</p>
          {file && (
            <p className="text-xs text-blue-500 mt-1 font-medium truncate max-w-full px-4">
              已选择: {file.name}
            </p>
          )}
        </div>
        <input 
          id="simple-dropzone-file" 
          type="file" 
          className="hidden" 
          onChange={handleFileChange} 
          accept={acceptedFileTypes}
        />
      </label>
    </div>
  )
}

function UploadIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
      <polyline points="17 8 12 3 7 8" />
      <line x1="12" x2="12" y1="3" y2="15" />
    </svg>
  )
}
