import { useRef, useState } from 'react';
import { Upload } from 'lucide-react';

interface ConvertFileDropzoneProps {
  onFileSelect: (file: File | null) => void;
  allowPaste?: boolean;
  acceptedTypes?: string;
}

export function ConvertFileDropzone({ onFileSelect, allowPaste = true, acceptedTypes }: ConvertFileDropzoneProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      onFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onFileSelect(e.target.files[0]);
    }
  };

  const handlePaste = allowPaste ? (e: React.ClipboardEvent) => {
    if (e.clipboardData.files && e.clipboardData.files[0]) {
      onFileSelect(e.clipboardData.files[0]);
    }
  } : undefined;

  return (
    <div
      className={`flex items-center justify-center w-full ${isDragging ? 'border-blue-500' : ''}`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onPaste={handlePaste}
    >
      <label 
        htmlFor="convert-dropzone-file" 
        className="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500"
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          <Upload className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
          <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
            <span className="font-semibold">点击上传</span> 或拖拽文件
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            支持 PDF, Word, Excel, PowerPoint 等格式
          </p>
        </div>
        <input
          id="convert-dropzone-file"
          type="file"
          onChange={handleFileSelect}
          accept={acceptedTypes}
          className="hidden"
          ref={fileInputRef}
        />
      </label>
    </div>
  );
}