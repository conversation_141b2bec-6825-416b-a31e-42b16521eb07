import { ScrollArea } from '@/components/ui/scroll-area';
import { useHistoryStore } from '@/store/historyStore';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface Message {
  type: 'user' | 'system';
  content: string;
  timestamp: number;
}

interface Conversation {
  id: string;
  messages: Message[];
  createdAt: number;
}

export function HistoryDialog() {
  const { isOpen, toggleDialog, conversations } = useHistoryStore();
  // console.log('Conversations in HistoryDialog:', conversations);

  if (!isOpen) return null;

  return (
    <div className="fixed top-0 right-0 w-[320px] h-screen border-l bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 shadow-lg animate-in slide-in-from-right z-50">
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-xl font-semibold">历史记录</h2>
        <Button variant="ghost" size="icon" onClick={toggleDialog}>
          <X className="h-4 w-4" />
        </Button>
      </div>
      <ScrollArea className="h-[calc(100vh-4rem)]">
        <div className="p-4 space-y-4">
          {conversations.length === 0 && (
            <p className="text-muted-foreground text-center py-4">
              暂无历史记录
            </p>
          )}
          {conversations.map((conversation) => (
            <div key={conversation.id} className="border rounded-lg p-4 space-y-3 hover:bg-accent/50 transition-colors">
              <div className="text-sm text-muted-foreground">
                {new Date(conversation.createdAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
              <div className="space-y-2">
                {conversation.messages.map((message, index) => (
                  <div 
                    key={index}
                    className={`p-3 rounded-lg ${
                      message.type === 'user' 
                        ? 'bg-primary/10 ml-auto max-w-[85%]' 
                        : 'bg-muted mr-auto max-w-[85%]'
                    }`}
                  >
                    <div className="text-xs text-muted-foreground mb-1">
                      {message.type === 'user' ? '你' : '助手'}
                    </div>
                    <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
                    <div className="text-xs text-muted-foreground mt-1">
                      {new Date(message.timestamp).toLocaleString('zh-CN', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
