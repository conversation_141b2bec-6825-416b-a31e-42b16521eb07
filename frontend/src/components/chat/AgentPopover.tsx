import { useState, useEffect } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Search, RefreshCw, Bot } from "lucide-react";
import { useAgentStore } from "@/store/agentStore";

interface AgentPopoverProps {
  onAgentSelect: (agentId: string) => void;
}

export function AgentPopover({ onAgentSelect }: AgentPopoverProps) {
  const { agents, currentAgent, setCurrentAgent } = useAgentStore();
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState("");

  // 当 Popover 打开时获取最新的 agents 数据
  useEffect(() => {
    if (isOpen) {
      // 如果需要从后端获取 agents 数据，可以在这里添加获取逻辑
    }
  }, [isOpen]);

  // 过滤 agents
  const filteredAgents = Object.entries(agents).filter(([id, agent]) =>
    agent.name.toLowerCase().includes(filter.toLowerCase()) ||
    id.toLowerCase().includes(filter.toLowerCase())
  );

  // 处理 agent 选择
  const handleAgentSelect = (agentId: string) => {
    setCurrentAgent(agentId);
    onAgentSelect(agentId);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                text-gray-500 hover:bg-gray-100 focus:z-10 focus:outline-none
                focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                dark:focus:bg-neutral-700 bg-transparent"
        >
          <Bot className="size-4 text-blue-500" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-100">
        <div className="space-y-4">
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <div className="text-sm font-medium">选择 Agent...</div>
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  // 如果需要刷新 agents 列表，可以在这里添加刷新逻辑
                }}
                variant="ghost"
                size="icon"
                className="p-1 hover:bg-gray-100 rounded-full dark:hover:bg-neutral-700
                         transition-colors duration-200 bg-transparent"
              >
                <RefreshCw className="h-4 w-4 text-gray-500 dark:text-neutral-400" />
              </Button>
            </div>
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                placeholder="搜索 Agent..."
                className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                     focus:outline-none focus:ring-1 focus:ring-blue-500
                     dark:bg-neutral-800 dark:border-neutral-700
                     dark:text-neutral-200 dark:placeholder-neutral-400"
              />
            </div>
          </div>
          <div className="max-h-[300px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
            {filteredAgents.map(([id, agent]) => (
              <button
                key={id}
                onClick={() => handleAgentSelect(id)}
                className={`w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                      dark:hover:bg-neutral-800 flex items-center justify-between gap-2
                      ${currentAgent === id ? 'bg-gray-100/70 dark:bg-neutral-800/70' : 'bg-transparent'}`}
              >
                <div className="flex items-center gap-2">
                  <Bot className={`h-4 w-4 ${
                    id === 'deep' ? 'text-blue-500' :
                    id === 'academic' ? 'text-purple-500' :
                    id === 'digital' ? 'text-green-500' :
                    id === 'stooges' ? 'text-orange-500' : 'text-gray-500'
                  }`} />
                  <span className="line-clamp-1">{agent.name}</span>
                </div>
                {currentAgent === id && (
                  <span className="text-xs text-blue-500">当前选择</span>
                )}
              </button>
            ))}
          </div>

          <div className="text-xs text-gray-500 pt-2 border-t">
            <p>Agent 代表不同的系统提示词，可以帮助 AI 以不同的角色和专业知识回答问题。</p>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
