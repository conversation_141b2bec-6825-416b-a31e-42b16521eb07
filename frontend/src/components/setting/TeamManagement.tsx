export function TeamManagement() {
  // 显示欢迎界面，在建中...
  return (
    <div className="flex flex-col items-center justify-center h-96 text-center">
      <div className="mb-8">
        <svg
          className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-600"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
          <circle cx="9" cy="7" r="4" />
          <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
          <path d="M16 3.13a4 4 0 0 1 0 7.75" />
        </svg>
      </div>
      <h2 className="text-3xl font-bold mb-4 text-gray-800 dark:text-white">欢迎使用团队管理</h2>
      <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">功能正在建设中，敬请期待...</p>
      <div className="flex space-x-2 justify-center">
        <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full"></div>
        <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full animation-delay-200"></div>
        <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full animation-delay-400"></div>
      </div>
    </div>
  );
}
