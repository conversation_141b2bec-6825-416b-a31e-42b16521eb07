import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { CustomModelDetail } from "./CustomModelDetail";
import { ServiceConfigPanel } from "./ModelConfigPanel";
import { ModelType, MODEL_CATEGORIES } from "@/store/modelConfigStore";
import { useModelConfigStore } from "@/store/modelConfigStore";

interface ModelService {
  id: string;
  type: ModelType;  // 使用 store 中定义的类型
  name: string;
  icon: string;
  status: boolean;
  apiKey?: string;
  baseUrl?: string;
  models?: {
    name: string;
    isSystem?: boolean;
  }[];
}

interface CustomModel {
  id: string;
  type: ModelType;  // 使用 store 中定义的类型
  name: string;
  baseModel: string;
  status: "training" | "ready" | "failed";
  createdAt: string;
}

// 添加服务类别接口
interface ServiceCategory {
  id: string;
  name: string;
  description: string;
  services: ModelService[];
}

export function ModelServiceConfig() {
  // 使用 MODEL_CATEGORIES 替代本地 categories 状态
  const { customModels } = useModelConfigStore();
  const categories = MODEL_CATEGORIES.map(category => ({
    ...category,
    // Ensure each service has a unique ID by combining category ID and service name
    services: category.services?.map(service => ({
      ...service,
      id: `${category.id}-${service.name}-${Math.random().toString(36).substr(2, 9)}`
    })) || []
  }));



  // 状态管理
  const [activeCategory, setActiveCategory] = useState<ServiceCategory["id"]>("language");
  const [selectedModel, setSelectedModel] = useState<CustomModel | null>(null);

  const getStatusColor = (status: CustomModel['status']) => {
    switch (status) {
      case 'ready':
        return 'text-green-600 bg-green-50 dark:bg-green-900/30 dark:text-green-400';
      case 'training':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 bg-red-50 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className="flex gap-6 h-full">
      {/* 主要内容区域 */}
      <div className="flex-1 space-y-6 overflow-auto p-6">
        {/* 类别切换按钮组 */}
        <div className="flex gap-2 mb-4">
          {MODEL_CATEGORIES.map((category) => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              onClick={() => {
                setActiveCategory(category.id);
                setSelectedModel(null);
              }}
            >
              {category.name}
            </Button>
          ))}
        </div>

        {/* 服务配置或定制模型详情 */}
        {selectedModel ? (
          <CustomModelDetail model={selectedModel} onClose={() => setSelectedModel(null)} />
        ) : (
          <ServiceConfigPanel 
            category={categories.find(c => c.id === activeCategory)!} 
          />
        )}
      </div>

      {/* 右侧边栏 - 定制模型列表 */}
      <div className="w-80 flex-shrink-0 border-l dark:border-neutral-800 overflow-auto">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">定制智能代理</h3>
          </div>

          <div className="space-y-3">
            {customModels
              .filter(model => model.type === activeCategory)
              .map((model) => (
                <div 
                  key={model.id}
                  className="p-3 bg-white dark:bg-neutral-900 rounded-lg border dark:border-neutral-800 hover:border-blue-500 dark:hover:border-blue-500 cursor-pointer transition-colors"
                  onClick={() => setSelectedModel(model)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {model.name}
                    </h4>
                    <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(model.status)}`}>
                      {model.status === 'ready' && '已就绪'}
                      {model.status === 'training' && '训练中'}
                      {model.status === 'failed' && '失败'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-neutral-400">
                    基础模型: {model.baseModel}
                  </p>
                  <p className="text-xs text-gray-400 dark:text-neutral-500 mt-2">
                    创建于 {model.createdAt}
                  </p>
                </div>
              ))}
          </div>
        </div>
      </div>
      
    </div>
  );
}