import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect } from "react";
import { useSearchEngineStore } from "@/store/searchEngineStore";
import { Slider } from "@/components/ui/slider";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Edit, Check, X, ExternalLink } from "lucide-react";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import API from "@/config/api";

export function SearchEngineConfig() {
  // const { engines, fetchEngines, providers } = useSearchEngineStore();
  const [selectedEngine, setSelectedEngine] = useState<string>("");
  const [apiKey, setApiKey] = useState<string>("");
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [blacklist, setBlacklist] = useState<string>("");
  const [includeDate, setIncludeDate] = useState<boolean>(true);
  const [augmentMode, setAugmentMode] = useState<boolean>(false);
  const [resultCount, setResultCount] = useState<number>(10);

  // 添加新的状态
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [newEngine, setNewEngine] = useState({
    name: "",
    provider: "",
    url: "",
    token: "",
    note: ""
  });

  // 分组后的引擎
  const [groupedEngines, setGroupedEngines] = useState<{[key: string]: typeof engines}>({});
  const { engines, fetchEngines, providers } = useSearchEngineStore();

  useEffect(() => {
    fetchEngines();
  }, [fetchEngines]);

        // 处理提供商选择变化
        const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
          const selectedProvider = e.target.value;
          const providerConfig = providers.find(p => p.value === selectedProvider);
          
          // 更新引擎配置，自动填充默认 API URL
          setNewEngine({
            ...newEngine, 
            provider: selectedProvider,
            url: providerConfig?.apiUrl || ''
          });
        };


  useEffect(() => {
    if (engines.length > 0 && !selectedEngine) {
      setSelectedEngine(engines[0].id);
    }

    // 按提供商分组
    const grouped = engines.reduce((acc, engine) => {
      const provider = engine.provider;
      if (!acc[provider]) {
        acc[provider] = [];
      }
      acc[provider].push(engine);
      return acc;
    }, {} as {[key: string]: typeof engines});

    setGroupedEngines(grouped);
  }, [engines, selectedEngine]);

  // 获取当前选中的引擎
  const currentEngine = engines.find(engine => engine.id === selectedEngine);

  // 验证 API 密钥
  const verifyApiKey = async () => {
    try {
      // 这里可以添加验证 API 密钥的逻辑
      toast({
        title: "验证成功",
        description: "API 密钥有效",
      });
    } catch (error) {
      toast({
        title: "验证失败",
        description: "API 密钥无效或已过期",
        variant: "destructive",
      });
    }
  };

  // 保存设置
  const saveSettings = async () => {
    try {
      if (!currentEngine) return;

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录或会话已过期，请重新登录');
      }

      const response = await fetch(`${API.SEARCH.ENGINES}/${currentEngine.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          token: apiKey || currentEngine.token,
          // 其他可能需要更新的字段
        })
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      await fetchEngines();

      toast({
        title: "保存成功",
        description: "搜索引擎设置已更新",
      });
    } catch (error) {
      toast({
        title: "保存失败",
        description: "无法保存设置，请稍后重试",
        variant: "destructive",
      });
    }
  };

  // 添加新引擎
  const addEngine = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录或会话已过期，请重新登录');
      }

      const response = await fetch(API.SEARCH.ENGINES, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: newEngine.name,
          provider: newEngine.provider,
          url: newEngine.url,
          token: newEngine.token,
          note: newEngine.note,
          is_active: true
        })
      });

      if (!response.ok) {
        throw new Error('添加失败');
      }

      await fetchEngines();
      setIsAddDialogOpen(false);
      setNewEngine({
        name: "",
        provider: "",
        url: "",
        token: "",
        note: ""
      });

      toast({
        title: "添加成功",
        description: "新的搜索引擎已添加",
      });
    } catch (error) {
      toast({
        title: "添加失败",
        description: "无法添加新的搜索引擎，请稍后重试",
        variant: "destructive",
      });
    }
  };

  // 更新引擎
  const updateEngine = async () => {
    try {
      if (!currentEngine) return;

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录或会话已过期，请重新登录');
      }

      const response = await fetch(`${API.SEARCH.ENGINES}/${currentEngine.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: newEngine.name || currentEngine.name,
          provider: newEngine.provider || currentEngine.provider,
          url: newEngine.url || currentEngine.url,
          token: newEngine.token || currentEngine.token,
          note: newEngine.note || currentEngine.note
        })
      });

      if (!response.ok) {
        throw new Error('更新失败');
      }

      await fetchEngines();
      setIsEditDialogOpen(false);
      setNewEngine({
        name: "",
        provider: "",
        url: "",
        token: "",
        note: ""
      });

      toast({
        title: "更新成功",
        description: "搜索引擎已更新",
      });
    } catch (error) {
      toast({
        title: "更新失败",
        description: "无法更新搜索引擎，请稍后重试",
        variant: "destructive",
      });
    }
  };

  // if (engines.length === 0) {
  //   return <div className="p-6 text-center">加载搜索引擎信息中...</div>;
  // }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">网络搜索提供商</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              添加提供商
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新的搜索提供商</DialogTitle>
              <DialogDescription>
                请补充引擎token
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="name" className="text-right">
                  名称
                </label>
                <Input
                  id="name"
                  value={newEngine.name}
                  onChange={(e) => setNewEngine({...newEngine, name: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="provider" className="text-right">
                  提供商
                </label>
                <select
                  id="provider"
                  value={newEngine.provider}
                  onChange={handleProviderChange}
                  className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {providers.map((provider) => (
                    <option key={provider.value} value={provider.value}>
                      {provider.label}
                    </option>
                  ))}
                </select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="url" className="text-right">
                  API URL
                </label>
                <Input
                  id="url"
                  value={newEngine.url}
                  onChange={(e) => setNewEngine({...newEngine, url: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="token" className="text-right">
                  API 密钥
                </label>
                <Input
                  id="token"
                  type="password"
                  value={newEngine.token}
                  onChange={(e) => setNewEngine({...newEngine, token: e.target.value})}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <label htmlFor="note" className="text-right">
                  备注
                </label>
                <Input
                  id="note"
                  value={newEngine.note}
                  onChange={(e) => setNewEngine({...newEngine, note: e.target.value})}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={addEngine}>添加</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>


      
      {/* 提供商卡片列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(groupedEngines).map(([provider, providerEngines]) => (
          <Card key={provider} className="overflow-hidden">
            <CardHeader className="bg-gray-50 dark:bg-neutral-800">
              <div className="flex items-center gap-2">
                {/* <img
                  src={`/icons/${provider.toLowerCase()}.png`}
                  alt={provider}
                  className="w-8 h-8"
                  onError={(e) => (e.currentTarget.src = "/icons/default-search.png")}
                /> */}
                <CardTitle>{provider}</CardTitle>
              </div>
              <CardDescription>
                {providerEngines.length} 个配置
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Tabs defaultValue={providerEngines[0].id}>
                <TabsList className="w-full justify-start rounded-none border-b">
                  {providerEngines.map((engine) => (
                    <TabsTrigger
                      key={engine.id}
                      value={engine.id}
                      onClick={() => setSelectedEngine(engine.id)}
                      className="data-[state=active]:bg-gray-100 dark:data-[state=active]:bg-neutral-700"
                    >
                      {engine.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {providerEngines.map((engine) => (
                  <TabsContent key={engine.id} value={engine.id} className="p-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">{engine.name}</h4>
                      <div className="flex items-center gap-2">
                        <a
                          href={engine.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:text-blue-600"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </a>
                        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                setNewEngine({
                                  name: engine.name,
                                  provider: engine.provider,
                                  url: engine.url,
                                  token: engine.token,
                                  note: engine.note || ""
                                });
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>编辑搜索提供商</DialogTitle>
                              <DialogDescription>
                                修改搜索引擎提供商信息
                              </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <div className="grid grid-cols-4 items-center gap-4">
                                <label htmlFor="edit-name" className="text-right">
                                  名称
                                </label>
                                <Input
                                  id="edit-name"
                                  value={newEngine.name}
                                  onChange={(e) => setNewEngine({...newEngine, name: e.target.value})}
                                  className="col-span-3"
                                />
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <label htmlFor="edit-provider" className="text-right">
                                  提供商
                                </label>
                                <Input
                                  id="edit-provider"
                                  value={newEngine.provider}
                                  onChange={(e) => setNewEngine({...newEngine, provider: e.target.value})}
                                  className="col-span-3"
                                />
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <label htmlFor="edit-url" className="text-right">
                                  API URL
                                </label>
                                <Input
                                  id="edit-url"
                                  value={newEngine.url}
                                  onChange={(e) => setNewEngine({...newEngine, url: e.target.value})}
                                  className="col-span-3"
                                />
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <label htmlFor="edit-token" className="text-right">
                                  API 密钥
                                </label>
                                <Input
                                  id="edit-token"
                                  type="password"
                                  value={newEngine.token}
                                  onChange={(e) => setNewEngine({...newEngine, token: e.target.value})}
                                  className="col-span-3"
                                />
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <label htmlFor="edit-note" className="text-right">
                                  备注
                                </label>
                                <Input
                                  id="edit-note"
                                  value={newEngine.note}
                                  onChange={(e) => setNewEngine({...newEngine, note: e.target.value})}
                                  className="col-span-3"
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
                              <Button onClick={updateEngine}>保存</Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">API 密钥</span>
                        <span className="text-sm font-mono">
                          {engine.token.substring(0, 8)}...
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">可用额度</span>
                        <span className="text-sm">{engine.available_credit}</span>
                      </div>
                      {engine.note && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">备注</span>
                          <span className="text-sm">{engine.note}</span>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
            <CardFooter className="bg-gray-50 dark:bg-neutral-800 p-3">
              <div className="w-full flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const engine = providerEngines.find(e => e.id === selectedEngine);
                    if (engine) {
                      setApiKey(engine.token);
                    }
                  }}
                >
                  配置
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>


    </div>
  );
}
