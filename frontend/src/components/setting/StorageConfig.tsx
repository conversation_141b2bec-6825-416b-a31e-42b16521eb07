import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function StorageConfig() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6">
        {/* 数据库配置 */}
        <div className="bg-white dark:bg-neutral-900 rounded-lg p-6 border dark:border-neutral-800">
          <h3 className="text-lg font-medium mb-4">数据库配置</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">数据库类型</label>
              <Select defaultValue="postgresql">
                <SelectTrigger>
                  <SelectValue placeholder="选择数据库类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="postgresql">PostgreSQL</SelectItem>
                  <SelectItem value="mysql">MySQL</SelectItem>
                  <SelectItem value="mongodb">MongoDB</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">连接地址</label>
              <Input placeholder="例如: localhost:5432" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">数据库名称</label>
              <Input placeholder="数据库名称" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">用户名</label>
              <Input />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">密码</label>
              <Input type="password" />
            </div>

            <Button>测试连接</Button>
          </div>
        </div>

        {/* 文件存储配置 */}
        <div className="bg-white dark:bg-neutral-900 rounded-lg p-6 border dark:border-neutral-800">
          <h3 className="text-lg font-medium mb-4">文件存储配置</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">存储类型</label>
              <Select defaultValue="local">
                <SelectTrigger>
                  <SelectValue placeholder="选择存储类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="local">本地存储</SelectItem>
                  <SelectItem value="s3">Amazon S3</SelectItem>
                  <SelectItem value="oss">阿里云 OSS</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">存储路径</label>
              <Input placeholder="本地存储路径或云存储 Endpoint" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Access Key</label>
              <Input />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Secret Key</label>
              <Input type="password" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Bucket 名称</label>
              <Input />
            </div>

            <Button>验证配置</Button>
          </div>
        </div>

                {/* 向量数据库配置 */}
        <div className="bg-white dark:bg-neutral-900 rounded-lg p-6 border dark:border-neutral-800">
          <h3 className="text-lg font-medium mb-4">向量数据库配置</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">数据库类型</label>
              <Select defaultValue="milvus">
                <SelectTrigger>
                  <SelectValue placeholder="选择向量数据库类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="milvus">Milvus</SelectItem>
                  <SelectItem value="qdrant">Qdrant</SelectItem>
                  <SelectItem value="weaviate">Weaviate</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">服务地址</label>
              <Input placeholder="例如: localhost:19530" />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">用户名</label>
              <Input />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">密码</label>
              <Input type="password" />
            </div>

            <Button>测试连接</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
