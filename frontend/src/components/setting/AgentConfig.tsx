import { useState, useEffect } from "react";
import { useAgentStore } from "@/store/agentStore";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export function AgentConfig() {
  const { agents, currentAgent, setCurrentAgent } = useAgentStore();
  const [agentName, setAgentName] = useState("");
  const [config, setConfig] = useState("");

  // 当选中的代理改变时，更新表单数据
  useEffect(() => {
    if (currentAgent && agents[currentAgent]) {
      setAgentName(agents[currentAgent].name);
      setConfig(agents[currentAgent].content);
    }
  }, [currentAgent, agents]);

  return (
    <div className="space-y-6">
      <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(agents).map(([key, agent]) => (
          <div
            key={key}
            className={`p-6 bg-white dark:bg-neutral-900 rounded-lg border ${
              currentAgent === key
                ? "border-blue-500 dark:border-blue-500"
                : "border-gray-200 dark:border-neutral-800"
            } hover:border-blue-500 dark:hover:border-blue-500 cursor-pointer transition-colors`}
            onClick={() => setCurrentAgent(key)}
          >
            <div className="flex flex-col h-full">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                    {agent.name}
                  </h3>
                </div>
              </div>

              <div className="mt-2 flex-1">
                <p className="text-sm text-gray-600 dark:text-neutral-400 line-clamp-3">
                  {agent.content.slice(0, 150)}...
                </p>
              </div>

              <div className="mt-4 flex justify-end">
                <Button variant="outline" size="sm">
                  编辑
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* 添加新代理卡片 */}
        <div className="p-6 bg-white dark:bg-neutral-900 rounded-lg border border-dashed border-gray-200 dark:border-neutral-800 hover:border-blue-500 dark:hover:border-blue-500 cursor-pointer transition-colors">
          <div className="flex flex-col items-center justify-center h-full text-center">
            <svg
              className="size-8 text-gray-400 dark:text-neutral-600 mb-2"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M5 12h14" />
              <path d="M12 5v14" />
            </svg>
            <p className="text-sm text-gray-600 dark:text-neutral-400">
              添加新代理
            </p>
          </div>
        </div>
      </div>

      {/* 当前选中代理的详细设置 */}
      {currentAgent && (
        <div className="bg-white dark:bg-neutral-900 rounded-lg border dark:border-neutral-800 p-6">
          <h3 className="text-lg font-semibold mb-4">
            {agents[currentAgent].name} 设置
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">代理名称</label>
              <Input 
                value={agentName}
                onChange={(e) => setAgentName(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                系统提示词
              </label>
              <Textarea
                value={config}
                onChange={(e) => setConfig(e.target.value)}
                className="min-h-[200px] font-mono text-sm"
              />
            </div>

            <div className="flex justify-end">
              <Button onClick={() => {
                // TODO: 调用 store 的更新方法
                console.log({ name: agentName, content: config });
              }}>
                保存配置
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
