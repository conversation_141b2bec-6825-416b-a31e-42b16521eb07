import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { useUserStore } from "@/store/userStore";

export function UserProfile() {
  const { users, fetchUsers } = useUserStore();
  
  // 修改初始化逻辑
  const [userInfo, setUserInfo] = useState({
    username: "",
    email: "",
    created_at: "",
    location: "IIM@DHU",
    is_admin: false,
    avatar_url: ""
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  // 添加新的 useEffect 来监听 users 变化
  useEffect(() => {
    if (users.length > 0) {
      setUserInfo({
        username: users[0].username,
        email: users[0].email,
        is_admin: users[0].is_admin,
        created_at: users[0].created_at,
        location: "IIM@DHU",
        avatar_url: users[0].avatar_url
      });
    }
  }, [users]);

  return (
    <div className="flex gap-8">
      <div className="flex-[7]">
        <div className="grid grid-cols-1 lg:grid-cols-2 lg:items-start gap-6 md:gap-8 lg:gap-12">
          {/* 左侧头像区域 */}
          <div className="aspect-w-16 aspect-h-6 lg:aspect-h-14 overflow-hidden bg-gray-100 rounded-2xl dark:bg-neutral-800">
            <img 
              className="object-cover rounded-2xl transition-transform duration-500 ease-in-out hover:scale-105"
              src={userInfo.avatar_url}
              alt="User Avatar"
            />
          </div>

          {/* 右侧信息区域 */}
          <div className="space-y-8 lg:space-y-10">
            {/* 基本信息 */}
            <div>
              <h3 className="mb-5 font-semibold text-black dark:text-white">
                基本信息
              </h3>

              <div className="grid gap-4">
                <div className="flex gap-4">
                  <svg className="shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>

                  <div className="grow">
                    <p className="text-sm text-gray-600 dark:text-neutral-400">
                      用户名
                    </p>
                    <Input 
                      className="mt-1"
                      value={userInfo.username}
                      disabled
                      // onChange={(e) => setUserInfo({...userInfo, username: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex gap-4">
                  <svg className="shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21.2 8.4c.5.38.8.97.8 1.6v10a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V10a2 2 0 0 1 .8-1.6l8-6a2 2 0 0 1 2.4 0l8 6Z"></path>
                    <path d="m22 10-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 10"></path>
                  </svg>

                  <div className="grow">
                    <p className="text-sm text-gray-600 dark:text-neutral-400">
                      邮箱
                    </p>
                    <Input 
                      className="mt-1"
                      value={userInfo.email}
                      onChange={(e) => setUserInfo({...userInfo, email: e.target.value})}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 联系方式 */}
            <div>
              <h3 className="mb-5 font-semibold text-black dark:text-white">
                联系方式
              </h3>

              <div className="grid gap-4">
                <div className="flex gap-4">
                <svg className="shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 6v6l4 2"/>
                  </svg>

                  <div className="grow">
                    <p className="text-sm text-gray-600 dark:text-neutral-400">
                      创建时间
                    </p>
                    <Input 
                      className="mt-1"
                      value={userInfo.created_at}
                      disabled
                      // onChange={(e) => setUserInfo({...userInfo, created_at: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex gap-4">
                  <svg className="shrink-0 size-5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>

                  <div className="grow">
                    <p className="text-sm text-gray-600 dark:text-neutral-400">
                      地址
                    </p>
                    <Input 
                      className="mt-1"
                      value={userInfo.location}
                      disabled
                      // onChange={(e) => setUserInfo({...userInfo, location: e.target.value})}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 保存按钮 */}
            <div className="flex justify-end">
              {!users.find(u => u.username === userInfo.username)?.is_active && (
                <Button onClick={async () => {
                  try {
                    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/activate`, {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                      },
                      body: JSON.stringify({
                        username: userInfo.username
                      })
                    });

                    if (response.ok) {
                      // 激活成功后刷新用户列表
                      await fetchUsers();
                    } else {
                      const error = await response.json();
                      console.error('激活失败:', error);
                    }
                  } catch (error) {
                    console.error('激活请求失败:', error);
                  }
                }}>
                  激活
                </Button>
              )}
            </div>
          </div>
      </div>
    </div>
        {/* 用户列表侧边栏 */}
        <div className="hidden xl:block flex-[3] border-l">
          <div className="hidden xl:block w-80 shrink-0 ml-3">
            <div className="sticky top-6 space-y-4">
              <h3 className="font-semibold text-black dark:text-white">
                所有用户
              </h3>

              <div className="space-y-2">
                {users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-neutral-800 cursor-pointer transition-colors"
                    onClick={() => setUserInfo({
                      username: user.username,
                      email: user.email,
                      is_admin: user.is_admin,
                      created_at: user.created_at, // 假设数据
                      location: "IIM@Align", // 假设数据
                      avatar_url: user.avatar_url
                    })}
                  >
                    <img
                      src={user.avatar_url}
                      alt={user.username}
                      className="size-10 rounded-full"
                    />
                    <div className="min-w-0">
                    <p className={`font-medium text-sm truncate ${
                        user.is_admin ? 'font-bold text-red-800 dark:text-red-400' : ''
                      }`}>
                        {user.username}{user.is_admin ? ' *' : ''}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-neutral-400 truncate">
                        {new Date(user.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="ml-auto">
                      <div className={`w-2 h-2 rounded-full ${user.is_active ? 'bg-green-500' : 'bg-gray-300'}`} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

    </div>
  );
}