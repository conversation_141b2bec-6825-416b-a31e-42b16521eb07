import { useState, useEffect } from "react";
import { useAuth } from "@/utils/AuthContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, FileText, Image, History } from "lucide-react";

interface Stats {
  totalChats: number;
  totalDocuments: number;
  totalDiagrams: number;
  recentActivities: Activity[];
}

interface Activity {
  id: string;
  type: 'chat' | 'document' | 'diagram';
  action: string;
  timestamp: string;
  details: string;
}

export function Dashboard() {
  const { user } = useAuth();
  const userId = user?.id;
  // Remove the first useEffect (lines 28-46) and keep only the mock data useEffect

  // Update the stats state initialization to include an empty array
  const [stats, setStats] = useState<Stats>({
    totalChats: 0,
    totalDocuments: 0,
    totalDiagrams: 0,
    recentActivities: []  // Ensure this is initialized as an empty array
  });
  const [loading, setLoading] = useState(true);

  // useEffect(() => {
  //   const fetchStats = async () => {
  //     try {
  //       const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  //       const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/${userId}/stats`, {
  //         headers: {
  //           'Authorization': `Bearer ${token}`
  //         }
  //       });
  //       const data = await response.json();
  //       setStats(data);
  //     } catch (error) {
  //       console.error('获取统计数据失败:', error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };

  //   if (userId) {
  //     fetchStats();
  //   }
  // }, [userId]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockData: Stats = {
          totalChats: 128,
          totalDocuments: 45,
          totalDiagrams: 67,
          recentActivities: [
            {
              id: '1',
              type: 'chat',
              action: '新建对话',
              details: '与 AI 助手讨论论文解读',
              timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString()
            },
            {
              id: '2',
              type: 'document',
              action: '上传文档',
              details: 'Large Language Models 研究论文.pdf',
              timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
            },
            {
              id: '3',
              type: 'diagram',
              action: '生成图表',
              details: 'AI 系统架构图',
              timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString()
            },
            {
              id: '4',
              type: 'chat',
              action: '继续对话',
              details: '探讨深度学习最新进展',
              timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString()
            },
            {
              id: '5',
              type: 'document',
              action: '导入文档',
              details: '深度学习框架对比分析.docx',
              timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString()
            },
            {
              id: '6',
              type: 'diagram',
              action: '更新图表',
              details: '神经网络结构示意图',
              timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString()
            },
            {
              id: '7',
              type: 'chat',
              action: '创建会话',
              details: '讨论强化学习应用场景',
              timestamp: new Date(Date.now() - 1000 * 60 * 300).toISOString()
            },
            {
              id: '8',
              type: 'document',
              action: '分享文档',
              details: 'Transformer架构详解.pdf',
              timestamp: new Date(Date.now() - 1000 * 60 * 360).toISOString()
            }
          ]
        };

        setStats(mockData);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchStats();
    }
  }, [userId]);

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总对话数</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalChats}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">文档数量</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDocuments}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">图表数量</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDiagrams}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">最近活动</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats?.recentActivities?.map((activity) => (
              <div
                key={activity.id}
                className="flex items-center space-x-4 rounded-lg border p-4"
              >
                <div className="flex-shrink-0">
                  {activity.type === 'chat' && <MessageSquare className="h-5 w-5" />}
                  {activity.type === 'document' && <FileText className="h-5 w-5" />}
                  {activity.type === 'diagram' && <Image className="h-5 w-5" />}
                </div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">{activity.action}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{activity.details}</p>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(activity.timestamp).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}