import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectTrigger,
  SelectItem,
  SelectContent,
  SelectValue,
} from "@/components/ui/select";
import { ModelService } from "@/types";

interface AddCustomModelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedService: ModelService | null;
  customModelForm: {
    name: string;
    description: string;
    temperature: number;
    maxTokens: number;
    agentId: string;
  };
  setCustomModelForm: (form: {
    name: string;
    description: string;
    temperature: number;
    maxTokens: number;
    agentId: string;
  }) => void;
  onSubmit: () => void;
}

// 添加 useAgentStore 导入
import { useAgentStore } from "@/store/agentStore";

export function AddCustomModelDialog({
  open,
  onOpenChange,
  selectedService,
  customModelForm,
  setCustomModelForm,
  onSubmit,
}: AddCustomModelDialogProps) {
  // 获取 agents 数据
  const { agents } = useAgentStore();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            基于{selectedService?.models?.[0]?.name}定制新模型
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">模型名称</label>
            <Input
              value={customModelForm.name}
              onChange={(e) =>
                setCustomModelForm({
                  ...customModelForm,
                  name: e.target.value,
                })
              }
              placeholder="输入模型名称"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">模型描述</label>
            <Input
              value={customModelForm.description}
              onChange={(e) =>
                setCustomModelForm({
                  ...customModelForm,
                  description: e.target.value,
                })
              }
              placeholder="输入模型描述"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              基础模型信息
            </label>
            <div className="p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg">
              <div className="text-sm font-medium">
                {selectedService?.models?.[0]?.name}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {selectedService?.name}
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              温度 ({customModelForm.temperature})
            </label>
            <Input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={customModelForm.temperature}
              onChange={(e) =>
                setCustomModelForm({
                  ...customModelForm,
                  temperature: parseFloat(e.target.value),
                })
              }
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">最大 Token</label>
            <Input
              type="number"
              min="1"
              max="8192"
              value={customModelForm.maxTokens}
              onChange={(e) =>
                setCustomModelForm({
                  ...customModelForm,
                  maxTokens: parseInt(e.target.value) || 2048,
                })
              }
              placeholder="输入最大 Token 数"
            />
          </div>
      
          <div>
            <label className="block text-sm font-medium mb-1">
              Agent 提示词
            </label>
            <Select
              value={customModelForm.agentId}
              onValueChange={(value) =>
                setCustomModelForm({
                  ...customModelForm,
                  agentId: value,
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择 Agent" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(agents).map(([id, agent]) => (
                  <SelectItem key={id} value={id}>
                    {agent.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={onSubmit}>定制</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
