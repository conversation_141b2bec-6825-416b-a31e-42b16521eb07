import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, ActivitySquare, Trash2 } from "lucide-react"; // 添加图标导入
import { checkLLMHealth } from "@/utils/healthCheck"; // 添加导入
import { useState, useEffect } from "react"; // 添加 useState 导入
import {
  useModelConfigStore,
  ModelType,
  CustomModel,
} from "@/store/modelConfigStore";
import { AddCustomModelDialog } from "./AddCustomModelDialog";
import { AddBaseModelDialog } from "./AddBaseModelDialog";

interface ServiceConfigProps {
  category: ServiceCategory;
}

// 首先在组件顶部从 store 中解构出 removeConfig
export function ServiceConfigPanel({ category }: ServiceConfigProps) {
  // 在组件顶部从 store 中添加 customModels
  const { configs, addConfig, removeConfig, addCustomModel, customModels } = useModelConfigStore();

  const [localCategory, setLocalCategory] = useState(category);
  const [loadingChecks, setLoadingChecks] = useState<Record<string, boolean>>(
    {}
  );
  const [healthStatus, setHealthStatus] = useState<
    Record<string, { status: "ok" | "error"; latency: number }>
  >({});
  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [selectedService, setSelectedService] = useState<
    (typeof localCategory.services)[0] | null
  >(null);
  const [customModelForm, setCustomModelForm] = useState({
    name: "",
    description: "",
    temperature: 0.7,
    maxTokens: 2048,
    agentId: "",
  });

  // Add health check handler
  const handleHealthCheck = async (serviceId: string, baseUrl: string) => {
    try {
      setLoadingChecks((prev) => ({ ...prev, [serviceId]: true }));
      const result = await checkLLMHealth({ serviceId, baseUrl });
      setHealthStatus((prev) => ({
        ...prev,
        [serviceId]: result,
      }));
    } catch (error) {
      console.error("Health check failed:", error);
    } finally {
      setLoadingChecks((prev) => ({ ...prev, [serviceId]: false }));
    }
  };

  // 添加缺失的状态声明
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newService, setNewService] = useState({
    name: "",
    baseUrl: "",
    apiKey: "",
    model: "",
  });

  useEffect(() => {
    if (category?.id) {
      setLocalCategory({
        ...category,
        services: configs
          .filter((config) => config.type === category.id)
          .map((config) => ({
            id: `llm-${config.model}`,
            type: config.type,
            name: config.name, // 直接使用配置中的服务名称
            icon: "Q",
            status: true,
            baseUrl: config.host,
            apiKey: config.apiKey,
            models: [{ name: config.model, isSystem: true }],
          })),
      });
    }
  }, [configs, category]);

  const handleAddService = () => {
    if (!newService.name) {
      // 如果没有输入服务名称，不允许添加
      return;
    }

    // 添加到 store 中
    addConfig({
      host: newService.baseUrl,
      model: newService.model,
      name: newService.name, // 只使用用户输入的服务名称
      apiKey: newService.apiKey,
      type: category?.id as ModelType,
    });

    // 重置表单和关闭对话框
    setShowAddDialog(false);
    setNewService({ name: "", baseUrl: "", apiKey: "", model: "" });
  };

  const handleRemoveService = (serviceId: string) => {
    const config = configs.find((c) => `llm-${c.model}` === serviceId);
    if (config) {
      // 检查是否有关联的定制模型
      const linkedCustomModels = customModels.filter(
        (model) => model.baseService === config.name
      );
    
      if (linkedCustomModels.length > 0) {
        alert(`无法删除该服务：存在 ${linkedCustomModels.length} 个关联的定制模型。请先删除相关的定制模型。`);
        return;
      }
    
      removeConfig(config);
    }
  };

  const handleCustomModel = (service: (typeof localCategory.services)[0]) => {
    setSelectedService(service);
    setCustomModelForm({
      name: "",
      description: "",
      temperature: 0.7,
      maxTokens: 2048,
      agentId: "",
    });
    setShowCustomDialog(true);
  };

  const handleSubmitCustomModel = () => {
    if (!selectedService || !customModelForm.name) return;
    
    // 创建新的定制模型
    const newCustomModel: CustomModel = {
      id: `custom-${Date.now()}`,
      type: category.id as ModelType,
      name: customModelForm.name,
      description: customModelForm.description,
      baseModel: selectedService.models?.[0]?.name || '',
      baseService: selectedService.name,
      temperature: customModelForm.temperature,
      maxTokens: customModelForm.maxTokens,
      agentId: customModelForm.agentId,
      status: "training",
      createdAt: new Date().toISOString()
    };

    // 添加到 store
    addCustomModel(newCustomModel);

    // 重置表单并关闭对话框
    setCustomModelForm({
      name: "",
      description: "",
      temperature: 0.7,
      maxTokens: 2048,
      agentId: "",
    });
    setShowCustomDialog(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {category?.name || "未命名类别"}
            </h2>
            <p className="text-sm text-gray-500 dark:text-neutral-400">
              {category?.description || "暂无描述"}
            </p>
          </div>
          <Button
            onClick={() => setShowAddDialog(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {/* 添加 */}
          </Button>
        </div>

        {/* 添加服务对话框 */}
        <AddBaseModelDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          categoryName={category.name}
          newService={newService}
          setNewService={setNewService}
          onSubmit={handleAddService}
        />
      </div>

      <div className="grid grid-cols-1 gap-6 w-full">
        {localCategory.services.map((service) => (
          <div
            key={service.id}
            className="bg-white dark:bg-neutral-900 rounded-lg p-6 border dark:border-neutral-800"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 size-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 dark:text-blue-300 font-medium text-lg">
                    {service.icon}
                  </span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    {service.name}
                  </h3>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  title="健康检查"
                  onClick={() =>
                    service.baseUrl &&
                    handleHealthCheck(service.id, service.baseUrl)
                  }
                >
                  <ActivitySquare
                    className={`h-4 w-4 ${
                      loadingChecks[service.id] ? "animate-spin" : ""
                    }`}
                  />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  title="删除服务"
                  onClick={() => handleRemoveService(service.id)}
                >
                  <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  title="定制新模型"
                  onClick={() => handleCustomModel(service)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {service.apiKey !== undefined && (
                <div>
                  <label className="block text-sm font-medium mb-1">
                    API 密钥
                  </label>
                  <Input type="password" value={service.apiKey} readOnly />
                </div>
              )}

              {service.baseUrl !== undefined && (
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <label className="block text-sm font-medium">
                      API 地址
                    </label>
                    {healthStatus[service.id] && (
                      <div className="flex items-center gap-2">
                        <span
                          className={`inline-block w-2 h-2 rounded-full ${
                            healthStatus[service.id].status === "ok"
                              ? "bg-green-500"
                              : "bg-red-500"
                          }`}
                        />
                        <span className="text-sm text-gray-500">
                          {`${healthStatus[service.id].latency}ms`}
                        </span>
                      </div>
                    )}
                  </div>
                  <label>{service.baseUrl}</label> 
                </div>
              )}

              {service.models && service.models.length > 0 && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    基础模型：
                  </label>
                  <div>{service.models[0].name}</div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      <AddCustomModelDialog
        open={showCustomDialog}
        onOpenChange={setShowCustomDialog}
        selectedService={selectedService}
        customModelForm={customModelForm}
        setCustomModelForm={setCustomModelForm}
        onSubmit={handleSubmitCustomModel}
      />
    {/* </div> */}
    </div>
  );
}
