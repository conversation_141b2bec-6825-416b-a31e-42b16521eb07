import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface AddBaseModelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  categoryName: string;
  newService: {
    name: string;
    baseUrl: string;
    apiKey: string;
    model: string;
  };
  setNewService: (service: {
    name: string;
    baseUrl: string;
    apiKey: string;
    model: string;
  }) => void;
  onSubmit: () => void;
}

export function AddBaseModelDialog({
  open,
  onOpenChange,
  categoryName,
  newService,
  setNewService,
  onSubmit,
}: AddBaseModelDialogProps) {
  const [models, setModels] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchModels = async () => {
    if (!newService.baseUrl || !newService.apiKey) return;
    
    setLoading(true);
    try {
      const response = await fetch(`${newService.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${newService.apiKey}`
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch models');
      
      const data = await response.json();
      setModels(data.data.map((model: any) => model.id));
    } catch (error) {
      console.error('Error fetching models:', error);
    } finally {
      setLoading(false);
    }
  };

  // 当 API 地址或密钥改变且都有值时，获取模型列表
  useEffect(() => {
    if (newService.baseUrl && newService.apiKey) {
      fetchModels();
    }
  }, [newService.baseUrl, newService.apiKey]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>添加{categoryName}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">服务名称</label>
            <Input
              value={newService.name}
              onChange={(e) =>
                setNewService({ ...newService, name: e.target.value })
              }
              placeholder="输入服务名称"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">API 地址</label>
            <Input
              value={newService.baseUrl}
              onChange={(e) =>
                setNewService({ ...newService, baseUrl: e.target.value })
              }
              placeholder="http://127.0.0.1:3033/v1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">API 密钥</label>
            <Input
              type="password"
              value={newService.apiKey}
              onChange={(e) =>
                setNewService({ ...newService, apiKey: e.target.value })
              }
              placeholder="sk-1234"
            />
          </div>
          {categoryName !== 'OCR服务' && categoryName !== '文档转换' && (
            <div>
              <label className="block text-sm font-medium mb-1">默认模型</label>
              <Select
                value={newService.model}
                onValueChange={(value) =>
                  setNewService({ ...newService, model: value })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={loading ? "加载中..." : "选择模型"} />
                </SelectTrigger>
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem key={model} value={model}>
                      {model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={onSubmit}>添加</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}