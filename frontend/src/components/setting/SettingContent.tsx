import { useEffect } from "react";

import { UserProfile } from "./UserProfile";
import { TeamManagement } from "./TeamManagement";
import { SearchEngineConfig } from "./SearchEngineConfig";
import { StorageConfig } from "./StorageConfig";
import { AgentConfig } from "./AgentConfig";
import { About } from "./About";
import { Dashboard } from "./Dashboard";
import LLMFactory from "./LLMProvider/page";

interface SettingContentProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export function SettingContent({ activeTab, setActiveTab }: SettingContentProps) {
  useEffect(() => {
    if (!activeTab) {
      setActiveTab("Dashboard");
    }
  }, []);

  const renderContent = () => {
    switch (activeTab) {
      case "Dashboard":
        return <Dashboard />;
      case "用户管理":
        return <UserProfile />;
      case "团队管理":
        return <TeamManagement />;
      case "模型服务":
        return <LLMFactory />;
      case "搜索引擎":
        return <SearchEngineConfig />;
      case "存储设置":
        return <StorageConfig />;
      case "Agents":
        return <AgentConfig />;
      case "About":
        return <About />;
      default:
        return <div>请选择一个设置项</div>;
    }
  };

  return (
    <div className="p-6 h-full lg:pl-72">
      <div className="mx-auto h-full flex flex-col">
        <h2 className="text-2xl font-semibold mb-6">{activeTab}</h2>
        <div className="bg-white dark:bg-neutral-800 rounded-lg p-6 shadow flex-1">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}