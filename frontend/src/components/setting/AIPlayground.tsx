import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { CustomModel } from "@/store/modelConfigStore";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface AIPlaygroundProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  model: CustomModel;
  systemPrompt?: string;
  baseUrl?: string;
  apiKey?: string;  // 添加 apiKey 属性
}

export function AIPlayground({
  open,
  onOpenChange,
  model,
  systemPrompt,
  baseUrl,
  apiKey,  // 添加 apiKey 参数
}: AIPlaygroundProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!input.trim() || loading || !baseUrl) return;

    const newMessage: { role: 'user' | 'assistant', content: string } = { role: 'user', content: input };
    setMessages(prev => [...prev, newMessage]);
    setInput('');
    setLoading(true);

    try {
      const response = await fetch(`${baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,  // 使用传入的 apiKey
        },
        body: JSON.stringify({
          messages: [
            { role: 'system', content: systemPrompt },
            ...messages,
            newMessage
          ],
          model: model.baseModel,
          temperature: model.temperature,
          max_tokens: model.maxTokens,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: data.choices?.[0]?.message?.content || '抱歉，我遇到了一些问题。' 
      }]);
    } catch (error) {
      console.error('Chat error:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: '抱歉，请求失败了。请检查网络连接或稍后重试。' 
      }]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange} side="right">
      <SheetContent className="w-[600px] sm:w-[540px] flex flex-col">
        <SheetHeader>
          <SheetTitle>Playground - {model.name}</SheetTitle>
        </SheetHeader>
        <div className="flex-1 mt-6 flex flex-col h-[calc(100vh-180px)]">
          <div className="space-y-4 flex-1 flex flex-col">
            <div>
              <label className="block text-sm font-medium mb-2">系统提示词</label>
              <div className="p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {(systemPrompt || "暂无提示词").slice(0, 20)}
                  {(systemPrompt?.length || 0) > 20 && "..."}
                </p>
              </div>
            </div>
            <div className="flex-1 flex flex-col min-h-0"> {/* 添加 min-h-0 */}
              <label className="block text-sm font-medium mb-2">对话测试</label>
              <ScrollArea className="h-[400px] bg-gray-50 dark:bg-neutral-800 rounded-lg p-4"> {/* 设置固定高度 */}
                <div className="space-y-4"> {/* 添加内容容器 */}
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={`${
                        message.role === 'user' ? 'text-blue-600' : 'text-gray-800'
                      } dark:text-gray-200`}
                    >
                      <div className="font-medium mb-1">
                        {message.role === 'user' ? '你' : 'AI'}:
                      </div>
                      <div className="text-sm">{message.content}</div>
                    </div>
                  ))}
                  {loading && (
                    <div className="text-sm text-gray-500">AI 正在思考...</div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
          <div className="mt-4 flex gap-2">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSubmit()}
              placeholder="输入问题..."
              disabled={loading}
            />
            <Button
              size="icon"
              onClick={handleSubmit}
              disabled={loading || !input.trim()}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}