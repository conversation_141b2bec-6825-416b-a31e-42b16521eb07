import { Button } from "@/components/ui/button";
import { CustomModel, useModelConfigStore } from "@/store/modelConfigStore";
import { PlayCircle, ArrowLeft, Trash2 } from "lucide-react";
import { useState } from "react";
import { AIPlayground } from "./AIPlayground";

interface CustomModelDetailProps {
  model: CustomModel;
  onClose: () => void;
}

import { useAgentStore } from "@/store/agentStore";

export function CustomModelDetail({ model, onClose }: CustomModelDetailProps) {
  const { agents } = useAgentStore();
  const { removeCustomModel, configs } = useModelConfigStore();
  const [showPlayground, setShowPlayground] = useState(false);
  const baseServiceConfig = configs.find(config => 
    config.name === model.baseService
  );

  
  const handleDelete = () => {
    if (window.confirm('确定要删除这个定制模型吗？')) {
      removeCustomModel(model);
      onClose();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {model.name}
          </h2>
          <p className="text-sm text-gray-500 dark:text-neutral-400">
            基于 {model.description} 定制
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            title="在 Playground 中打开"
            onClick={() => setShowPlayground(true)}
          >
            <PlayCircle className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            title="删除模型"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            title="返回"
            onClick={onClose}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">基础模型</label>
          <div className="p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg">
            <div className="text-sm font-medium">{model.baseModel}</div>
            <div className="text-xs text-gray-500 mt-1">{model.baseService}</div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Agent 提示词</label>
          <div className="p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg">
            <div className="text-sm font-medium">
              {agents[model.agentId]?.name || "未指定"}
            </div>
            <div className="text-xs text-gray-500 mt-1 line-clamp-2">
              {agents[model.agentId]?.content || "暂无提示词"}
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">模型参数</label>
          <div className="bg-gray-50 dark:bg-neutral-900 rounded-lg p-4 space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">温度</span>
              <span className="text-sm font-medium">{model.temperature}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">最大 Token</span>
              <span className="text-sm font-medium">{model.maxTokens}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">状态</span>
              <span className={`text-sm font-medium ${
                model.status === 'ready' ? 'text-green-500' :
                model.status === 'training' ? 'text-yellow-500' :
                'text-red-500'
              }`}>
                {model.status === 'ready' ? '已就绪' :
                 model.status === 'training' ? '训练中' :
                 '失败'}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500">创建时间</span>
              <span className="text-sm font-medium">
                {new Date(model.createdAt).toLocaleString('zh-CN')}
              </span>
            </div>
          </div>
        </div>
      </div>
      <AIPlayground
        open={showPlayground}
        onOpenChange={setShowPlayground}
        model={model}
        systemPrompt={agents[model.agentId]?.content}
        baseUrl={baseServiceConfig?.host}
        apiKey={baseServiceConfig?.apiKey}  // 添加 apiKey
      />
    </div>
  );
}