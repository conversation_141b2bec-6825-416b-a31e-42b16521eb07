"use client";

import { <PERSON>, Settings, Plus, Trash2, <PERSON>, <PERSON>Off, RefreshCw } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useState, useEffect, useRef } from "react";
import { useLLMStore } from '@/store/llmStore';
import { AddProviderDialog } from './AddProviderDialog';
import { Input } from "@/components/ui/input";
import { ModelManageDialog } from './ModelManageDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function LLMFactory() {
  const { providers, selectedProvider, setSelectedProvider, toggleProvider, updateProvider, deleteProvider, fetchProviders } = useLLMStore();
  const [baseUrl, setBaseUrl] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 当选中的提供商变化时，更新输入框的值
  useEffect(() => {
    if (selectedProvider) {
      setBaseUrl(selectedProvider.baseUrl || '');
      setApiKey(selectedProvider.apiKey || '');
    }
  }, [selectedProvider]);

  // 处理输入变化，使用防抖
  const handleBaseUrlChange = (value: string) => {
    setBaseUrl(value);
    
    // 清除之前的定时器
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // 设置新的定时器，延迟更新
    updateTimeoutRef.current = setTimeout(() => {
      if (selectedProvider) {
        updateProvider(selectedProvider.id, { baseUrl: value });
      }
    }, 500); // 500毫秒的延迟
  };

  // 处理 API 密钥变化
  const handleApiKeyChange = (value: string) => {
    setApiKey(value);
    
    // 清除之前的定时器
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // 设置新的定时器，延迟更新
    updateTimeoutRef.current = setTimeout(() => {
      if (selectedProvider) {
        updateProvider(selectedProvider.id, { apiKey: value });
      }
    }, 500); // 500毫秒的延迟
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const handleRemoveModel = async (modelId: string) => {
    if (!selectedProvider) return;

    const updatedModels = selectedProvider.models.filter(model => model.id !== modelId);

    // 更新 provider 并获取更新后的数据
    await updateProvider(selectedProvider.id, {
      ...selectedProvider,
      models: updatedModels
    });

    // 强制重新选择当前 provider 以刷新界面
    setSelectedProvider({
      ...selectedProvider,
      models: updatedModels
    });
  };
  
  // 处理删除提供商
  const handleDeleteProvider = async () => {
    if (!selectedProvider) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      // 调用删除函数
      const success = await deleteProvider(selectedProvider.id);

      if (success) {
        // 刷新提供商列表
        await fetchProviders();
      } else {
        setDeleteError('删除提供商失败，请重试');
      }
    } catch (error) {
      console.error('删除提供商时出错:', error);
      setDeleteError('删除提供商时出错: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsDeleting(false);
    }
  };

  // 渲染提供商详情
  const renderProviderDetails = () => {
    if (!selectedProvider) return null;
    
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-2">
            <h1 className="text-xl font-medium">{selectedProvider.name}</h1>
            <Label htmlFor={`provider-${selectedProvider.id}`} className="text-sm text-gray-500">
              {selectedProvider.enabled ? '已启用' : '已禁用'}
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id={`provider-${selectedProvider.id}`}
              checked={selectedProvider.enabled || false}
              onCheckedChange={() => toggleProvider(selectedProvider.id)}
            />

            {/* 删除提供商按钮 */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <button
                  className="ml-2 p-2 text-gray-500 hover:text-red-500 rounded-md hover:bg-gray-100"
                  title="删除提供商"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认删除</AlertDialogTitle>
                  <AlertDialogDescription>
                    您确定要删除提供商 "{selectedProvider.name}" 及其所有模型吗？此操作无法撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                {deleteError && (
                  <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                    {deleteError}
                  </div>
                )}
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={(e) => {
                      e.preventDefault();
                      handleDeleteProvider();
                    }}
                    disabled={isDeleting}
                    className="bg-red-500 hover:bg-red-600 text-white"
                  >
                    {isDeleting ? '删除中...' : '确认删除'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-sm font-medium mb-2">API 密钥</h2>
            <div className="flex">
              <input
                type={showApiKey ? "text" : "password"}
                value={showApiKey ? apiKey : "••••••••••••"}
                onChange={(e) => handleApiKeyChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg bg-white"
                readOnly={!showApiKey}
              />
              <button
                className="ml-2 px-3 py-2 border border-gray-200 rounded-lg text-sm flex items-center"
                onClick={() => setShowApiKey(!showApiKey)}
                title={showApiKey ? "隐藏 API 密钥" : "显示 API 密钥"}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div>
            <h2 className="text-sm font-medium mb-2">API 地址</h2>
            <Input
              value={baseUrl}
              onChange={(e) => handleBaseUrlChange(e.target.value)}
              className="w-full"
            />
            <div className="mt-1 flex items-center text-xs text-gray-500">
              <span>{baseUrl}/v1/chat/completions</span>
              <div className="flex-1"></div>
              <span>请确保您的IP已在白名单内</span>
            </div>
          </div>

          <div>
            <h2 className="text-sm font-medium mb-2 flex items-center">
              模型
              <Search className="ml-2 h-4 w-4 text-gray-400" />
            </h2>

            <div className="space-y-2">
              {selectedProvider.models.map((model) => (
                <div
                  key={model.id}
                  className="flex items-center space-x-2 p-3 hover:bg-gray-50 rounded-lg border border-gray-100"
                >
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center mr-3">
                        <span className="text-purple-600 font-medium">
                          {model.id.split('-')[0][0].toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium">
                          {model.id.split('-').join(' ')}
                        </div>
                        <span className="text-xs text-gray-500">
                          {model.id}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    {model.contextWindow && (
                      <span className="px-2 py-1 bg-gray-50 rounded-md text-xs text-gray-600">
                        {model.contextWindow}k tokens
                      </span>
                    )}
                    <button
                      onClick={() => handleRemoveModel(model.id)}
                      className="text-gray-400 hover:text-red-500 transition-colors"
                      title="移除模型"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex space-x-2">
            <ModelManageDialog />
            <button
              className="px-4 py-2 border border-gray-200 rounded-lg text-sm flex items-center"
              onClick={() => fetchProviders()}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              刷新
            </button>
            <button className="px-4 py-2 border border-gray-200 rounded-lg text-sm flex items-center">
              <Plus className="h-4 w-4 mr-1" />
              添加
            </button>
          </div>
        </div>
      </>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-2rem)] text-center">
        <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
          <Settings className="h-8 w-8 text-gray-400" />
        </div>
        <h2 className="text-xl font-medium mb-2">请选择或添加一个提供商</h2>
        <p className="text-gray-500 mb-6 max-w-md">
          从左侧列表选择一个现有的提供商，或点击底部的"添加"按钮创建一个新的提供商。
        </p>
        <AddProviderDialog />
      </div>
    );
  };

  return (
    <div className="flex h-full bg-gray-50 dark:bg-neutral-900">
      {/* Left Sidebar */}
      <div className="w-64 border-r border-gray-200 bg-white overflow-y-auto relative">
        <div className="p-4">
          <div className="relative">
            <input
              type="text"
              placeholder="搜索模型平台"
              className="w-full pl-8 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-gray-300"
            />
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
          </div>
        </div>

        <div className="space-y-1 pb-16">
          {providers.map((provider) => (
            <div
              key={provider.id}
              className={`flex items-center justify-between px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                provider.id === selectedProvider?.id ? "bg-gray-100" : ""
              }`}
              onClick={async () => {
                // 先刷新数据，然后再切换提供商
                await fetchProviders();
                // 从刷新后的数据中找到对应的提供商
                const refreshedProviders = useLLMStore.getState().providers;
                const refreshedProvider = refreshedProviders.find(p => p.id === provider.id);
                if (refreshedProvider) {
                  setSelectedProvider(refreshedProvider);
                } else {
                  // 如果找不到刷新后的提供商（可能已被删除），使用原始数据
                  setSelectedProvider(provider);
                }
              }}
            >
              <div className="flex items-center">
                <div className={`w-6 h-6 rounded flex items-center justify-center text-white text-xs ${provider.color}`}>
                  {provider.icon}
                </div>
                <span className="ml-3 text-sm">{provider.name}</span>
              </div>
              <svg
                viewBox="0 0 24 24"
                className={`w-4 h-4 ${provider.enabled ? 'text-green-500' : 'text-gray-300'}`}
                fill="currentColor"
              >
                <circle cx="12" cy="12" r="4" />
              </svg>
            </div>
          ))}
        </div>

        <div className="absolute bottom-4 left-4 right-4 flex space-x-2">
          <AddProviderDialog />
        </div>
      </div>

      {/* Right Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {selectedProvider ? renderProviderDetails() : renderEmptyState()}
        </div>
      </div>
    </div>
  );
}
