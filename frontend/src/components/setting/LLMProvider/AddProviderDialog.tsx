import { Plus } from "lucide-react";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const providerTypes = [
  { id: 'openai', name: 'OpenAI' },
  { id: 'ollama', name: '<PERSON><PERSON><PERSON>' },
  { id: 'lm_studio', name: 'LM Studio' }
];

import { useLLMStore } from '@/store/llmStore';
import { Button } from "@/components/ui/button";

export function AddProviderDialog() {
  const { addProvider } = useLLMStore();
  const [open, setOpen] = useState(false);
  const [newProvider, setNewProvider] = useState({ name: '', type: '' });

  const handleSubmit = () => {
    if (!newProvider.name || !newProvider.type) return;
    
    addProvider({
      name: newProvider.name,
      type: newProvider.type,
      baseUrl: '',
    });
    
    setNewProvider({ name: '', type: '' });
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button className="flex items-center justify-center px-4 py-2 border border-gray-200 rounded-lg text-sm text-gray-600 hover:bg-gray-50">
          <Plus className="h-4 w-4 mr-2" />
          添加
        </button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>添加新的供应商</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label>供应商名称</Label>
            <Input
              placeholder="请输入供应商名称"
              value={newProvider.name}
              onChange={(e) => setNewProvider({ ...newProvider, name: e.target.value })}
            />
          </div>
          <div className="space-y-2">
            <Label>供应商类型</Label>
            <Select
              value={newProvider.type}
              onValueChange={(value) => setNewProvider({ ...newProvider, type: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择供应商类型" />
              </SelectTrigger>
              <SelectContent>
                {providerTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSubmit} disabled={!newProvider.name || !newProvider.type}>
              确定
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}