import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useLLMStore } from '@/store/llmStore';
import API from '@/config/api';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Model {
  id: string;
  name: string;
  contextWindow?: number;
}

// 添加 Props 接口，接收表单数据
interface ModelManageDialogProps {
  formData?: {
    baseUrl: string;
    apiKey: string;
    name: string;
    provider: string;
    modelType: string;
  };
  onSave?: (models: Model[]) => void;
}

export function ModelManageDialog({ formData, onSave }: ModelManageDialogProps) {
  const { selectedProvider, updateProvider, setSelectedProvider, addProvider } = useLLMStore();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // 添加表单状态
  const [apiBaseUrl, setApiBaseUrl] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [providerName, setProviderName] = useState('');
  // 修改 modelType 的类型
  const [modelType, setModelType] = useState<'openai' | 'deepseek' | 'qwen'>('openai');

  // 当对话框打开或关闭时，初始化表单数据
  useEffect(() => {
    if (open) {
      // 重置状态
      setError(null);
      setSuccessMessage(null);

      // 优先使用传入的表单数据
      if (formData) {
        setApiBaseUrl(formData.baseUrl || '');
        setApiKey(formData.apiKey || '');
        setProviderName(formData.name || '');
        // 确保 modelType 是有效的类型
        setModelType((formData.modelType as 'openai' | 'deepseek' | 'qwen') || 'openai');
      }
      // 如果没有传入表单数据，则使用选中的提供商数据
      else if (selectedProvider) {
        setApiBaseUrl(selectedProvider.baseUrl || '');
        setApiKey(selectedProvider.apiKey || '');
        setProviderName(selectedProvider.name || '');
        setModelType(selectedProvider.type || 'openai');
      }

      // 如果有 API 地址，则获取模型列表
      if (apiBaseUrl) {
        fetchModels();
      }
    }
  }, [open, formData, selectedProvider]);

  // 获取模型列表
  const fetchModels = async () => {
    if (!apiBaseUrl) {
      setError('请先设置 API 地址');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 构建API URL，确保移除末尾的斜杠
      const baseUrl = apiBaseUrl.trim().replace(/\/$/, '');

      // 尝试两种可能的API路径格式
      // 1. 标准OpenAI格式: /v1/models
      // 2. LiteLLM可能使用的格式: /models
      const apiUrl = `${baseUrl}/v1/models`;
      console.log('请求模型列表URL:', apiUrl);

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
          'Content-Type': 'application/json'
        }
      });

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      console.log('响应Content-Type:', contentType);

      if (!response.ok) {
        // 尝试获取错误响应文本以便调试
        const errorText = await response.text();
        console.error('API错误响应:', errorText);

        // 检查是否返回了HTML而不是JSON
        if (contentType && contentType.includes('text/html')) {
          console.log('收到HTML响应而不是JSON，尝试备用API路径');

          // 尝试备用路径 - LiteLLM可能使用不同的路径格式
          const fallbackUrl = `${baseUrl}/models`;
          console.log('尝试备用URL:', fallbackUrl);

          const fallbackResponse = await fetch(fallbackUrl, {
            headers: {
              'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
              'Content-Type': 'application/json'
            }
          });

          if (!fallbackResponse.ok) {
            const fallbackErrorText = await fallbackResponse.text();
            console.error('备用API错误响应:', fallbackErrorText);
            throw new Error(`获取模型列表失败: ${fallbackResponse.status} ${fallbackResponse.statusText}`);
          }

          // 尝试解析备用响应
          let fallbackData;
          try {
            fallbackData = await fallbackResponse.json();
            console.log('备用API返回数据:', fallbackData);

            // 处理备用响应数据
            if (!fallbackData.data || !Array.isArray(fallbackData.data)) {
              throw new Error('API返回格式不符合预期');
            }
          } catch (jsonError) {
            console.error('备用API JSON解析错误:', jsonError);

            // 尝试第三种格式 - LiteLLM专用格式
            console.log('尝试LiteLLM专用格式');
            const litellmUrl = `${baseUrl}/router/models`;
            console.log('尝试LiteLLM专用URL:', litellmUrl);

            const litellmResponse = await fetch(litellmUrl, {
              headers: {
                'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
                'Content-Type': 'application/json'
              }
            });

            if (!litellmResponse.ok) {
              const litellmErrorText = await litellmResponse.text();
              console.error('LiteLLM专用API错误响应:', litellmErrorText);
              throw new Error(`获取模型列表失败: ${litellmResponse.status} ${litellmResponse.statusText}`);
            }

            fallbackData = await litellmResponse.json();
            console.log('LiteLLM专用API返回数据:', fallbackData);

            // LiteLLM专用格式可能有不同的数据结构
            if (!fallbackData.data && Array.isArray(fallbackData)) {
              // 如果直接返回数组，适配为标准格式
              fallbackData = { data: fallbackData };
            } else if (!fallbackData.data || !Array.isArray(fallbackData.data)) {
              throw new Error('API返回格式不符合预期');
            }
          }

          const models: Model[] = fallbackData.data
            .filter((model: any) => !model.id.includes('text-embedding'))
            .map((model: any) => ({
              id: model.id,
              name: model.id.split('/').pop() || model.id,
              contextWindow: model.context_length || undefined
            }));

          setAvailableModels(models);
          return;
        }

        throw new Error(`获取模型列表失败: ${response.status} ${response.statusText}`);
      }

      // 尝试解析JSON响应
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('JSON解析错误:', jsonError);
        const rawText = await response.text();
        console.log('原始响应内容:', rawText);
        throw new Error('无法解析API响应为JSON');
      }

      // 添加调试日志
      console.log('API返回数据:', data);

      // 处理返回的模型数据
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('API返回格式不符合预期');
      }

      const models: Model[] = data.data
        // 移除 text-embedding 类型的模型
        .filter((model: any) => !model.id.includes('text-embedding'))
        .map((model: any) => ({
          id: model.id,
          name: model.id.split('/').pop() || model.id,
          contextWindow: model.context_length || undefined
        }));

      setAvailableModels(models);
    } catch (err: any) {
      // 提供更详细的错误信息
      let errorMessage = err.message || '获取模型列表失败';

      // 检查是否是HTML解析错误
      if (errorMessage.includes('Unexpected token') && errorMessage.includes('<!DOCTYPE')) {
        errorMessage = '获取模型列表失败: 服务器返回了HTML而不是JSON。这可能是因为ngrok配置问题或服务器错误。请检查服务器日志。';
        console.error('HTML解析错误，服务器可能返回了错误页面而不是API响应');
      }
      // 检查是否是网络错误
      else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        errorMessage = '获取模型列表失败: 网络连接错误，请检查服务器是否正在运行并且可以访问。';
      }
      // 检查是否是认证错误
      else if (errorMessage.includes('401') || errorMessage.includes('403')) {
        errorMessage = '获取模型列表失败: 认证失败，请检查API密钥是否正确。';
      }

      setError(errorMessage);
      console.error('获取模型列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 处理模型选择变化
  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev =>
      prev.includes(modelId)
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  // 当选中的提供商变化时，更新已选模型
  useEffect(() => {
    if (selectedProvider) {
      // 从已有的提供商配置中获取选中的模型
      setSelectedModels(selectedProvider.models.map(model => model.id));
    }
  }, [selectedProvider]);

  // 保存到数据库
  const saveToDatabase = async (models: Model[]) => {
    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 存储所有创建的模型ID
      const createdModelIds: string[] = [];
      let providerId: string = '';

      // 获取现有模型列表
      const existingModelsResponse = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const existingModels = await existingModelsResponse.json();

      // 循环插入每个模型
      for (let i = 0; i < models.length; i++) {
        const model = models[i];

        // 检查是否已存在相同配置的模型 - 使用provider字段匹配
        const existingModel = existingModels.find((em: any) =>
          em.model_name === model.id &&
          em.credential === apiKey &&
          em.base_url === apiBaseUrl &&
          em.provider === providerName // 使用provider字段进行比较
        );

        if (existingModel) {
          console.log(`模型 ${model.id} 已存在，跳过插入`);
          createdModelIds.push(existingModel.id);
          if (i === 0) {
            providerId = existingModel.id;
          }
          continue;
        }

        console.log(`保存新模型 ${i+1}/${models.length}: ${model.id}`);

        // 准备请求数据
        // 为每个模型生成唯一名称
        const uniqueName = `${providerName}-${model.id.split('/').pop() || model.id}`;
        const payload = {
          name: uniqueName, // 使用唯一名称
          model_type: modelType,
          model_name: model.id,
          provider: providerName,
          credential: apiKey,
          base_url: apiBaseUrl
        };

        // 创建模型记录
        const response = await fetch(API.MODELS.LIST, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`保存模型 ${model.id} 失败:`, errorText);

          // 检查是否是唯一约束冲突错误
          if (errorText.includes('unique') || errorText.includes('唯一') || errorText.includes('constraint')) {
            console.warn(`模型 ${model.id} 可能存在名称冲突，尝试跳过`);
            // 尝试获取现有模型
            const retryExistingModels = await (await fetch(API.MODELS.LIST, {
              headers: { 'Authorization': `Bearer ${token}` }
            })).json();

            // 再次尝试查找匹配的模型
            const matchingModel = retryExistingModels.find((em: any) =>
              em.model_name === model.id &&
              em.base_url === apiBaseUrl
            );

            if (matchingModel) {
              console.log(`找到匹配的模型 ${model.id}，使用现有ID`);
              createdModelIds.push(matchingModel.id);
              if (i === 0) {
                providerId = matchingModel.id;
              }
              continue; // 跳过当前模型，继续处理下一个
            }
          }

          throw new Error(`保存模型 ${model.id} 失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`模型 ${model.id} 保存成功，返回数据:`, data);

        createdModelIds.push(data.id);

        // 保存第一个模型的ID作为提供商ID
        if (i === 0) {
          providerId = data.id;
        }
      }

      console.log(`成功处理了 ${createdModelIds.length} 个模型，提供商ID: ${providerId}`);
      return providerId;
    } catch (error) {
      console.error('保存到数据库失败:', error);
      throw error;
    }
  };

  // 删除提供商的所有现有模型
  const deleteExistingModels = async (providerName: string, apiBaseUrl: string, apiKey: string) => {
    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 获取现有模型列表
      const existingModelsResponse = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const existingModels = await existingModelsResponse.json();

      // 找出属于当前提供商的所有模型 - 使用provider字段匹配
      const modelsToDelete = existingModels.filter((model: any) =>
        model.provider === providerName &&
        model.base_url === apiBaseUrl &&
        model.credential === apiKey
      );

      console.log(`找到 ${modelsToDelete.length} 个需要删除的现有模型`);

      // 删除这些模型
      for (const model of modelsToDelete) {
        console.log(`删除模型: ${model.model_name}`);
        await fetch(API.MODELS.DETAIL(model.id), {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }

      return true;
    } catch (error) {
      console.error('删除现有模型失败:', error);
      throw error;
    }
  };

  const handleSave = async () => {
    if (!apiBaseUrl || !apiKey || !providerName) {
      setError('请填写完整的 API 信息');
      return;
    }

    if (selectedModels.length === 0) {
      setError('请至少选择一个模型');
      return;
    }

    setLoading(true);

    try {
      // 从可用模型中筛选出用户选中的模型
      const updatedModels = availableModels
        .filter(model => selectedModels.includes(model.id))
        .map(model => ({
          id: model.id,
          name: model.id.split('/').pop() || model.id, // 使用简化的模型名称
          contextWindow: model.contextWindow
        }));

      // 0. 先删除该提供商的所有现有模型
      await deleteExistingModels(providerName, apiBaseUrl, apiKey);

      // 1. 保存到数据库
      const providerId = await saveToDatabase(updatedModels);

      // 2. 更新本地状态
      const providerType = modelType;

      const updatedProvider = {
        id: providerId,
        name: providerName,
        type: providerType,
        apiKey: apiKey,
        baseUrl: apiBaseUrl,
        models: updatedModels,
        enabled: true,
        icon: providerName.charAt(0).toUpperCase(),
        color: 'bg-blue-500'
      };

      // 刷新 store 中的提供商列表
      await useLLMStore.getState().fetchProviders();

      // 从刷新后的数据中找到对应的提供商
      const refreshedProviders = useLLMStore.getState().providers;
      const refreshedProvider = refreshedProviders.find(p =>
        p.name === providerName &&
        p.baseUrl === apiBaseUrl &&
        p.apiKey === apiKey
      );

      if (refreshedProvider) {
        // 更新当前选中的提供商
        useLLMStore.getState().setSelectedProvider(refreshedProvider);
      } else {
        // 如果找不到刷新后的提供商，使用更新后的数据
        await useLLMStore.getState().addProvider(updatedProvider);
        useLLMStore.getState().setSelectedProvider(updatedProvider);
      }

      // 调用回调函数
      if (onSave) {
        onSave(updatedModels);
      }

      // 显示成功消息
      setError(null);
      setSuccessMessage(`成功保存了 ${updatedModels.length} 个模型！您可以点击"关闭"按钮返回，或继续编辑。`);

      // 不关闭对话框，让用户可以继续查看和编辑
      // setOpen(false);
    } catch (err: any) {
      // 提供更详细的错误信息
      let errorMessage = err.message || '保存失败';

      // 检查是否是数据库唯一约束错误
      if (errorMessage.includes('unique') || errorMessage.includes('唯一') || errorMessage.includes('constraint')) {
        errorMessage = '保存失败：模型名称冲突。请使用不同的提供商名称或检查是否已存在相同配置的模型。';
      }
      // 检查是否是网络错误
      else if (errorMessage.includes('network') || errorMessage.includes('网络')) {
        errorMessage = '保存失败：网络连接错误，请检查您的网络连接。';
      }
      // 检查是否是认证错误
      else if (errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('认证')) {
        errorMessage = '保存失败：认证失败，请重新登录后再试。';
      }

      setError(errorMessage);
      setSuccessMessage(null);
      console.error('保存失败:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-green-500 hover:bg-green-600">
          <Settings className="h-4 w-4 mr-1" />
          管理
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>管理模型</DialogTitle>
        </DialogHeader>

        {/* 添加表单 */}
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="provider-name">提供商名称：{providerName}</Label>
            <Input
              id="provider-name"
              value={providerName}
              onChange={(e) => setProviderName(e.target.value)}
              placeholder="例如: OpenAI, DeepSeek"
              hidden
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="api-url">API 地址</Label>
            <Input
              id="api-url"
              value={apiBaseUrl}
              onChange={(e) => setApiBaseUrl(e.target.value)}
              placeholder="例如: https://api.openai.com"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="api-key">API Key</Label>
            <Input
              id="api-key"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="例如: sk-..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="model-type">模型类型</Label>
            <Select
              value={modelType}
              onValueChange={(value: 'openai' | 'deepseek' | 'qwen') => setModelType(value)}
            >
              <SelectTrigger id="model-type">
                <SelectValue placeholder="选择模型类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="deepseek">DeepSeek</SelectItem>
                <SelectItem value="qwen">Qwen</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            onClick={fetchModels}
            disabled={!apiBaseUrl || loading}
            variant="outline"
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                获取中...
              </>
            ) : (
              '获取模型列表'
            )}
          </Button>
        </div>

        {error && (
          <div className="py-2 text-center">
            <p className="text-red-500">{error}</p>
          </div>
        )}

        {successMessage && (
          <div className="py-2 text-center">
            <p className="text-green-500">{successMessage}</p>
          </div>
        )}

        <div className="py-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">正在获取模型列表...</span>
            </div>
          ) : availableModels.length > 0 ? (
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-2">
                {availableModels.map((model) => (
                  <div
                    key={model.id}
                    className="flex items-center space-x-2 p-3 hover:bg-gray-50 rounded-lg border border-gray-100"
                  >
                    <Checkbox
                      id={`model-${model.id}`}
                      checked={selectedModels.includes(model.id)}
                      onCheckedChange={() => handleModelToggle(model.id)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center mr-3">
                          <span className="text-purple-600 font-medium">
                            {model.name.split('-')[0][0].toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <label
                            htmlFor={`model-${model.id}`}
                            className="text-sm font-medium cursor-pointer block"
                          >
                            {model.name.split('-').join(' ')}
                          </label>
                          <span className="text-xs text-gray-500">
                            {model.id}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {model.contextWindow && (
                        <span className="px-2 py-1 bg-gray-50 rounded-md text-xs text-gray-600">
                          {model.contextWindow}k
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="py-8 text-center text-gray-500">
              {error ? null : "请先获取模型列表"}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2">
          {successMessage ? (
            // 成功后显示关闭按钮
            <Button variant="outline" onClick={() => setOpen(false)}>
              关闭
            </Button>
          ) : (
            // 未保存时显示取消按钮
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={loading || selectedModels.length === 0 || !apiBaseUrl || !apiKey || !providerName}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            保存已选 ({selectedModels.length} 个模型)
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}