// 论文分节解读

import { cn } from "@/lib/utils";
import { useEffect, useState } from "react";
import { DIGITAL_EXPERT_PROMPT } from "@/lib/prompts/digital";
import ReactMarkdown from 'react-markdown';
import { PlayCircle, CheckCircle, ChevronDown, Loader2 } from "lucide-react";
import remarkGfm from 'remark-gfm';

interface Section {
    question: string;
    answer: string;
  }
  
  interface ReadingSidepanelProps {
    isOpen: boolean;
    sections: Section[];
    selectedSections: number[];
    onSectionComplete: (sectionIndex: number) => void;
  }
  
  export function ReadingSidepanel({ isOpen, sections, selectedSections, onSectionComplete }: ReadingSidepanelProps) {
    const [currentSectionIndex, setCurrentSectionIndex] = useState<number | null>(null);
    const [isProcessing, setIsProcessing] = useState(false);
    const [sectionStatus, setSectionStatus] = useState<Record<number, 'pending' | 'processing' | 'completed'>>({});
    const [messages, setMessages] = useState<Array<{ role: string; content: string }>>([
      { role: 'system', content: DIGITAL_EXPERT_PROMPT }
    ]);

    // 添加状态来存储每个章节的响应内容
    const [sectionResponses, setSectionResponses] = useState<Record<number, string>>({});
    const [expandedThinking, setExpandedThinking] = useState<Record<number, boolean>>({});

    // 处理单个章节
    const processSection = async (sectionIndex: number) => {
      if (isProcessing || sectionStatus[sectionIndex] === 'completed') return;

      setIsProcessing(true);
      setSectionStatus(prev => ({ ...prev, [sectionIndex]: 'processing' }));

      try {
        // 检查是否是最后一个选中的章节
        const isLastSection = sectionIndex === selectedSections[selectedSections.length - 1];
        
        const response = await fetch(`${import.meta.env.VITE_LLM_HOST}/chat/completions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${import.meta.env.VITE_LLM_API_KEY}`
          },
          body: JSON.stringify({
            model: import.meta.env.VITE_LLM_MODEL,
            messages: [
              ...messages,
              { 
                role: 'user', 
                content: `阅读该章节：${sections[sectionIndex].question}\n${sections[sectionIndex].answer}${isLastSection ? '\n<完成>' : ''}`
              }
            ],
            stream: true
          })
        });
  
        if (!response.ok) {
          throw new Error('处理失败');
        }
  
        // 处理流式响应
        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let currentResponse = '';

        if (reader) {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(5));
                  if (data.choices && data.choices[0].delta.content) {
                    currentResponse += data.choices[0].delta.content;
                    setSectionResponses(prev => ({
                      ...prev,
                      [sectionIndex]: currentResponse
                    }));
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          }
        }
  
        // 处理完成后更新状态
        setSectionStatus(prev => ({ ...prev, [sectionIndex]: 'completed' }));
        onSectionComplete(sectionIndex);
        
      } catch (error) {
        console.error('处理错误:', error);
        setSectionStatus(prev => ({ ...prev, [sectionIndex]: 'pending' }));
      } finally {
        setIsProcessing(false);
      }
    };

  const processMarkdownContent = (content: string) => {
    const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/);
    const mainContent = content.replace(/<think>[\s\S]*?<\/think>/, '');
    return { mainContent, thinkContent: thinkMatch ? thinkMatch[1] : null };
  };
  
    // 初始化选中章节的状态
    useEffect(() => {
      const newStatus: Record<number, 'pending' | 'processing' | 'completed'> = {};
      selectedSections.forEach(index => {
        if (!sectionStatus[index]) {
          newStatus[index] = 'pending';
        }
      });
      setSectionStatus(prev => ({ ...prev, ...newStatus }));
    }, [selectedSections]);
  
    return (
      <div className={cn(
        "absolute inset-y-0 left-0 w-[40%] bg-white dark:bg-neutral-900 border-r transform transition-transform duration-300 ease-in-out flex flex-col",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Header */}
        <div className="flex-none border-b border-gray-200 dark:border-neutral-700">
          <div className="p-4">
            <h3 className="text-lg font-semibold">阅读进度</h3>
          </div>
        </div>

        {/* Main Content - Scrollable */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 space-y-4">
            {selectedSections.map((sectionIndex) => (
              <div key={sectionIndex} className="border rounded-lg">
                {/* Section Header */}
                <div 
                  className="p-4 border-b cursor-pointer flex items-center justify-between"
                  onClick={() => setExpandedThinking(prev => ({
                    ...prev,
                    [sectionIndex]: !prev[sectionIndex]
                  }))}
                >
                  <span className="font-medium">{sections[sectionIndex].question}</span>
                  <div className="flex items-center gap-2">
                    {sectionStatus[sectionIndex] === 'completed' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : sectionStatus[sectionIndex] === 'processing' ? (
                      <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                    ) : (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          processSection(sectionIndex);
                        }}
                        disabled={isProcessing}
                        className="text-blue-600 hover:text-blue-700 disabled:opacity-50"
                      >
                        <PlayCircle className="h-5 w-5" />
                      </button>
                    )}
                    <ChevronDown className={`h-4 w-4 transition-transform ${
                      expandedThinking[sectionIndex] ? 'rotate-180' : ''
                    }`} />
                  </div>
                </div>

                {/* Section Content */}
                {expandedThinking[sectionIndex] && (sectionStatus[sectionIndex] === 'processing' || sectionStatus[sectionIndex] === 'completed') && (
                  <div className="p-4 prose prose-sm dark:prose-invert max-w-none">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {processMarkdownContent(sectionResponses[sectionIndex] || '').mainContent}
                    </ReactMarkdown>
                    {processMarkdownContent(sectionResponses[sectionIndex] || '').thinkContent && (
                      <div className="mt-4 pt-4 border-t">
                        <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">思考过程</div>
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                          {processMarkdownContent(sectionResponses[sectionIndex] || '').thinkContent || ''}
                        </ReactMarkdown>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex-none border-t border-gray-200 dark:border-neutral-700">
          <div className="p-4 flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {/* 已完成 {completedSections.length}/{selectedSections.length} 章节 */}
            </span>
            <button
              onClick={() => {
                const nextPending = selectedSections.find(index => sectionStatus[index] === 'pending');
                if (nextPending !== undefined) {
                  processSection(nextPending);
                }
              }}
              disabled={isProcessing || selectedSections.every(index => sectionStatus[index] === 'completed')}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isProcessing ? '处理中...' : '继续阅读'}
            </button>
          </div>
        </div>
      </div>
    );
  }