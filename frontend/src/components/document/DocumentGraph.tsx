import { BookOpen } from "lucide-react";

export function DocumentGraph() {
  return (
    <div className="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
      <div className="flex flex-col">
        <div className="-m-1.5 overflow-x-auto">
          <div className="p-1.5 min-w-full inline-block align-middle">
            <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
              {/* Header */}
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-neutral-700">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    我的文档图谱
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    构建您的个人知识结构体系。
                  </p>
                </div>
              </div>

              {/* Content */}
              <div className="p-8 flex flex-col items-center justify-center min-h-[400px] text-center">
                <BookOpen className="h-16 w-16 text-blue-500 mb-4" />
                <h3 className="text-xl font-semibold text-gray-800 dark:text-neutral-200 mb-2">
                  欢迎使用文档图谱
                </h3>
                <p className="text-gray-600 dark:text-neutral-400 max-w-md mb-6">
                  这里将展示您的知识结构体系，帮助您更好地组织和理解文档之间的关联。
                </p>
                <p className="text-sm text-gray-500 dark:text-neutral-500 italic">
                  功能正在开发中，敬请期待...
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
