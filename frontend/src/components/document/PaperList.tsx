import { useState, useEffect } from "react";
import { useDocumentStore } from "@/store/documentStore";
import {
  Plus,
  Search,
  Eye,
  EyeOff,
  Trash2,
  RefreshCw,
} from "lucide-react";

import { Document } from "@/store/documentStore";
import { UploadDocumentDialog } from "@/components/document/UploadDocumentDialog";

interface PaperListProps {
  onPreviewDocument: (document: Document | null) => void;
}

export function PaperList({ onPreviewDocument }: PaperListProps) {
  const [isUploadDocumentOpen, setIsUploadDocumentOpen] = useState(false);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const { documents, fetchDocuments } = useDocumentStore();
  const [searchQuery, setSearchQuery] = useState("");
  const { deleteDocument } = useDocumentStore();
  const [currentPreviewDoc, setCurrentPreviewDoc] = useState<Document | null>(null);

  // 添加分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // 添加过滤文档的计算属性
  const filteredDocuments = documents.filter((doc) =>
    doc.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // 添加分页逻辑
  const pageCount = Math.ceil(filteredDocuments.length / itemsPerPage);
  const paginatedDocuments = filteredDocuments.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // 修改 handlePreview 函数
  const handlePreview = (doc: Document) => {
    if (currentPreviewDoc?.id === doc.id) {
      // 如果点击的是当前预览的文档，则关闭预览
      setCurrentPreviewDoc(null);
      onPreviewDocument(null);
    } else {
      // 否则，预览新的文档
      setCurrentPreviewDoc(doc);
      onPreviewDocument(doc);
    }
  };

  return (
    <div className="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
      {/* Content */}
      <div className="flex flex-col">
        <div className="-m-1.5 overflow-x-auto">
          <div className="p-1.5 min-w-full inline-block align-middle">
            <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
              {/* Header */}
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-neutral-700">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-neutral-200 flex items-center gap-2">
                    文档库
                    <span
                      className={`text-sm px-2 py-0.5 rounded-full ${
                        documents.length >= 100
                          ? "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300"
                          : documents.length >= 80
                          ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
                          : documents.length >= 50
                          ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300"
                          : "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300"
                      }`}
                    >
                      {documents.length}/100
                    </span>
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    管理您的所有文档，支持预览和下载。
                  </p>
                </div>

                <div>
                  <div className="inline-flex gap-x-2">
                    <div className="relative">
                      <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="搜索文档..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-8 py-2 px-3 text-sm rounded-lg border border-gray-200 bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200"
                      />
                    </div>
                    <button
                      onClick={() => fetchDocuments()}
                      className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700"
                    >
                      <RefreshCw className="h-4 w-4" />
                      刷新
                    </button>
                    <button
                      onClick={() => setIsUploadDocumentOpen(true)}
                      className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                    >
                      <Plus className="h-4 w-4" />
                      添加文档
                    </button>
                    {selectedDocuments.length > 0 && (
                      <button
                        onClick={async () => {
                          if (confirm("确定要删除选中的文档吗？")) {
                            try {
                              // 使用 Promise.all 并行处理所有删除请求
                              await Promise.all(
                                selectedDocuments.map((id) =>
                                  deleteDocument(id)
                                )
                              );
                              setSelectedDocuments([]);
                              // 添加这一行来刷新文档列表
                              await fetchDocuments();
                            } catch (error) {
                              alert(
                                error instanceof Error
                                  ? error.message
                                  : "删除失败，请重试"
                              );
                            }
                          }
                        }}
                        className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-red-600 text-white hover:bg-red-700 disabled:opacity-50 disabled:pointer-events-none"
                      >
                        <Trash2 className="h-4 w-4" />
                        删除选中
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Table */}
              <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                <thead className="bg-gray-50 dark:bg-neutral-800">
                  <tr>
                    <th scope="col" className="ps-6 py-3 text-start">
                      <input
                        type="checkbox"
                        className="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedDocuments(
                              filteredDocuments.map((doc) => doc.id)
                            );
                          } else {
                            setSelectedDocuments([]);
                          }
                        }}
                      />
                    </th>
                    <th
                      scope="col"
                      className="ps-6 lg:ps-3 xl:ps-0 pe-6 py-3 text-start"
                    >
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        文档名称
                      </span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-start">
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        类型
                      </span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-start">
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        大小
                      </span>
                    </th>

                    <th
                      scope="col"
                      className="px-6 py-3 text-end flex gap-4 justify-end"
                    >
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-neutral-700">
                  {paginatedDocuments.map((doc) => (
                    <tr key={doc.id}>
                      <td className="size-px whitespace-nowrap">
                        <div className="ps-6 py-3">
                          <input
                            type="checkbox"
                            checked={selectedDocuments.includes(doc.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedDocuments([
                                  ...selectedDocuments,
                                  doc.id,
                                ]);
                              } else {
                                setSelectedDocuments(
                                  selectedDocuments.filter(
                                    (id) => id !== doc.id
                                  )
                                );
                              }
                            }}
                            className="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500"
                          />
                        </div>
                      </td>
                      <td className="size-px whitespace-nowrap">
                        <div className="ps-6 lg:ps-3 xl:ps-0 pe-6 py-3">
                          <span title={doc.title}>
                            {doc.title.length > 30
                              ? `${doc.title.slice(0, 30)}...`
                              : doc.title}
                          </span>
                        </div>
                      </td>
                      <td className="size-px whitespace-nowrap">
                        <div className="px-6 py-3">{doc.type}</div>
                      </td>
                      <td className="size-px whitespace-nowrap">
                        <div className="px-6 py-3">
                          {(doc.file_size / 1024).toFixed(2)} KB
                        </div>
                      </td>
                      <td className="size-px whitespace-nowrap">
                        <div className="px-6 py-3 text-end flex gap-4 justify-end">
                          {doc.content_type === "application/pdf" && (
                            <>
                              <button
                                title={currentPreviewDoc?.id === doc.id ? "关闭预览" : (doc.markdown_url ? "预览 (已有Markdown内容)" : "预览")}
                                onClick={() => handlePreview(doc)}
                                className={`${
                                  currentPreviewDoc?.id === doc.id ? 'bg-blue-100 rounded-md p-1' : ''
                                } ${
                                  doc.markdown_url ? 'text-green-500 hover:text-green-600 dark:text-green-400 dark:hover:text-green-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                                }`}
                              >
                                {currentPreviewDoc?.id === doc.id ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {/* 在表格后添加分页控件 */}
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-neutral-700">
                <div>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    显示 {paginatedDocuments.length} 条，共{" "}
                    {filteredDocuments.length} 条
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {Array.from({ length: pageCount }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i + 1)}
                      className={`px-3 py-1 rounded-lg ${
                        currentPage === i + 1
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-600 dark:bg-neutral-700 dark:text-neutral-300"
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 使用新的上传文档对话框 */}
      <UploadDocumentDialog
        open={isUploadDocumentOpen}
        onOpenChange={setIsUploadDocumentOpen}
      />
    </div>
  );
}


