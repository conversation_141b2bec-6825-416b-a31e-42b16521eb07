import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
// 移除 Split import
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ConvertFileDropzone } from "../ConvertFileDropzone";
// 添加 Save 图标导入
import { Eye, Save, Images, ChevronDown } from "lucide-react";
import ReactMarkdown from "react-markdown";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// 在文件顶部添加导入
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose,
} from "@/components/ui/drawer";

// 添加 store 导入
import { usePaperImagesStore } from "@/store/paperImagesStore";
// import { generateThumbnail } from "@/utils/imageUtils";
import { useAuth } from "@/utils/AuthContext";
import { useModelConfigStore } from "@/store/modelConfigStore";

// 添加 PDF.js 导入
import * as pdfjsLib from "pdfjs-dist";
pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 支持的文件类型
const SUPPORTED_TYPES = {
  "application/pdf": ".pdf",
  "application/msword": ".doc",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    ".docx",
  "application/vnd.ms-excel": ".xls",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": ".xlsx",
  "application/vnd.ms-powerpoint": ".ppt",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    ".pptx",
  "text/html": ".html",
  "text/plain": ".txt",
  "application/zip": ".zip",
};

export function AddDocumentDialog({ open, onOpenChange }: Props) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({ name: "" });
  const [file, setFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [convertedMarkdown, setConvertedMarkdown] = useState<string | null>(
    null
  );
  // 添加转换模式状态
  const [conversionMode, setConversionMode] = useState<"fast" | "accurate">(
    "accurate"
  );
  const { configs } = useModelConfigStore();
  const documentServices = configs.filter(
    (config) => config.type === "document"
  );
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState<string | null>(null);
  // 添加新的 useEffect 来设置默认选择
  useEffect(() => {
    if (documentServices.length > 0 && !selectedService) {
      setSelectedService(documentServices[0].name);
    }
  }, [documentServices, selectedService]);
  const selectedServiceConfig = documentServices.find(
    (service) => service.name === selectedService
  );

  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showImageList, setShowImageList] = useState(false);
  const [paperImages, setPaperImages] = useState<any[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const [loadingImageId, setLoadingImageId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<"pdf" | "markdown">("pdf");
  const { addImage, removeImage } = usePaperImagesStore();
  // 添加重置函数
  const resetDialog = useCallback(() => {
    setFormData({ name: "" });
    setFile(null);
    setError(null);
    setConvertedMarkdown(null);
    setPdfUrl(null);
    setNumPages(0);
    setCurrentPage(1);
    setShowImageList(false);
    setPaperImages([]);
    setIsLoadingImages(false);
    setLoadingImageId(null);
    setActiveTab("pdf");
    setConversionMode("accurate");
    // 清空已选择的图片
    usePaperImagesStore.getState().clearImages();
  }, []);

  useEffect(() => {
    if (open) {
      resetDialog();
    }
  }, [open, resetDialog]);

  // 添加获取单张图片（base64编码）的函数
  // ... 其他 imports 和状态定义 ...

  // 定义 API 配置
  const apiConfig = {
    baseUrl: selectedServiceConfig?.host || import.meta.env.VITE_MINERU_API_URL,
    apiKey:
      selectedServiceConfig?.apiKey || import.meta.env.VITE_MINERU_API_KEY,
  };

  // 修改 fetchImageContent 函数
  const fetchImageContent = async (fileName: string, imageName: string) => {
    try {
      const response = await fetch(`${apiConfig.baseUrl}/document/image`, {
        method: "POST",
        headers: {
          "X-API-Key": apiConfig.apiKey,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          file_name: fileName,
          image_name: imageName,
        }),
      });

      if (!response.ok) {
        throw new Error("获取图片失败");
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("获取图片失败:", error);
      throw error;
    }
  };

  // 修改获取图片列表的函数
  const handleFetchImages = async () => {
    console.log("开始获取论文图片...");
    setIsLoadingImages(true);

    try {
      const response = await fetch(`${apiConfig.baseUrl}/document/images`, {
        method: "POST",
        headers: {
          "X-API-Key": apiConfig.apiKey,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          file_name: file?.name || "",
        }),
      });

      console.log("请求已发送，等待响应...");
      const data = await response.json();
      console.log("收到响应:", data);

      if (!response.ok) {
        throw new Error(data.detail || "获取图片列表失败");
      }

      const { images } = data;
      console.log("获取到图片数量:", images?.length || 0);
      setPaperImages(images || []);
      setShowImageList(true);
    } catch (error) {
      console.error("获取图片列表失败:", error);
      setError(error instanceof Error ? error.message : "获取图片列表失败");
    } finally {
      setIsLoadingImages(false);
    }
  };

  // 修改 handleFileSelect 函数
  const handleFileSelect = async (file: File | null) => {
    if (!file) {
      setError(null);
      setFile(null);
      setPdfUrl(null);
      return;
    }

    if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {
      setError(
        `不支持的文件类型。支持的格式：${Object.values(SUPPORTED_TYPES).join(
          ", "
        )}`
      );
      setFile(null);
      setPdfUrl(null);
      return;
    }

    setError(null);
    setFile(file);
    setFormData((prev) => ({ ...prev, name: file.name }));

    // 处理 PDF 预览
    if (file.type === "application/pdf") {
      try {
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
        const pdf = await pdfjsLib.getDocument(url).promise;
        setNumPages(pdf.numPages);
        setCurrentPage(1);
      } catch (error) {
        console.error("PDF 加载失败:", error);
        setError("PDF 预览加载失败");
        setPdfUrl(null);
      }
    } else {
      setPdfUrl(null);
    }
  };

  // 修改 handleSubmit 函数
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;
    setError(null);
    setConvertedMarkdown(null);

    const formDataToSend = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value) formDataToSend.append(key, value.toString());
    });
    formDataToSend.append("file", file);

    try {
      setIsSubmitting(true);
      let response;

      if (conversionMode === "fast") {
        response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL}/api/markitdown/document`,
          {
            method: "POST",
            body: formDataToSend,
          }
        );
      } else {
        response = await fetch(`${apiConfig.baseUrl}/document`, {
          method: "POST",
          headers: {
            "X-API-Key": apiConfig.apiKey,
          },
          body: formDataToSend,
        });
      }

      const data = await response.json();
      console.log("Response data:", data); // 添加日志

      if (!response.ok) {
        throw new Error(data.detail || "转换失败");
      }

      setConvertedMarkdown(data.markdown_content);
      setActiveTab("markdown");

      // 如果需要处理图片，可以在这里添加图片处理逻辑
      if (data.images && data.images.length > 0) {
        console.log("转换的图片：", data.images);
      }
      // }
    } catch (error) {
      console.error("Error:", error);
      setError(error instanceof Error ? error.message : "转换失败，请重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 添加保存文档的处理函数
  const handleSaveDocument = async () => {
    if (!file || !convertedMarkdown) return;
    setError(null);

    try {
      // 1. 保存文档到数据库
      console.log("开始保存文档...");
      const formDataToSend = new FormData();
      formDataToSend.append("file", file);
      formDataToSend.append("name", formData.name);
      formDataToSend.append("markdown_content", convertedMarkdown);
      formDataToSend.append("content_type", file.type);
      formDataToSend.append("type", file.type.split("/")[1].toUpperCase());
      // 移除 user_id，因为后端会从 token 中获取
      // formDataToSend.append('user_id', userId?.toString() || '');

      const token =
        localStorage.getItem("token") || sessionStorage.getItem("token");
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/api/documents`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formDataToSend,
        }
      );

      const documentData = await response.json();

      if (!response.ok) {
        throw new Error(documentData.detail || "保存文档失败");
      }

      // 2. 保存选中的图片
      const { selectedImages } = usePaperImagesStore.getState();
      // console.log(`开始保存 ${selectedImages.size} 张选中的图片...`);
      for (const imageName of Array.from(selectedImages.keys())) {
        const image = selectedImages.get(imageName);
        if (!image?.content) {
          console.log(`跳过图片 ${imageName}: 无内容`);
          continue;
        }

        const imageFormData = new FormData();
        // 将 base64 转换为 Blob
        const imageBlob = await fetch(
          `data:${image.content.content_type};base64,${image.content.base64_content}`
        ).then((r) => r.blob());

        // 构建图片文件
        const imageFile = new File([imageBlob], image.file_name, {
          type: image.content.content_type,
        });

        // 生成缩略图
        // 生成缩略图
        let thumbnailBase64: string;
        try {
          thumbnailBase64 = await generateThumbnail(imageFile);
        } catch (error) {
          console.error(`生成图片 ${imageName} 的缩略图失败:`, error);
          continue;
        }

        // 确保使用正确的图片类型
        const defaultType = "flowchart"; // 设置默认类型
        const imageType = image.type?.toLowerCase() || defaultType;

        imageFormData.append("file", imageFile);
        imageFormData.append("name", imageName);
        imageFormData.append("description", `从文档 ${file.name} 中提取的图片`);
        imageFormData.append("type", imageType);
        imageFormData.append("document_id", documentData.id.toString());
        imageFormData.append("thumbnail", thumbnailBase64);

        try {
          const imageResponse = await fetch(
            `${import.meta.env.VITE_API_BASE_URL}/api/diagrams`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
              },
              body: imageFormData,
            }
          );

          if (!imageResponse.ok) {
            const errorData = await imageResponse.json();
            console.error(`保存图片 ${imageName} 失败:`, errorData.detail);
            continue;
          }

          const imageData = await imageResponse.json();
          console.log(`图片 ${imageName} 保存成功，ID:`, imageData.id);
        } catch (error) {
          console.error(`保存图片 ${imageName} 时发生错误:`, error);
        }
      }

      // 清空已选择的图片
      usePaperImagesStore.getState().clearImages();
      // 关闭对话框
      onOpenChange(false);
    } catch (error) {
      console.error("保存过程中发生错误:", error);
      setError(error instanceof Error ? error.message : "保存失败，请重试");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={`
        transition-all duration-300 p-6 overflow-hidden resize
        ${file?.type === "application/pdf" ? "w-[1200px]" : "w-[600px]"}
        max-w-[95vw] max-h-[95vh]
      `}
      >
        <DialogHeader>
          <DialogTitle>添加新论文</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={handleSubmit}
          className="space-y-4 h-[calc(95vh-120px)]"
        >
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <div
            className={`
            grid gap-4 h-full transition-all duration-300
            ${file?.type === "application/pdf" ? "grid-cols-10" : "grid-cols-1"}
          `}
          >
            {/* 左侧表单区域 */}
            <div
              className={`
              space-y-4 overflow-auto px-4
              ${file?.type === "application/pdf" ? "col-span-3" : "w-full"}
            `}
            >
              <ConvertFileDropzone
                onFileSelect={handleFileSelect}
                allowPaste={false}
                acceptedTypes={Object.values(SUPPORTED_TYPES).join(",")}
              />
              <div className="space-y-2 w-full">
                <Label
                  htmlFor="name"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  文档名称
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder="请输入文档名称"
                  className="w-full transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                  required
                />
              </div>
              <div className="flex flex-col space-y-2">
                <div className="flex space-x-2 items-center">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        size="default"
                        className="w-[140px] justify-between"
                      >
                        {conversionMode === "fast" ? "Markitdown" : "MinerU"}
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start" className="w-[140px]">
                      <DropdownMenuItem
                        onClick={() => setConversionMode("fast")}
                      >
                        MarkitDown
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setConversionMode("accurate")}
                      >
                        MinerU
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  <Button
                    type="submit"
                    disabled={isSubmitting || !file || !!convertedMarkdown}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                        {conversionMode === "fast" ? "快速..." : "精确转换..."}
                      </div>
                    ) : (
                      "转换"
                    )}
                  </Button>
                  {convertedMarkdown && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSaveDocument}
                      className="flex items-center"
                    >
                      <Save className="w-4 h-4 mr-1" />
                      保存
                    </Button>
                  )}
                </div>
                {conversionMode === "accurate" &&
                  documentServices.length > 0 && (
                    <div className="mt-4 space-y-2 border rounded-lg p-4">
                      <div className="text-sm font-medium mb-2">
                        选择文档转换服务：
                      </div>
                      <div className="grid grid-cols-1 gap-3">
                        {documentServices.map((service) => (
                          <div
                            key={service.name}
                            className={`block p-4 bg-white border rounded-lg shadow-sm transition-all duration-200
                  ${
                    selectedService === service.name
                      ? "border-blue-500 ring-2 ring-blue-200"
                      : "border-gray-200 hover:border-gray-300 dark:border-gray-700"
                  }
                  dark:bg-gray-800 dark:hover:bg-gray-700`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <Checkbox
                                  id={service.name}
                                  checked={selectedService === service.name}
                                  onCheckedChange={() =>
                                    setSelectedService(
                                      service.name === selectedService
                                        ? null
                                        : service.name
                                    )
                                  }
                                  className="h-4 w-4"
                                />
                                <div>
                                  <h5 className="text-base font-medium text-gray-900 dark:text-white">
                                    {service.name}
                                  </h5>
                                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 ">
                                    {service.host}
                                  </p>
                                  <div className="relative w-full flex items-center justify-between gap-10 mt-1">
                                    <Input
                                      type="text"
                                      value={
                                        showApiKey === service.name
                                          ? service.apiKey
                                          : "••••••••••••"
                                      }
                                      className="flex-1 text-sm bg-transparent border-0 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                                      readOnly
                                    />
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      className="shrink-0 px-2 h-8"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        setShowApiKey(
                                          showApiKey === service.name
                                            ? null
                                            : service.name
                                        );
                                      }}
                                    >
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
              </div>
            </div>

            {/* PDF 预览区域 - 仅在 PDF 文件时显示 */}
            {file?.type === "application/pdf" && pdfUrl && (
              <div className="col-span-7 h-full transition-opacity duration-300">
                <div className="flex flex-col h-full">
                  <ul className="flex flex-wrap text-sm font-medium text-center text-gray-500 dark:text-gray-400 mb-4">
                    <li className="me-2">
                      <button
                        onClick={() => setActiveTab("pdf")}
                        className={`inline-block px-4 py-3 rounded-lg ${
                          activeTab === "pdf"
                            ? "text-white bg-blue-600"
                            : "hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"
                        }`}
                      >
                        PDF 预览
                      </button>
                    </li>
                    <li className="me-2">
                      <button
                        onClick={() => setActiveTab("markdown")}
                        className={`inline-block px-4 py-3 rounded-lg ${
                          activeTab === "markdown"
                            ? "text-white bg-blue-600"
                            : convertedMarkdown
                            ? "hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 dark:hover:text-white"
                            : "text-gray-400 cursor-not-allowed"
                        }`}
                        disabled={!convertedMarkdown}
                      >
                        Markdown 预览
                      </button>
                    </li>
                  </ul>

                  <div className="flex-1 min-h-0">
                    {activeTab === "pdf" && (
                      <>
                        {pdfUrl ? (
                          <div className="h-full flex flex-col border rounded-lg overflow-hidden">
                            <div className="flex-1 min-h-0 bg-gray-100">
                              <object
                                data={pdfUrl}
                                type="application/pdf"
                                className="w-full h-full"
                              >
                                <div className="flex items-center justify-center h-full">
                                  <p>
                                    PDF 预览不可用，请使用支持 PDF 预览的浏览器
                                  </p>
                                </div>
                              </object>
                            </div>
                          </div>
                        ) : (
                          <div className="h-full flex items-center justify-center border rounded-lg bg-gray-50 text-gray-400">
                            正在加载 PDF 预览...
                          </div>
                        )}
                      </>
                    )}

                    {activeTab === "markdown" && convertedMarkdown && (
                      <div className="relative flex h-full">
                        <div className="flex-1 border rounded-lg overflow-hidden flex flex-col">
                          <div className="flex justify-end p-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleFetchImages}
                              disabled={isLoadingImages}
                              className="flex items-center gap-2"
                            >
                              {isLoadingImages ? (
                                <>
                                  <div className="animate-spin h-4 w-4 border-2 border-gray-600 border-t-transparent rounded-full" />
                                  获取中...
                                </>
                              ) : (
                                <>
                                  <Images className="h-4 w-4" />
                                  查看论文图
                                </>
                              )}
                            </Button>
                          </div>
                          <div className="flex-1 overflow-y-auto">
                            <div className="p-4">
                              <div className="prose prose-sm max-w-none dark:prose-invert">
                                <ReactMarkdown>
                                  {convertedMarkdown}
                                </ReactMarkdown>
                              </div>
                            </div>
                          </div>
                        </div>

                        {showImageList && (
                          <Drawer
                            open={showImageList}
                            onOpenChange={setShowImageList}
                            direction="right"
                          >
                            <DrawerContent className="w-[280px] h-full">
                              <DrawerHeader className="border-b border-gray-200 dark:border-neutral-700">
                                <DrawerTitle>
                                  论文图列表 ({paperImages.length})
                                </DrawerTitle>
                                <DrawerClose
                                  onClick={() => setShowImageList(false)}
                                />
                              </DrawerHeader>
                              <div className="flex-1 overflow-y-auto p-4">
                                {paperImages.length === 0 ? (
                                  <div className="text-center text-gray-500 py-8">
                                    暂无图片
                                  </div>
                                ) : (
                                  <div className="space-y-4">
                                    {paperImages.map((image, index) => (
                                      <div
                                        key={index}
                                        className="border border-gray-200 dark:border-neutral-700 rounded-lg p-4"
                                      >
                                        <div className="flex flex-col space-y-2">
                                          <p className="text-sm text-gray-500">
                                            大小：
                                            {(image.size / 1024).toFixed(2)} KB
                                          </p>
                                          <div className="flex gap-2 justify-between">
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              disabled={
                                                loadingImageId === index
                                              }
                                              onClick={async () => {
                                                try {
                                                  setLoadingImageId(index);
                                                  const imageData =
                                                    await fetchImageContent(
                                                      file?.name || "",
                                                      image.file_name
                                                    );
                                                  // 创建新的图片预览区域
                                                  const previewDiv =
                                                    document.createElement(
                                                      "div"
                                                    );
                                                  previewDiv.className = "mt-2";
                                                  const img =
                                                    document.createElement(
                                                      "img"
                                                    );
                                                  img.src = `data:${imageData.content_type};base64,${imageData.base64_content}`;
                                                  img.className =
                                                    "w-full h-auto rounded";
                                                  img.alt = image.file_name;

                                                  // 获取当前图片容器
                                                  const container =
                                                    document.getElementById(
                                                      `image-container-${index}`
                                                    );
                                                  if (container) {
                                                    // 清除之前的预览
                                                    const existingPreview =
                                                      container.querySelector(
                                                        ".mt-2"
                                                      );
                                                    if (existingPreview) {
                                                      container.removeChild(
                                                        existingPreview
                                                      );
                                                    }
                                                    // 添加新的预览
                                                    previewDiv.appendChild(img);
                                                    container.appendChild(
                                                      previewDiv
                                                    );
                                                  }
                                                } catch (error) {
                                                  setError("加载图片失败");
                                                } finally {
                                                  setLoadingImageId(null);
                                                }
                                              }}
                                            >
                                              {loadingImageId === index ? (
                                                <div className="animate-spin h-4 w-4 border-2 border-gray-600 border-t-transparent rounded-full" />
                                              ) : (
                                                <Eye className="h-2 w-2" />
                                              )}
                                            </Button>
                                            <Checkbox
                                              className="h-4 w-4"
                                              onCheckedChange={async (
                                                checked
                                              ) => {
                                                const imageName = `${
                                                  file?.name || ""
                                                }-${index + 1}`;

                                                if (checked) {
                                                  addImage({
                                                    name: imageName,
                                                    file_name: image.file_name,
                                                    size: image.size,
                                                    path: image.path,
                                                    type: "flowchart",
                                                  });

                                                  try {
                                                    const imageData =
                                                      await fetchImageContent(
                                                        file?.name || "",
                                                        image.file_name
                                                      );
                                                    usePaperImagesStore
                                                      .getState()
                                                      .updateImageContent(
                                                        imageName,
                                                        {
                                                          content_type:
                                                            imageData.content_type,
                                                          base64_content:
                                                            imageData.base64_content,
                                                        }
                                                      );
                                                  } catch (error) {
                                                    console.error(
                                                      "加载图片内容失败:",
                                                      error
                                                    );
                                                  }
                                                } else {
                                                  removeImage(imageName);
                                                }
                                              }}
                                            />
                                          </div>
                                          <div
                                            id={`image-container-${index}`}
                                          ></div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </DrawerContent>
                          </Drawer>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
