import { useState, useEffect } from "react";
import { useAuth } from "@/utils/AuthContext";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Refresh<PERSON><PERSON>,
  FileText,
  Star,
} from "lucide-react";
import { AddDigestDialog } from "./AddDigestDialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useDigestStore } from "@/store/digestStore";

interface Digest {
  id: number;
  title: string;
  source_url: string;
  markdown_content: string;
  summary: string;
  digest_type: string;
  is_public: boolean;
  rate: number;
  tags: string;
  created_at: string;
  updated_at: string;
}

export function DigestList() {
  const { user } = useAuth();
  const userId = user?.id;

  // console.log(userId)

  const [isAddDigestOpen, setIsAddDigestOpen] = useState(false);
  const [digests, setDigests] = useState<Digest[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDigests, setSelectedDigests] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // 导入 useDigestStore
    // 导入 clearDigests
      const { digests: storeDigests, addDigest, removeDigest, clearDigests } = useDigestStore();

      // 获取摘要列表
      const fetchDigests = async () => {
        setIsLoading(true);
        try {
          const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/digests/?user_id=${userId}`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          });
          if (!response.ok) throw new Error("获取摘要列表失败");
          const data = await response.json();

          // 先清空 store
          clearDigests();

          // 然后添加新数据
          data.forEach((digest: any) => {
            addDigest({
              title: digest.title,
              content: digest.summary,
              user_id: digest.user_id
            });
          });

          setDigests(data);
        } catch (err) {
          setError(err instanceof Error ? err.message : "未知错误");
        } finally {
          setIsLoading(false);
        }
      };

    // 删除摘要
    const deleteDigest = async (id: number) => {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/digests/${id}`, {
          method: "DELETE",
        });
        if (!response.ok) throw new Error("删除摘要失败");

        // 同时从 store 中删除
        removeDigest(id.toString());

        await fetchDigests(); // 重新获取列表
      } catch (err) {
        setError(err instanceof Error ? err.message : "删除失败");
      }
    };

    useEffect(() => {
      fetchDigests();
    }, [userId]); // 当用户数据更新时重新获取摘要列表

  // 过滤和分页逻辑
  const filteredDigests = digests.filter((digest) =>
    digest.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const pageCount = Math.ceil(filteredDigests.length / itemsPerPage);
  const paginatedDigests = filteredDigests.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div className="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
      <div className="flex flex-col">
        <div className="-m-1.5 overflow-x-auto">
          <div className="p-1.5 min-w-full inline-block align-middle">
            <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden dark:bg-neutral-800 dark:border-neutral-700">
              {/* Header */}
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-neutral-700">
                <div>
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-neutral-200">
                    文献摘要
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    查看所有文献的AI摘要和分析。
                  </p>
                </div>

                <div className="inline-flex gap-x-2">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="搜索摘要..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8 py-2 px-3 text-sm rounded-lg border border-gray-200 bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200"
                    />
                  </div>
                  <button
                    onClick={fetchDigests}
                    className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700"
                  >
                    <RefreshCw className="h-4 w-4" />
                    刷新
                  </button>
                  <button
                    onClick={() => setIsAddDigestOpen(true)}
                    className="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                  >
                    <Plus className="h-4 w-4" />
                    添加摘要
                  </button>
                </div>
              </div>

              {/* Table */}
              <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-700">
                <thead className="bg-gray-50 dark:bg-neutral-800">
                  <tr>
                    <th scope="col" className="ps-6 py-3 text-start">
                      <input
                        type="checkbox"
                        className="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-600 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedDigests(
                              paginatedDigests.map((d) => d.id)
                            );
                          } else {
                            setSelectedDigests([]);
                          }
                        }}
                      />
                    </th>
                    <th scope="col" className="px-6 py-3 text-start">
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        标题
                      </span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-start">
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        类型
                      </span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-start">
                      <span className="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-neutral-200">
                        评分
                      </span>
                    </th>
                    <th scope="col" className="px-6 py-3 text-end">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-neutral-700">
                  {paginatedDigests.map((digest) => (
                    <tr key={digest.id}>
                      <td className="size-px whitespace-nowrap">
                        <div className="ps-6 py-3">
                          <input
                            type="checkbox"
                            checked={selectedDigests.includes(digest.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedDigests([
                                  ...selectedDigests,
                                  digest.id,
                                ]);
                              } else {
                                setSelectedDigests(
                                  selectedDigests.filter(
                                    (id) => id !== digest.id
                                  )
                                );
                              }
                            }}
                            className="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-3">
                        <div className="flex items-center gap-2">
                          <a
                            href={digest.source_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="group flex items-center gap-2 text-gray-800 hover:text-blue-600 dark:text-neutral-200 dark:hover:text-blue-400"
                          >
                            <span className="font-medium">
                              {digest.title.length > 30
                                ? `${digest.title.slice(0, 30)}...`
                                : digest.title}
                            </span>
                            <svg
                              className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                            >
                              <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
                              <polyline points="15 3 21 3 21 9" />
                              <line x1="10" y1="14" x2="21" y2="3" />
                            </svg>
                          </a>
                        </div>
                      </td>
                      <td className="px-6 py-3">{digest.digest_type}</td>
                      <td className="px-6 py-3">
                        <div className="flex items-center gap-1">
                          {Array.from({ length: digest.rate }).map(
                            (_, index) => (
                              <Star
                                key={index}
                                className="h-4 w-4 fill-yellow-400 text-yellow-400"
                              />
                            )
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-3 text-end">
                        <div className="flex gap-2 justify-end">
                          <Popover>
                            <PopoverTrigger asChild>
                              <button
                                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                title="查看摘要"
                              >
                                <FileText className="h-4 w-4" />
                              </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[400px] p-4">
                              <div className="space-y-2">
                                <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100">
                                  {digest.title}
                                </h4>
                                <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                                  {digest.summary}
                                </p>
                              </div>
                            </PopoverContent>
                          </Popover>
                          <button
                            onClick={() => {
                              if (confirm("确定要删除这条摘要吗？")) {
                                deleteDigest(digest.id);
                              }
                            }}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              <div className="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-neutral-700">
                <div>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    显示 {paginatedDigests.length} 条，共{" "}
                    {filteredDigests.length} 条
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {Array.from({ length: pageCount }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i + 1)}
                      className={`px-3 py-1 rounded-lg ${
                        currentPage === i + 1
                          ? "bg-blue-600 text-white"
                          : "bg-gray-100 text-gray-600 dark:bg-neutral-700 dark:text-neutral-300"
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Digest Dialog */}
      <AddDigestDialog
        open={isAddDigestOpen}
        onOpenChange={setIsAddDigestOpen}
        onSuccess={fetchDigests}
        userId={userId || 1}  // 添加 userId 属性
      />
    </div>
  );
}
