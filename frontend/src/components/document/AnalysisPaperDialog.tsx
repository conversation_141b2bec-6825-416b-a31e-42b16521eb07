import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { CircleHelp } from "lucide-react";

interface AnalysisPaperDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: any | null;
}

import { useEffect, useState } from "react";
import { ChevronDown, BookOpenText } from "lucide-react";
import { cn } from "@/lib/utils";
import { ReadingSidepanel } from "./ReadingSidepanel";

export function AnalysisPaperDialog({ open, onOpenChange, document }: AnalysisPaperDialogProps) {
  if (!document) return null;

  const [sections, setSections] = useState<Array<{question: string, answer: string}>>([]);

  useEffect(() => {
    if (document.markdown_content) {
      // 按 # 分割文本获取所有章节
      const parts = document.markdown_content.split(/(?=^# )/m);
      // 过滤空内容并处理所有章节
      const validSections = parts
        .filter((part: string) => part.trim())
        .map((section: string) => {
          const lines = section.split('\n');
          const title = lines[0].replace(/^#\s+/, '').trim();
          const content = lines.slice(1).join('\n').trim();
          return {
            question: title,
            answer: content
          };
        });

      setSections(validSections);
    }
  }, [document]);

  const [expandedSections, setExpandedSections] = useState<number[]>([0]); // 默认展开第一节

  const toggleSection = (index: number) => {
    setExpandedSections(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // 添加选中状态管理
  const [checkedSections, setCheckedSections] = useState<number[]>([]);

  const toggleCheck = (index: number) => {
    setCheckedSections(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  // 添加加载状态
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // 添加处理完成状态管理
  const [completedSections, setCompletedSections] = useState<number[]>([]);

  // 添加侧边栏状态
  const [isSidepanelOpen, setIsSidepanelOpen] = useState(false);
  // 添加完成状态
  const [isCompleted, setIsCompleted] = useState(false);

  return (

    // 为整个对话框添加 Header和Footer

    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[80vw] h-[80vh] overflow-y-auto max-w-none">
        <ReadingSidepanel 
          isOpen={isSidepanelOpen}
          sections={sections}
          selectedSections={checkedSections}
          onSectionComplete={(sectionIndex) => {
            setCompletedSections(prev => [...prev, sectionIndex]);
            if (checkedSections.every(index => 
              [...completedSections, sectionIndex].includes(index)
            )) {
              setIsAnalyzing(false);
              setIsCompleted(true);
            }
          }}
        />
        
        <div className={cn(
          "transition-all duration-300 ease-in-out",
          isSidepanelOpen ? "ml-[40%]" : "ml-0"
        )}>
          <DialogHeader className="flex flex-row items-center justify-between">
            <DialogTitle className="text-2xl font-bold">论文分析</DialogTitle>
            <div className="flex justify-end mr-6">
              <button
                onClick={() => {
                  setIsAnalyzing(true);
                  setIsSidepanelOpen(true);
                  setIsCompleted(false);
                }}
                className="inline-flex items-center gap-x-2 px-4 py-2 text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                disabled={checkedSections.length === 0 || isCompleted}
              >
                {!isAnalyzing && !isCompleted && <BookOpenText className="size-4" />}
                {isAnalyzing && (
                  <span className="animate-spin inline-block size-4 border-[3px] border-current border-t-transparent text-white rounded-full" role="status" aria-label="loading"></span>
                )}
                {isCompleted ? '已完成' : isAnalyzing ? '分析中...' : '阅读'}
              </button>
            </div>
          </DialogHeader>

          {/* 其他内容保持不变 */}
        {/* Stepper */}
        <ul className="relative flex flex-row gap-x-2 mb-10 border-b pb-6 max-w-6xl mx-auto">
          {checkedSections.map((sectionIndex, step) => (
            <li key={sectionIndex} className="flex items-center gap-x-2 shrink basis-0 flex-1 group">
              <div className="min-w-7 min-h-7 inline-flex justify-center items-center text-xs align-middle">
                <span className={`size-7 flex justify-center items-center shrink-0 font-medium rounded-full
                  ${completedSections.includes(sectionIndex)
                    ? 'bg-blue-600 text-white'
                    : 'bg-white border border-gray-200 text-gray-800 dark:bg-neutral-900 dark:border-neutral-700 dark:text-white'
                  }`}>
                  {completedSections.includes(sectionIndex) ? '✓' : step + 1}
                </span>
                <span className="ms-2 block text-sm font-medium text-gray-800 dark:text-white">
                  {step === 0 ? 'Abstract' : sections[sectionIndex].question}
                </span>
              </div>
              {step < checkedSections.length - 1 && (
                <div className="w-full h-px flex-1 bg-gray-200 group-last:hidden dark:bg-neutral-700"></div>
              )}
            </li>
          ))}
        </ul>


        <div className="divide-y divide-gray-200 dark:divide-neutral-700 overflow-y-auto">
          {sections.map((item, index) => (
            <div key={index} className="py-8 first:pt-0 last:pb-0">
              <div className="flex gap-x-5">
                <div className="shrink-0 mt-1">
                  <input
                    type="checkbox"
                    checked={checkedSections.includes(index)}
                    onChange={() => toggleCheck(index)}
                    className="size-6 border-gray-300 rounded text-blue-600 focus:ring-blue-500"
                  />
                </div>
                <div className="grow">
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => toggleSection(index)}
                  >
                    <h3 className="md:text-lg font-semibold text-gray-800 dark:text-neutral-200">
                      {item.question}
                    </h3>
                    <ChevronDown 
                      className={`h-5 w-5 transition-transform ${
                        expandedSections.includes(index) ? 'rotate-180' : ''
                      }`}
                    />
                  </div>
                  {expandedSections.includes(index) && (
                    <p className="mt-4 text-gray-500 dark:text-neutral-500 whitespace-pre-line">
                      {item.answer}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
    </div>
      </DialogContent>
    </Dialog>
  );
}