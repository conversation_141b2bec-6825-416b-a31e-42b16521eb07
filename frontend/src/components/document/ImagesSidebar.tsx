import { X } from 'lucide-react';

interface Image {
  id: number;
  name: string;
  description: string;
  type: string;
  content_type: string;
  data: string;
}

interface ImagesSidebarProps {
  images: Image[];
  isOpen: boolean;
  onClose: () => void;
}

export function ImagesSidebar({ images, isOpen, onClose }: ImagesSidebarProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-96 bg-white dark:bg-neutral-800 shadow-lg transform transition-transform duration-300 ease-in-out overflow-y-auto">
      <div className="sticky top-0 z-10 bg-white dark:bg-neutral-800 border-b border-gray-200 dark:border-neutral-700 p-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">论文精选图片</h3>
        <button
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="p-4 space-y-4">
        {images.map((image) => (
          <div key={image.id} className="border border-gray-200 dark:border-neutral-700 rounded-lg p-4">
            <img
              src={`data:${image.content_type};base64,${image.data}`}
              alt={image.name}
              className="w-full h-auto rounded-lg"
            />
            <h4 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">{image.name}</h4>
            {image.description && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{image.description}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}